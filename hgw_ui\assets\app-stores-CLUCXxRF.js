import{d as e}from"./pinia-DkXT_Xot.js";import{r as o,c as t}from"./vue-vendor-D6tHD5lA.js";const r=e("user",()=>{const e=o(null),r=t(()=>null!==e.value),a=()=>{e.value=null,localStorage.removeItem("userInfo"),localStorage.removeItem("isLoggedIn"),localStorage.removeItem("apptoken")};return{userInfo:e,isLoggedIn:r,login:o=>{e.value=o,localStorage.setItem("apptoken",o.apptoken),localStorage.setItem("userInfo",JSON.stringify(o)),localStorage.setItem("isLoggedIn","true")},logout:a,initUserState:()=>{const o=localStorage.getItem("userInfo");if("true"===localStorage.getItem("isLoggedIn")&&o)try{e.value=JSON.parse(o)}catch(t){a()}}}}),a=e("freedomUser",()=>{const e=o(null),r=t(()=>null!==e.value&&""!==e.value.token),a=()=>{e.value=null,localStorage.removeItem("freedomUserInfo"),localStorage.removeItem("isFreedomLoggedIn"),localStorage.removeItem("freedomToken")},l=()=>localStorage.getItem("freedomToken")||"";return{freedomUserInfo:e,isFreedomLoggedIn:r,freedomLogin:o=>{e.value=o,localStorage.setItem("freedomToken",o.token),localStorage.setItem("freedomUserInfo",JSON.stringify(o)),localStorage.setItem("isFreedomLoggedIn","true")},freedomLogout:a,initFreedomUserState:()=>{const o=localStorage.getItem("freedomUserInfo");if("true"===localStorage.getItem("isFreedomLoggedIn")&&o)try{e.value=JSON.parse(o)}catch(t){a()}},getFreedomToken:l,checkFreedomLogin:()=>{const e=l();return"true"===localStorage.getItem("isFreedomLoggedIn")&&""!==e&&"undefined"!==e}}});export{a,r as u};
