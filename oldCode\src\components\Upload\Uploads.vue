<template>
  <div>
    <el-upload
      action="https://huigupaimai.oss-cn-beijing.aliyuncs.com"
      :data="dataObj"
      multiple
      :file-list="fileList"
      :before-upload="beforeUpload"
      :on-remove="handleRemove"
      :on-success="handleUploadSuccess"
      :on-preview="handlePreview"
    >
      <el-button size="small" type="primary">点击上传</el-button>
      <div slot="tip" class="el-upload__tip">
        {{tips}}只能上传jpg/png文件，且不超过10MB
      </div>
    </el-upload>
    <el-dialog :visible.sync="PreviewPic">
      <!-- <img width="100%" :src="fileList[0].url" alt=""> -->
    </el-dialog>
  </div>
</template>
<script>
// import { getOss } from '@/api/oss'
import ajax from '../../store/Ajax'
import { getFileName } from './gettime'
export default {
  name: 'SingleUpload',
  props: {
    tips:{
      type:String,
      default:''
    },
    fileListNew:{
      value:Array,
      default:[]
    }
  },
  data() {
    return {
      ossUrl: this.$Pc.ossUrl,
      dataObj: {
        policy: '',
        signature: '',
        key: '',
        ossaccessKeyId: '',
        dir: '',
        host: ''
        // callback:'',
      },
      PreviewPic: false,
      // fileList: []
    }
  },
  computed: {
    fileList(){
      console.log(this.fileListNew,'fileListNew')
      if(this.fileListNew)
      return this.fileListNew
      else return []
    }
    // imageUrl() {
    //   return this.value
    // },
    // imageName() {
    //   if (this.value != null && this.value !== '') {
    //     return this.value.substr(this.value.lastIndexOf('/') + 1)
    //   } else {
    //     return null
    //   }
    // },
    // fileList() {
    //   return [
    //     {
    //       name: this.imageName,
    //       url: this.imageUrl,
    //     },
    //   ];
    // },
    // showFileList: {
    //   get: function () {
    //     return (
    //       this.value !== null && this.value !== "" && this.value !== undefined
    //     );
    //   },
    //   set: function (newValue) {},
    // },
  },
  methods: {
    emitInput(val) {
      this.$emit('upload', val)
    },
    handleRemove(file, fileList) {
      // this.fileList = fileList
      this.emitInput(fileList)
      // this.emitInput('')
    },
    handlePreview(file) {
      window.open(file.url, "_blank");
      // this.PreviewPic = true
    },
    beforeUpload(file) {
      const _self = this
      return new Promise((resolve, reject) => {
        ajax.aliosspolicy()
          .then((response) => {
            // const isJPG = file.type === 'image/jpeg'
            // const isGIF = file.type === 'image/gif'
            // const isPNG = file.type === 'image/png'
            // const isBMP = file.type === 'image/bmp'
            // if (!isJPG && !isGIF && !isPNG && !isBMP) {
            //   this.$message({
            //     type: 'error',
            //     message: '上传图片格式文件，必须是JPG/GIF/PNG/BMP 格式!'
            //   })
            //   reject('error')
            // }
            var fileExtension = file.name.substring(
              file.name.lastIndexOf('.') + 1
            )
            _self.dataObj.policy = response.data.policy
            _self.dataObj.signature = response.data.signature
            _self.dataObj.ossaccessKeyId = response.data.accessid
            _self.dataObj.key =
              response.data.dir + getFileName() + '.' + fileExtension
            _self.dataObj.dir = response.data.dir
            _self.dataObj.host = response.data.host
            resolve(true)
          })
          .catch((err) => {
            console.log(err)
            reject(false)
          })
      })
    },
    handleUploadSuccess(res, file) {
      // this.fileList = []
      this.fileList.push({
        name: file.name,
        key:this.dataObj.key,
        url: this.ossUrl + this.dataObj.key
      })
      // this.fileList.pop();
      // this.fileList.push({
      //   name: file.name,
      //   url:
      //     this.ossUrl +
      //     "/" +
      //     this.dataObj.key,
      // });
      console.log(this.fileList)
      console.log(this.dataObj.key)
      this.emitInput(this.fileList)
    }
  }
}
</script>
<style>
</style>
