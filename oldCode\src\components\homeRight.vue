<template>
	<!-- 暂时隐藏该组件 -->
	<div v-if="false">
		<div class="homeRight">
			<div v-if="routerId" class="homeRight_right homeRight_rights homeRight_right_bottom"
				@mousemove="isShowRight = true" @mouseleave="isShowRight = false">
				<div class="homeRight_right_left">
					<!-- <img class="homeRight_right_one" src="../assets/home/<USER>/four.png"> -->
					<img class="homeRight_right_two" src="../assets/home/<USER>/four1.png">
				</div>
				<div>分享</div>
				<div v-if="isShowRight" class="homeRight_right_three"
					style="top: 0px;right:60px;width: 220px;height: 220px;display: block;">
					<qrcode class="qrcode" :url="datas.url" :iconurl="datas.icon" :wid="200" :hei="200" :imgwid="66"
						:imghei="66"></qrcode>
					<h6>扫一扫分享</h6>
				</div>
			</div>
			<div class="homeRight_right homeRight_right_bottom" @click="enterClick">
				<div class="homeRight_right_left">
					<!-- <img class="homeRight_right_one" src="../assets/home/<USER>/one.png"> -->
					<img class="homeRight_right_two" src="../assets/home/<USER>/one1.png">
				</div>
				<div>入驻</div>
			</div>
					<!-- <img class="homeRight_right_one" src="../assets/home/<USER>/two.png"> -->
			<div class="homeRight_right homeRight_right_bottom" @click="centerDialogVisible = true">
				<div class="homeRight_right_left">
					<img class="homeRight_right_two" src="../assets/home/<USER>/two1.png">
				</div>
				<div>客服</div>
			</div>
			<div class="homeRight_right homeRight_rights homeRight_right_bottom">
				<div class="homeRight_right_left">
					<!-- <img class="homeRight_right_one" src="../assets/home/<USER>/four.png"> -->
					<img class="homeRight_right_two" src="../assets/home/<USER>/four1.png">
				</div>
				<div>手机端</div>
				<div class="homeRight_right_three">
					<img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/images/appxiazai.png">
					<h6>下载APP</h6>
				</div>
			</div>
			<a href="https://www.hghello.com/#/news?cateid=1&id=209" target="_blank"
				class="homeRight_right homeRight_right_bottom">
				<div class="homeRight_right_left">
					<!-- <img class="homeRight_right_one" src="../assets/home/<USER>/two.png"> -->
					<img class="homeRight_right_two" src="../assets/home/<USER>/lianxi.png">
				</div>
				<div style="font-size:13px;margin-top:3px">扫码关注</div>
			</a>
			<div class="homeRight_right homeRight_right_bottom" @click="backTop">
				<div class="homeRight_right_left">
					<!-- <img class="homeRight_right_one" src="../assets/home/<USER>/three.png"> -->
					<img class="homeRight_right_two" src="../assets/home/<USER>/three1.png">
				</div>
				<div>顶部</div>
			</div>
		</div>
		<el-dialog
			:visible.sync="centerDialogVisible"
			width="30%"
			center>
			<div>
				<img style="width:100%" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/qiyekefu.png" alt="" srcset="">
			</div>
		</el-dialog>
	</div>
</template>

<script>
	// import QRCode from 'qrcodejs2'
	import qrcode from 'vue_qrcodes'
	export default {
		props: ['routerId'],
		components: {
			qrcode
		},
		data() {
			return {
				datas: {
					url: '',
					icon: 'https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/pengyoulogo.jpg'
				},
				isShowRight: false,
				isShouye: true,
				centerDialogVisible:false
			}
		},
		mounted() {
			// this.creatQrCode()
			if (this.$route.path == '/') {
				this.datas.url = 'https://www.hghello.com/h5'
			}
		},
		methods: {
			creatQrCode() {
				if (this.routerId)
					this.isShowRight = true
				this.datas.url = 'https://www.hghello.com/h5/#/pages/details/details?list=' + this.routerId
			},
			backTop() { // 动画
				let timer = null
				timer = setInterval(function() {
					//获取滚动条到顶部的位置
					let osTop = document.documentElement.scrollTop || document.body.scrollTop
					let ispeed = Math.floor(-osTop / 5)
					// 判断到顶部的等式：滚动条到顶部的位置 + 当前窗口内容可视区 == 滚动条的总高度
					document.documentElement.scrollTop = document.body.scrollTop = osTop + ispeed
					//this.isTop = true
					if (osTop === 0) {
						clearInterval(timer)
						timer = null
					}
				}, 10)
			},
			enterClick() {
				this.$router.push({ //核心语句
					path: '/enterqiye', //跳转的路径
				})
			}
		}
	}
</script>

<style>
	.homeRight {
		width: 56px;
		min-height: 170px;
		max-height: 278px;
		position: fixed;
		right: -10px;
		top: 45%;
		transform: translateX(-50%);
    	/* border: 1px solid #EEEEEE; */
		z-index: 22;
	}

	.homeRight_right {
		position: relative;
		display: flex;
		-js-display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 56px;
		height: 56px;
		background: rgba(168, 0, 18, .1);
		margin-bottom: 16px;
		border-radius: 6px;
		cursor: pointer;
		opacity: 1 !important;
	}

	a {
		text-decoration: none;
	}

	.homeRight_right_bottom {
		border: 1px solid #A80012;
	}

	.homeRight_right_left {
		width: 16px;
		height: 16px;
		position: relative;
	}

	.homeRight_right_left img {
		position: absolute;
		top: 0;
		left: 0;
		width: 16px !important;
		height: 16px !important;
	}

	.homeRight_rights {
		width: 56px;
		height: 56px;
	}

	.homeRight_rights:hover .homeRight_right_three {
		display: block;
		opacity: 1 !important;
	}

	.homeRight_right_three {
		width: 132px;
		height: 144px;
		background: #FFFFFF;
    	border: 1px solid #eee;
		position: absolute;
		right: 58px;
		top: 0px;
		display: flex;
		-js-display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		font-weight: 400;
		color: #777777 !important;
		display: none;
	}

	.homeRight_right_three h6 {
		margin: 0;
	}

	.homeRight_right_three img {
		margin-top: 10px;
		width: 102px !important;
		height: 102px !important;
	}

	.homeRight_right div {
		font-size: 14px;
		font-weight: 400;
		color: #d7363b;
		text-align: center;
	}

	.homeRight_right a {
		font-size: 14px;
		font-weight: 400;
		color: #d7363b;
		text-align: center;
	}

	.homeRight_right img {
		width: 16px;
		height: 16px;
	}

	/* .homeRight_right:hover .homeRight_right_one {
		display: none;
		opacity: 0 !important;
	} */

	.homeRight_right:hover .homeRight_right_two {
		display: block;
		opacity: 1 !important;
	}

	/* .homeRight_right_two {
		display: none;
		opacity: 0 !important;
	} */

	.qrcode {
		display: inline-block;
	}

	.qrcode img {
		width: 180px !important;
		height: 180px !important;
		background-color: #fff;
		padding: 6px;
		box-sizing: border-box;
	}

	.qrcode .logoimg {
		width: 66px !important;
		height: 66px !important;
	}
</style>
