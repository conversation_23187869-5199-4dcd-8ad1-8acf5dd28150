module.exports = {
	publicPath: './', // 启动页地址
	outputDir: 'dist', // 打包的目录
	lintOnSave: true, // 在保存时校验格式
	productionSourceMap: false, // 生产环境是否生成 SourceMap
	devServer: {
		open: true, // 启动服务后是否打开浏览器
		host: '0.0.0.0',
		port: 8080, // 服务端口
		https: false,
		hotOnly: false,
		// 在这里设置代理服务器.
		proxy: {
			// 对象中键是本地请求地址，值是代理服务器配置对象
			// 注意：vue/cli已经安装并配置了代理模块，可以直接用
			"/api": {
				// target: "https://mai-api.hghello.com/",
				target: "http://************:80",
				changeOrigin: true,
				pathRewrite: {
					"^/api": ""
				},

			}
		},
		before: app => {}
	},
	chainWebpack: (config) => {
		config.entry('main').add('babel-polyfill');
	},
	transpileDependencies: [
		'biyi-admin' // 指定对第三方组件也进行babel-polyfill处理
	],
	// chainWebpack: config => {
	// 	config.entry.app = ["babel-polyfill", resolve('src/main.js')],
	// 		config.resolve.alias
	// 		.set('@', resolve('src'))
	// 		.set('./@assets', resolve('src/assets'))
	// 		.set('@components', resolve('src/components'))
	// 		.set('@store', resolve('src/store'))
	// 		.set('@utils', resolve('src/utils'))
	// 		.set('@serviceSupervision', resolve('src/components/pages/serviceSupervision'))
	// 		.set('@onlineRevision', resolve('src/components/pages/onlineRevision'))
	// 		.set('@evaluate', resolve('src/components/pages/evaluate'))
	// 		.set('@TablesAndChairs', resolve('src/components/pages/TablesAndChairs'))
	// 		.set('@font', resolve('src/font'));
	// }
}
