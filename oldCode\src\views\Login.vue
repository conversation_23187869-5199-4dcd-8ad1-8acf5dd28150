<template>
	<div class="min_wrapper_1500" style="background-color: #F7F7F7;">
		<Headers @setIsReject='setIsReject' />
		<div class="loginPage">
			<div class="loginPages">
				<div class="loginPage_left">
					<div class="loginPage_left_one">
						<div class="loginPage_left_view">
							<!-- <img class="" :src="url + 'uploads/xiaochengxu.jpg'" /> -->
							<!-- <img src="@/assets/images/index/gongzhonghao.png" /> -->
							<img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/gzhao.jpg" alt="" srcset="">
							<div>关注公众号</div>
						</div>
						<div class="loginPage_left_view">
							<!-- <img class="" :src="url + 'uploads/xiaochengxu.jpg'" /> -->
							<!-- <img src="@/assets/images/index/gongzhonghao.png" /> -->
							<img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/images/appxiazai.png" alt="" srcset="">
							<div>扫码下载APP</div>
						</div>
						<div class="loginPage_left_view">
							<!-- <img class="" :src="url + 'uploads/xiaochengxu.jpg'" /> -->
							<!-- <img src="@/assets/images/index/gongzhonghao.png" /> -->
							<img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/xiaochengxu.jpg" alt="" srcset="">
							<div>打开小程序</div>
						</div>
					</div>
					<img class="loginPage_left_two" src="../assets/login/text.png">
				</div>
				<div v-if="isReject" class="loginPage_right_r">
					<div class="loginPage_right_r_one">用户注册</div>
					<div class="loginPage_right_r_two">
						<img src="../assets/login/user.png">
						<input class="logintext" v-model="mobile" type="text" value="" placeholder="请输入手机号" />
					</div>
					<div class="loginPage_right_r_two">
						<img src="../assets/login/phone.png">
						<input class="logintext" v-model="yzcode" type="text" value="" placeholder="请输入验证码" />
						<div class="loginPage_right_r_two_btn" @click="hqyzm" v-if="yz_hd">发送验证码</div>
						<djs class="loginPage_right_r_two_btn" v-if="!yz_hd" :djss="yzm_zj_cs" @getdata='js_yzm_fh'>
						</djs>
					</div>
					<div class="loginPage_right_r_two">
						<img src="../assets/login/psw.png">
						<input class="logintext" v-model="password" type="password" value="" placeholder="请输入密码" />
					</div>
					<div class="loginPage_right_r_two">
						<img src="../assets/login/psw.png">
						<input v-model="passwords" type="password" value="" placeholder="请再次输入密码" />
					</div>
					<div class="loginPage_right_r_btn" @click="rejectClick">
						立即注册
					</div>
					<div class="gouxuan">
						<div :class="!isdui?'gouxuan_one':'gouxuan_ones'" @click="isdui = !isdui">
							<img v-if="isdui" style="width: 10px;height: 8px;" src="../assets/login/dui.png">
						</div>
						<div class="gouxuan_two">注册即代表您同意 <span @click="getXieyi('160')">平台服务协议</span> 和 <span  @click="getXieyi('161')">隐私政策</span></div>
					</div>
					<div class="loginPage_right_r_bottom">
						已有账号？<span @click="isReject = false">立即登录>></span>
					</div>
				</div>
				<div v-if="isPsw" class="loginPage_right_r">
					<div class="loginPage_right_r_one">忘记密码</div>
					<div class="loginPage_right_r_two">
						<img src="../assets/login/user.png">
						<input class="logintext" v-model="mobile" type="text" value="" placeholder="请输入手机号" />
					</div>
					<div class="loginPage_right_r_two">
						<img src="../assets/login/phone.png">
						<input class="logintext" v-model="yzcode" type="text" value="" placeholder="请输入验证码" />
						<div class="loginPage_right_r_two_btn" @click="hqyzm" v-if="yz_hd">发送验证码</div>
						<djs class="loginPage_right_r_two_btn" v-if="!yz_hd" :djss="yzm_zj_cs" @getdata='js_yzm_fh'>
						</djs>
					</div>
					<div class="loginPage_right_r_two">
						<img src="../assets/login/psw.png">
						<input class="logintext" v-model="password" type="password" value="" placeholder="请输入密码" />
					</div>
					<div class="loginPage_right_r_two">
						<img src="../assets/login/psw.png">
						<input class="logintext" v-model="passwords" type="password" value="" placeholder="请再次输入密码" />
					</div>
					<div class="loginPage_right_r_btn" @click="wangjiClick">
						重置密码
					</div>
					<div class="loginPage_right_r_bottom">
						已重置密码？<span @click="isReject = false,isPsw=false">登录>></span>
					</div>
				</div>
				<div v-if="!isReject && !isPsw" class="loginPage_right_psw">
					<div class="loginPage_right_psw_one">
						<div @click="isPassword = true"
							:class="isPassword?'loginPage_right_psw_onels':'loginPage_right_psw_onel'">密码登录</div>
						<div @click="isPassword = false"
							:class="!isPassword?'loginPage_right_psw_onels':'loginPage_right_psw_onel'">短信登录</div>
						<div :class="isPassword?'loginPage_right_psw_onehengs':'loginPage_right_psw_oneheng'"></div>
					</div>
					<div v-if="isPassword ">
						<div class="loginPage_right_r_two">
							<img src="../assets/login/user.png">
							<input class="logintext" v-model="accent" type="text" value="" placeholder="请输入手机号" />
						</div>
						<div class="loginPage_right_r_two">
							<img src="../assets/login/psw.png">
							<input class="logintext" v-model="psw" type="password" value="" placeholder="请输入密码" />
						</div>
						<div class="loginPage_right_r_twoforget" @click="isPsw=true">忘记密码？</div>
						<div class="loginPage_right_r_btn" @click="login">
							立即登录
						</div>
					</div>
					<div v-if="!isPassword">
						<div class="loginPage_right_r_two">
							<img src="../assets/login/user.png">
							<input class="logintext" v-model="mobile" type="text" value="" placeholder="请输入手机号" />
						</div>
						<div class="loginPage_right_r_two">
							<img src="../assets/login/phone.png">
							<input class="logintext" v-model="yzcode" type="text" value="" placeholder="请输入验证码" />
							<div class="loginPage_right_r_two_btn" @click="hqyzm" v-if="yz_hd">发送验证码</div>
							<djs class="loginPage_right_r_two_btn" v-if="!yz_hd" :djss="yzm_zj_cs" @getdata='js_yzm_fh'>
							</djs>
						</div>
						<div class="loginPage_right_r_twoforgets">验证即登录，未注册将自动创建账号</div>
						<div class="loginPage_right_r_btn" @click="codeLogin">
							立即登录
						</div>
						<!-- <div class="gouxuan">
							<div :class="!isdui?'gouxuan_one':'gouxuan_ones'" @click="isdui = !isdui">
								<img v-if="isdui" style="width: 10px;height: 8px;" src="../assets/login/dui.png">
							</div>
							<div  class="gouxuan_two">注册即代表您同意 <span>平台服务协议</span> 和 <span>隐私政策</span></div>
						</div> -->
					</div>
					<div class="loginPage_right_psw_one_bottom">
						<div>
							<img src="../assets/login/wx.png">
							<div @click="weikaifang()">微信登录</div>
						</div>
						<div @click="isReject = true" class="loginPage_right_psw_one_bottom_right">
							<img src="../assets/login/gengduo.png">
							<div>立即注册</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		
		<el-dialog :title="xieyi.title" :visible.sync="dialogVisible" width="50%">
			<div >
				<font v-html="xieyi.content">
				</font>
			</div>
		</el-dialog>
		
		
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import djs from '@/components/daojishi.vue'
	import ajax from '../store/Ajax'
	export default {
		name: 'Home',
		components: {
			Headers,
			djs
		},
		data() {
			return {
				isPsw: false,
				dialogVisible: false,
				url: this.$Pc.ossUrl,
				isReject: true,
				isPassword: true,
				isdui: false,
				yz_hd: true, //验证码切换标签
				yzm_zj_cs: [{
					cs: true
				}], //控制验证码传递
				mobile: '',
				password: '',
				yzcode: '',
				passwords: '',
				accent: '',
				psw: '',
				xieyi:{
					title:'',
					content:''
				}
			}
		},
		created() {
			console.log(this.$route)
			if (this.$route.query) {
				if (this.$route.query.isReject) {
					this.isReject = false
				}
			}
		},
		watch: {
			isReject() {
				this.mobile = ''
				this.yzcode = ''
			}
		},
		methods: {
			js_yzm_fh: function(hd) {
				//验证码回调
				if (hd) {
					this.yz_hd = true;
				} else {
					console.log(hd);
				}
			},
			getXieyi(id) {
				ajax.newsinfo({
					id: id
				}).then(res => {
					this.dialogVisible = true
					this.xieyi = res.data;
				})
			},
			hqyzm() {
				// 导航轮播图
				if (!this.mobile) return this.$message.error('请填写手机号码')
				if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.mobile))  return this.$message.error('请输入正确的手机号码')
				ajax.LoginPress({
					mobile: this.mobile
				}).then(res => {
					if (res.code == 1) {
						this.yz_hd = false;
						this.$set(this.yzm_zj_cs, 'cs', false);
					}else {
						this.$message.error(res.msg)
					}
				}).catch(err => {

				})
			},
			weikaifang(){
				this.$message.success('暂未开放')
			},
			setIsReject(item) {
				this.isReject = item
			},
			rejectClick() {
				if (!this.isdui) return this.$message.error('请勾选平台协议和隐私政策')
				if (this.password == this.passwords) {
					ajax.register({
						mobile: this.mobile,
						password: this.password,
						yzcode: this.yzcode,
					}).then(res => {
						if (res.code == 1) {
							// window.alert('注册成功')
							this.$message.success('注册成功')
							setTimeout(() => {
								this.$router.push('login?isReject=false')
							}, 1000)
							
						}else{
							this.$message.error(res.msg)
						}
					}).catch(err => {

					})
				} else {
					this.$message.error('两次输入的密码不一致')
					// window.alert('')
				}
			},
			wangjiClick() {
				if (this.password == this.passwords) {
					ajax.zhaohuipwd({
						mobile: this.mobile,
						password: this.password,
						yzcode: this.yzcode,
					}).then(res => {
						if (res.code == 1) {
							this.$message.success('重置成功')
						}else{
							this.$message.error(res.msg)
						}
					}).catch(err => {
					})
				} else {
					this.$message.error('两次输入的密码不一致')
				}
			},
			login() {
				
				ajax.Login({
					mobile: this.accent,
					password: this.psw,
				}).then(res => {
					if (res.code == 1) {
						this.$message.success('登录成功')
						// window.alert('登录成功')
						localStorage.setItem('apptoken', res.data.apptoken);
						localStorage.setItem('userInfo', JSON.stringify(res.data));
						setTimeout(() => {
							this.$router.push('/')
						}, 150)
					} else {
						this.$message.error(res.msg)
					}
				}).catch(err => {

				})
			},
			codeLogin() {
				// if (!this.isdui) return this.$message.error('请勾选平台协议和隐私政策')
				ajax.dx_login({
					mobile: this.mobile,
					yzcode: this.yzcode,
				}).then(res => {
					console.log(res)
					if (res.code == 1) {
						this.$message.success('登录成功')
						// window.alert('登录成功')
						localStorage.setItem('apptoken', res.data.apptoken);
						localStorage.setItem('userInfo', JSON.stringify(res.data));
						setTimeout(() => {
							this.$router.push('/')
						}, 150)
					} else {
						this.$message.error(res.msg)
					}
				}).catch(err => {

				})
			}
		}
	}
</script>

<style type="text/css" scoped="scoped">
	.loginPage {
		min-width: 100%;
		max-width: 1920px;
		height: 844px;
		background: url(https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/bg.jpg) no-repeat center center;
		padding-top: 140px;
		box-sizing: border-box;
	}
	.logintext{
		height: 36px;
		line-height: 36px;
		font-size: 16px;
		width: 169px;
	}
	.loginPages {
		width: 1200px;
		margin: 0 auto;
		display: flex;
		-js-display: flex;
		justify-content: space-around;
	}

	.loginPage_left {
		/* height: 500px; */
		display: flex;
		-js-display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-around;
	}

	.loginPage_left_one {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-around;
		width: 534px;
	}

	.loginPage_left_view {
		font-size: 16px;
		font-weight: 400;
		color: #333333;
		display: flex;
		-js-display: flex;
		flex-direction: column;
		align-items: center;
	}

	.loginPage_left_view img {
		width: 120px;
		height: 120px;
		margin-bottom: 10px;
	}

	.loginPage_left_two {
		width: 534px;
		height: 40px;
	}

	.loginPage_right_r {
		width: 352px;
		height: 500px;
		background: #FFFFFF;
		position: relative;
	}

	.loginPage_right_r_one {
		font-size: 18px;
		font-weight: 500;
		color: #D6363B;
		padding-top: 39px;
		box-sizing: border-box;
		text-align: center;
		margin-bottom: 22px;
	}

	.loginPage_right_r_two {
		width: 280px;
		height: 50px;
		background: #FCFCFC;
		border: 1px solid #E6E6E6;
		margin: 0 auto 12px;
		display: flex;
		-js-display: flex;
		align-items: center;
		padding: 0 20px;
		box-sizing: border-box;
	}

	.loginPage_right_r_two_btn {
		font-size: 12px;
		font-weight: 600;
		color: #D6363B;
		flex-shrink: 0;
		cursor: pointer;
	}

	.loginPage_right_r_two:hover {
		border: 1px solid #D6363B;
	}

	.loginPage_right_r_two img {
		width: 12px;
		height: 14px;
	}

	.loginPage_right_r_two input {
		outline: none;
		border: none;
		margin-left: 10px;
	}

	.loginPage_right_r_btn {
		width: 280px;
		height: 48px;
		background: #D6363B;
		text-align: center;
		line-height: 48px;
		font-size: 16px;
		font-weight: 400;
		color: #FFFFFF;
		margin: 22px auto 0;
		cursor: pointer;
	}

	.gouxuan {
		width: 280px;
		display: flex;
		-js-display: flex;
		align-items: center;
		font-size: 12px;
		font-weight: 300;
		color: #999999;
		margin: 17px auto 0;
	}

	.gouxuan_one {
		width: 14px;
		height: 14px;
		background: #FFFFFF;
		border: 1px solid #E6E6E6;
		margin-right: 5px;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.gouxuan_ones {
		width: 14px;
		height: 14px;
		background: #D6363B;
		border: 1px solid #D6363B;
		margin-right: 5px;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
	}

	.gouxuan_two {
		color: #999999;
		font-weight: 700 !important;
	}

	.gouxuan_two span {
		color: #D6363B;
		cursor: pointer;
	}

	.loginPage_right_r_bottom {
		width: 352px;
		height: 50px;
		background: #FCFCFC;
		border: 1px solid #F0F0F0;
		position: absolute;
		bottom: 0;
		text-align: center;
		line-height: 50px;
		font-size: 14px;
		font-weight: 700;
		color: #5A5A5A;
	}

	.loginPage_right_r_bottom span {
		color: #D6363B;
		font-weight: 700;
		cursor: pointer;
	}

	.loginPage_right_psw {
		width: 352px;
		height: 389px;
		background: #FFFFFF;
		position: relative;
	}

	.loginPage_right_psw_one {
		width: 280px;
		height: 100px;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		margin: 0 auto;
	}

	.loginPage_right_psw_onel {
		width: 72px;
		height: 17px;
		font-size: 18px;
		font-weight: 500;
		color: #5A5A5A;
		cursor: pointer;
		width: 140px;
		text-align: center;
	}

	.loginPage_right_psw_onels {
		width: 72px;
		height: 17px;
		font-size: 18px;
		font-weight: 500;
		color: #D6363B;
		width: 140px;
		text-align: center;
	}

	.loginPage_right_psw_onehengs {
		width: 24px;
		height: 2px;
		background: #D6363B;
		position: absolute;
		top: 75px;
		left: 58px;
		transition: all 0.3s;
	}

	.loginPage_right_psw_oneheng {
		width: 24px;
		height: 2px;
		background: #D6363B;
		position: absolute;
		top: 75px;
		left: 198px;
		transition: all 0.3s;
	}

	.loginPage_right_r_twoforget {
		width: 280px;
		text-align: right;
		font-size: 14px;
		font-weight: 300;
		color: #777777;
		margin: 0 auto;
	}

	.loginPage_right_r_twoforgets {
		text-align: center;
		font-size: 14px;
		font-weight: 600;
		color: #5A5A5A;
	}

	.loginPage_right_psw_one_bottom {
		width: 352px;
		height: 50px;
		background: #FCFCFC;
		border: 1px solid #F0F0F0;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-around;
		font-size: 14px;
		font-weight: 600;
		color: #333333;
		position: absolute;
		bottom: 0;
	}

	.loginPage_right_psw_one_bottom img {
		width: 20px;
		height: 20px;
	}

	.loginPage_right_psw_one_bottom>div {
		display: flex;
		-js-display: flex;
		align-items: center;
		cursor: pointer;
	}

	.loginPage_right_psw_one_bottom_right {
		font-size: 14px;
		font-weight: 600;
		color: #D6363B;
	}
</style>
