<template>
  <div class="tiptap-editor">
    <div v-if="editor" class="editor-toolbar">
      <!-- 字体大小 -->
      <el-select
        v-model="currentFontSize"
        placeholder="字号"
        size="small"
        style="width: 80px"
        @change="setFontSize"
      >
        <el-option label="12px" value="12px" />
        <el-option label="14px" value="14px" />
        <el-option label="16px" value="16px" />
        <el-option label="18px" value="18px" />
        <el-option label="20px" value="20px" />
        <el-option label="24px" value="24px" />
        <el-option label="28px" value="28px" />
        <el-option label="32px" value="32px" />
      </el-select>

      <div class="toolbar-divider"></div>

      <!-- 粗体 -->
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive('bold') }"
        @click="editor.chain().focus().toggleBold().run()"
        title="粗体"
      >
        <strong>B</strong>
      </button>

      <!-- 斜体 -->
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive('italic') }"
        @click="editor.chain().focus().toggleItalic().run()"
        title="斜体"
      >
        <em>I</em>
      </button>

      <!-- 下划线 -->
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive('underline') }"
        @click="editor.chain().focus().toggleUnderline().run()"
        title="下划线"
      >
        <u>U</u>
      </button>

      <div class="toolbar-divider"></div>

      <!-- 文字颜色 -->
      <el-color-picker
        v-model="currentTextColor"
        size="small"
        @change="setTextColor"
        title="文字颜色"
      />

      <div class="toolbar-divider"></div>

      <!-- 对齐方式 -->
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive({ textAlign: 'left' }) }"
        @click="editor.chain().focus().setTextAlign('left').run()"
        title="左对齐"
      >
        ≡
      </button>
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive({ textAlign: 'center' }) }"
        @click="editor.chain().focus().setTextAlign('center').run()"
        title="居中对齐"
      >
        ≣
      </button>
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive({ textAlign: 'right' }) }"
        @click="editor.chain().focus().setTextAlign('right').run()"
        title="右对齐"
      >
        ≡
      </button>

      <div class="toolbar-divider"></div>

      <!-- 列表 -->
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive('bulletList') }"
        @click="editor.chain().focus().toggleBulletList().run()"
        title="无序列表"
      >
        • 列表
      </button>
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive('orderedList') }"
        @click="editor.chain().focus().toggleOrderedList().run()"
        title="有序列表"
      >
        1. 列表
      </button>

      <div class="toolbar-divider"></div>

      <!-- 清除格式 -->
      <button
        type="button"
        class="toolbar-btn"
        @click="editor.chain().focus().clearNodes().unsetAllMarks().run()"
        title="清除格式"
      >
        清除
      </button>
    </div>
    <div class="editor-content">
      <EditorContent :editor="editor" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Underline from '@tiptap/extension-underline'
import TextAlign from '@tiptap/extension-text-align'
import Color from '@tiptap/extension-color'
import TextStyle from '@tiptap/extension-text-style'
import FontFamily from '@tiptap/extension-font-family'
import ListItem from '@tiptap/extension-list-item'
import BulletList from '@tiptap/extension-bullet-list'
import OrderedList from '@tiptap/extension-ordered-list'

interface Props {
  modelValue?: string
  placeholder?: string
  autoFocus?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  autoFocus: false
})

const emit = defineEmits<Emits>()

const editor = ref<Editor>()
const currentFontSize = ref('14px')
const currentTextColor = ref('#000000')

// 设置字体大小
const setFontSize = (size: string) => {
  if (editor.value) {
    editor.value.chain().focus().setFontSize(size).run()
  }
}

// 设置文字颜色
const setTextColor = (color: string) => {
  if (editor.value && color) {
    editor.value.chain().focus().setColor(color).run()
  }
}

onMounted(() => {
  editor.value = new Editor({
    extensions: [
      StarterKit.configure({
        bulletList: false,
        orderedList: false,
        listItem: false,
      }),
      Underline,
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Color.configure({ types: [TextStyle.name, ListItem.name] }),
      TextStyle,
      FontFamily.configure({
        types: [TextStyle.name, ListItem.name],
      }),
      ListItem,
      BulletList.configure({
        HTMLAttributes: {
          class: 'my-custom-bullet-list',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'my-custom-ordered-list',
        },
      }),
    ],
    content: props.modelValue,
    autofocus: props.autoFocus,
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
        'data-placeholder': props.placeholder
      }
    },
    onUpdate: ({ editor }) => {
      emit('update:modelValue', editor.getHTML())
    }
  })
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue, false)
  }
})
</script>

<style scoped lang="scss">
.tiptap-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  background: #ffffff;

  &:focus-within {
    border-color: #004C66;
    box-shadow: 0 0 0 2px rgba(0, 76, 102, 0.1);
  }
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e5e5;
  background: #fafafa;
  flex-wrap: wrap;

  .toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 10px;
    border: 1px solid #d9d9d9;
    background: #ffffff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #666666;
    transition: all 0.2s ease;
    min-width: 32px;
    height: 32px;

    &:hover {
      background: #f0f0f0;
      border-color: #004C66;
      color: #004C66;
    }

    &.is-active {
      background: #004C66;
      border-color: #004C66;
      color: #ffffff;
    }
  }

  .toolbar-divider {
    width: 1px;
    height: 24px;
    background: #e5e5e5;
    margin: 0 4px;
  }

  :deep(.el-select) {
    .el-input__wrapper {
      height: 32px;
    }
  }

  :deep(.el-color-picker) {
    .el-color-picker__trigger {
      width: 32px;
      height: 32px;
      border: 1px solid #d9d9d9;
      border-radius: 4px;
    }
  }
}

.editor-content {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;

  :deep(.ProseMirror) {
    padding: 16px;
    outline: none;
    min-height: 200px;
    font-size: 14px;
    line-height: 1.6;
    color: #333333;

    &[data-placeholder]:empty::before {
      content: attr(data-placeholder);
      color: #999999;
      pointer-events: none;
      position: absolute;
    }

    p {
      margin: 0 0 12px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    h1, h2, h3, h4, h5, h6 {
      margin: 16px 0 12px 0;
      font-weight: 600;

      &:first-child {
        margin-top: 0;
      }
    }

    ul, ol {
      margin: 12px 0;
      padding-left: 24px;

      li {
        margin: 6px 0;
        line-height: 1.6;
      }
    }

    ul.my-custom-bullet-list {
      list-style-type: disc;
    }

    ol.my-custom-ordered-list {
      list-style-type: decimal;
    }

    strong {
      font-weight: 600;
    }

    em {
      font-style: italic;
    }

    u {
      text-decoration: underline;
    }

    // 文本对齐样式
    &[style*="text-align: left"] {
      text-align: left;
    }

    &[style*="text-align: center"] {
      text-align: center;
    }

    &[style*="text-align: right"] {
      text-align: right;
    }

    // 颜色样式
    span[style*="color"] {
      // 保持颜色样式
    }

    // 字体大小样式
    span[style*="font-size"] {
      // 保持字体大小样式
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .editor-toolbar {
    padding: 8px 12px;
    gap: 6px;

    .toolbar-btn {
      padding: 4px 6px;
      min-width: 28px;
      height: 28px;
      font-size: 12px;
    }

    :deep(.el-select) {
      .el-input__wrapper {
        height: 28px;
      }
    }

    :deep(.el-color-picker) {
      .el-color-picker__trigger {
        width: 28px;
        height: 28px;
      }
    }
  }

  .editor-content {
    min-height: 150px;
    max-height: 300px;

    :deep(.ProseMirror) {
      padding: 12px;
      min-height: 150px;
      font-size: 13px;
    }
  }
}
</style>
