<template>
  <div class="tiptap-editor">
    <div v-if="editor" class="editor-toolbar">
      <!-- 粗体 -->
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive('bold') }"
        @click="editor.chain().focus().toggleBold().run()"
        title="粗体"
      >
        <strong>B</strong>
      </button>

      <!-- 斜体 -->
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive('italic') }"
        @click="editor.chain().focus().toggleItalic().run()"
        title="斜体"
      >
        <em>I</em>
      </button>

      <div class="toolbar-divider"></div>

      <!-- 列表 -->
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive('bulletList') }"
        @click="editor.chain().focus().toggleBulletList().run()"
        title="无序列表"
      >
        • 列表
      </button>
      <button
        type="button"
        class="toolbar-btn"
        :class="{ 'is-active': editor.isActive('orderedList') }"
        @click="editor.chain().focus().toggleOrderedList().run()"
        title="有序列表"
      >
        1. 列表
      </button>

      <div class="toolbar-divider"></div>

      <!-- 清除格式 -->
      <button
        type="button"
        class="toolbar-btn"
        @click="editor.chain().focus().clearNodes().unsetAllMarks().run()"
        title="清除格式"
      >
        清除
      </button>
    </div>
    <div class="editor-content">
      <EditorContent :editor="editor" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'
import { Editor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'

interface Props {
  modelValue?: string
  placeholder?: string
  autoFocus?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '请输入内容...',
  autoFocus: false
})

const emit = defineEmits<Emits>()

const editor = ref<Editor>()

onMounted(() => {
  editor.value = new Editor({
    extensions: [
      StarterKit
    ],
    content: props.modelValue,
    autofocus: props.autoFocus,
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
        'data-placeholder': props.placeholder
      }
    },
    onUpdate: ({ editor }) => {
      emit('update:modelValue', editor.getHTML())
    }
  })
})

onBeforeUnmount(() => {
  if (editor.value) {
    editor.value.destroy()
  }
})

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  if (editor.value && editor.value.getHTML() !== newValue) {
    editor.value.commands.setContent(newValue, false)
  }
})
</script>

<style scoped lang="scss">
.tiptap-editor {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  background: #ffffff;

  &:focus-within {
    border-color: #004C66;
    box-shadow: 0 0 0 2px rgba(0, 76, 102, 0.1);
  }
}

.editor-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-bottom: 1px solid #e5e5e5;
  background: #fafafa;
  flex-wrap: wrap;

  .toolbar-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 10px;
    border: 1px solid #d9d9d9;
    background: #ffffff;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #666666;
    transition: all 0.2s ease;
    min-width: 32px;
    height: 32px;

    &:hover {
      background: #f0f0f0;
      border-color: #004C66;
      color: #004C66;
    }

    &.is-active {
      background: #004C66;
      border-color: #004C66;
      color: #ffffff;
    }
  }

  .toolbar-divider {
    width: 1px;
    height: 24px;
    background: #e5e5e5;
    margin: 0 4px;
  }


}

.editor-content {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;

  :deep(.ProseMirror) {
    padding: 16px;
    outline: none;
    min-height: 200px;
    font-size: 14px;
    line-height: 1.6;
    color: #333333;

    &[data-placeholder]:empty::before {
      content: attr(data-placeholder);
      color: #999999;
      pointer-events: none;
      position: absolute;
    }

    p {
      margin: 0 0 12px 0;

      &:last-child {
        margin-bottom: 0;
      }
    }

    h1, h2, h3, h4, h5, h6 {
      margin: 16px 0 12px 0;
      font-weight: 600;

      &:first-child {
        margin-top: 0;
      }
    }

    ul, ol {
      margin: 12px 0;
      padding-left: 24px;

      li {
        margin: 6px 0;
        line-height: 1.6;
      }
    }

    strong {
      font-weight: 600;
    }

    em {
      font-style: italic;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .editor-toolbar {
    padding: 8px 12px;
    gap: 6px;

    .toolbar-btn {
      padding: 4px 6px;
      min-width: 28px;
      height: 28px;
      font-size: 12px;
    }


  }

  .editor-content {
    min-height: 150px;
    max-height: 300px;

    :deep(.ProseMirror) {
      padding: 12px;
      min-height: 150px;
      font-size: 13px;
    }
  }
}
</style>
