import{a as e}from"./app-assets-OmEvQhWx.js";import{h as t}from"./utils-common-PdkFOSu3.js";const i=()=>{const{province_list:t,city_list:i,county_list:s}=e.area,r=[];for(const[e,n]of Object.entries(t)){const t={value:e,label:n,children:[]};for(const[r,n]of Object.entries(i))if(r.startsWith(e.substring(0,2))){const e={value:r,label:n,children:[]};for(const[t,i]of Object.entries(s))t.startsWith(r.substring(0,4))&&e.children.push({value:t,label:i});e.children.length>0&&t.children.push(e)}t.children.length>0&&r.push(t)}return r},s=(t,i,s)=>{const{province_list:r,city_list:n,county_list:a}=e.area,o=r[t],c=n[i],p=a[s];return o&&c&&p?[o,c,p]:null},r=async e=>{try{const i=await t.getOssPolicy();if(1!==i.code)throw new Error("获取OSS签名失败");const{policy:s,signature:r,accessid:n,dir:a,host:o,expire:c}=i.data,p=`${a}${Date.now()}_${e.name}`,l=new FormData;l.append("key",p),l.append("policy",s),l.append("OSSAccessKeyId",n),l.append("host",o),l.append("expire",c),l.append("signature",r),l.append("file",e);if((await fetch("https://huigupaimai.oss-cn-beijing.aliyuncs.com/",{method:"POST",body:l})).ok)return`${p}`;throw new Error("文件上传失败")}catch(i){throw i}},n=(e,t=5242880,i=["image/gif","image/png","image/jpeg","image/jpg","image/bmp"])=>{if(e.size>t)throw new Error(`文件大小不能超过${Math.round(t/1024/1024)}MB`);if(!i.includes(e.type))throw new Error("文件类型不支持，请上传gif、png、jpg、bmp格式的图片");return!0};export{s as g,i,r as u,n as v};
