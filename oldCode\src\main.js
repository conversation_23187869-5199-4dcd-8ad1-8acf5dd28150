import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import ElementUI from 'element-ui';
import 'element-ui/lib/theme-chalk/index.css';
require('es6-promise').polyfill();
// import 'babel-polyfill';
import axios from 'axios'
import VueAxios from 'vue-axios'
import * as math from 'mathjs'

import components from './store/components'
Vue.use(components)

Vue.use(VueAxios, axios)
Vue.config.productionTip = false

let temp = {
	ossUrl: 'https://huigupaimai.oss-cn-beijing.aliyuncs.com/',
	pcUrl:'https://www.yunpaiwang.com/',
	uploadUrl:'https://huigupaimai.oss-cn-beijing.aliyuncs.com'
}

router.beforeEach((to, from, next) => {
	if (to.path) {
		if (window._hmt) {
			window._hmt.push(['_trackPageview', '/#' + to.fullPath])
		}
	}
	next()
});
Vue.prototype.$Pc = temp

Vue.prototype.$math = math

Vue.use(ElementUI);

document.title = '灰谷网-闲置资产、二手设备、废旧物资等拍卖一站式处置平台';
document.keywords = '灰谷网,废旧物资拍卖,废旧物资处理,闲置资产拍卖';
// document.description = '';


new Vue({
	router,
	store,
	render: h => h(App)
}).$mount('#app')
