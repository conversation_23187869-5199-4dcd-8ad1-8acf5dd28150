<template>
    <!-- 上传 -->
    <el-dialog title="上传缴纳凭证" :visible.sync="dialogView" width="30%" :modal-append-to-body="false" append-to-body @close="isJiaonaQuxiao" center>
        <div class="agreement_title">
            <upload :fileListNew="fileList" @upload="uploadCallbacks"></upload>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button type="success" @click="uploadSubmit" :loading="isUpload" :disabled="!zhengming">确定上传</el-button>
            <el-button type="info" @click="isJiaonaQuxiao" :loading="isUpload">取消</el-button>
        </span>
    </el-dialog>
</template>
<script>
import ajax from '../store/Ajax'
import Upload from './Upload/Upload.vue'
export default {
    components: {
        Upload
    },
    data() {
        return {
            dialogView:false,
            zhengming:'',
            fileList:[],
            isUpload:false,
            bd_id:'',
            member_id:'',
            bm_id:''
        }
    },
    methods:{
        open(row){
            this.bd_id = row.bd_id
            this.member_id = row.member_id
            this.bm_id = row.bm_id
            this.dialogView = true
        },
        uploadCallbacks(src) {
            console.log(src,'src')
            // 图片上传回调
            this.zhengming = src.map((item) => item.key).join(",");
            this.fileList = src
        },
        isJiaonaQuxiao(){
            this.dialogView = false
        },
        uploadSubmit(){
            if(!this.zhengming){
                return this.$message.error('请上传缴纳凭证！')
            }
            this.isUpload = true
            let params ={
                bd_id:this.bd_id,
                member_id:this.member_id,
                bm_id:this.bm_id,
                zhengming:this.zhengming
            }
            ajax.zhengming_bd(params).then(res => {
                if (res.code == 0) this.$message.error(res.msg)
                else {
                    this.isJiaonaQuxiao()
                    this.$message.success(res.msg)
                    this.$emit('getInfo')
                }
                this.isUpload = false
            }).catch(err => {
                this.isUpload = false
            })
        }
    }
}
</script>
<style scoped>
.uploadClick{
    margin-top: 50px;
}
</style>