<template>
	<div class="auctionFirm min_wrapper_1500">
		<Headers @getKeyWord='inputKeyword' />
		<div class="auctionFirmCon">
			<div class="auctionFirmCon_one">
				<div class="auctionFirmCon_one_left">省份首字母</div>
				<div @click="biaotiClick(item.zimu)"
					:class="item.zimu==selectIndexs?'auctionFirmCon_one_left_rights':'auctionFirmCon_one_left_right'"
					v-for="(item,i) in list">{{item.zimu}}</div>
			</div>
			<div class="auctionFirmCon_two" v-for="(item,i) in list" v-if="item.lists.length !=0" :id='item.zimu'>
				<div class="auctionFirmCon_two_title">
					{{item.zimu}}
				</div>
				<div v-for="items,ins in item.lists"
					v-if="ins == 0 || item.lists[ins].address !=item.lists[ins - 1].address  ">
					<div class="auctionFirmCon_two_two">
						<div class="auctionFirmCon_two_two_left">{{items.address|addressFilt}}</div>
						<div class="auctionFirmCon_two_two_right" @click="setzhankai(ins,i)">
							<div style="cursor: pointer;">展开{{items.address| addressFilt}}全部企业</div>
							<img src="../assets/home/<USER>">
						</div>
					</div>
					<div :class="items.iszhankai?'auctionFirmCon_two_three':'auctionFirmCon_two_threes'">
						<div v-for="(itemss,index) in items.listss" @click="goDetail(itemss.id)"
							:class="index%4==0?'auctionFirmCon_two_three_view':'auctionFirmCon_two_three_view auctionFirmCon_two_three_views'">
							<div class="auctionFirmCon_two_three_view_img">
								<img style="width: 100%;height: 100%;" :src="url + itemss.qiyelogo" @error="(e)=>e.target.src = itemss.qiyelogo ? 'https://oss.yunpaiwang.com/'+itemss.qiyelogo : ''">
							</div>
							<div class="auctionFirmCon_two_three_view_right">
								<div>{{itemss.qiyemingcheng}}</div>
								<div>共{{itemss.pmh_num}}场竞价会</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import ajax from '../store/Ajax'
	import area from '../assets/js/area.js'
	export default {
		name: 'Auctionfirm',
		components: {
			Headers,
			FootersBottom,
			homeRight
		},
		data() {
			return {
				list: [{
					zimu: 'A',
					lists: []
				}, {
					zimu: 'B',
					lists: []
				}, {
					zimu: 'C',
					lists: []
				}, {
					zimu: 'F',
					lists: []
				}, {
					zimu: 'G',
					lists: []
				}, {
					zimu: 'H',
					lists: []
				}, {
					zimu: 'J',
					lists: []
				}, {
					zimu: 'L',
					lists: []
				}, {
					zimu: 'N',
					lists: []
				}, {
					zimu: 'Q',
					lists: []
				}, {
					zimu: 'S',
					lists: []
				}, {
					zimu: 'T',
					lists: []
				}, {
					zimu: 'X',
					lists: []
				}, {
					zimu: 'Y',
					lists: []
				}, {
					zimu: 'Z',
					lists: []
				}],
				selectIndex: '',
				qiyeList: [],
				page: 0,
				keyword: '',
				url: '',
				areaList: [],
				selectIndexs: ''
			}
		},
		created() {
			this.url = this.$Pc.ossUrl
			this.getqiyeList()
			let arae = area.area.province_list
			for (var key in arae) {
				this.areaList.push({
					id: key,
					name: arae[key]
				})
			}
			window.location.hash = '/auctionfirm'
		},
		filters: {
			addressFilt(v) {
				let arae = area.area.province_list
				let temp = []
				for (var key in arae) {
					temp.push({
						id: key,
						name: arae[key]
					})
				}
				let text = ' '
				temp.forEach(el => {
					if (el.id == v) {
						text = el.name
					}
				})
				return text
			},
		},
		methods: {
			biaotiClick(zimu) {
				this.selectIndexs = zimu
				document.getElementById(zimu).scrollIntoView();
			},
			setzhankai(idx, index) {
				this.list[index].lists[idx].iszhankai = !this.list[index].lists[idx].iszhankai
			},
			inputKeyword(keyword) {
				this.keyword = keyword
				this.list.forEach(el => {
					el.lists = []
				})
				this.getqiyeList()
			},
			getqiyeList() {
				// 导航轮播图
				ajax.pmqiyeList({
					keyword: this.keyword,
					page: this.page,
					shouzimu: '',
				}).then(res => {
					this.list.forEach(el => {
						res.data.forEach(els => {
							if (el.zimu == els.shouzimu) {
								if (el.lists.length == 0) {
									el.lists.push({
										address: els.province,
										iszhankai: false,
										listss: [els]
									})
								} else {
									el.lists.forEach(temp => {
										if (temp.address == els.province) {
											temp.listss.push(els)
										} else {
											el.lists.push({
												address: els.province,
												iszhankai: false,
												listss: [els]
											})
										}
									})
								}
							}
						})
					})
				})
			},
			// setIndex(i) {
			// 	this.selectIndex = i
			// 	this.page = 0
			// 	this.list = this.$options.data().list;
			// 	this.getqiyeList()
			// },
			goDetail(id) {
				let routeData = this.$router.resolve({ //核心语句
					path: '/auctionfirmDetail', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: id
					}
				})
				window.open(routeData.href, '_blank');

			}
		}
	}
</script>

<style>
	.auctionFirm {
		width: 100%;
		background-color: #F7F7F7;
	}

	.auctionFirmCon {
		width: 1200px;
		min-height: 1000px;
		padding-bottom: 100px;
		box-sizing: border-box;
		margin: 0 auto;
	}

	.auctionFirmCon_one {
		position: sticky;
		top: 0;
		display: flex;
		-js-display: flex;
		align-items: center;
		background: #FFFFFF;
		height: 60px;
		margin-top: 20px;
	}

	.auctionFirmCon_one_left {
		width: 165px;
		height: 60px;
		background: #FFFFFF;
		text-align: center;
		line-height: 60px;
		font-size: 18px;
		font-weight: bold;
		color: #333333;
	}

	.auctionFirmCon_one_left_right {
		width: 70px;
		height: 60px;
		background: #FFFFFF;
		text-align: center;
		line-height: 60px;
		font-size: 18px;
		font-weight: bold;
		color: #5A5A5A;
		cursor: pointer;
	}

	.auctionFirmCon_one_left_rights {
		width: 70px;
		height: 60px;
		background: #D6363B;
		text-align: center;
		line-height: 60px;
		font-size: 18px;
		font-weight: bold;
		color: #FFFFFF;
		cursor: pointer;
	}

	.auctionFirmCon_two {
		margin-top: 41px;
	}

	.auctionFirmCon_two_title {
		height: 60px;
		background: #EEEEEE;
		border-bottom: 2px solid #E24555;
		line-height: 60px;
		padding-left: 20px;
		box-sizing: border-box;
		font-size: 28px;
		font-weight: bold;
		color: #333333;
	}

	.auctionFirmCon_two_two {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20px;
		box-sizing: border-box;
		margin-top: 30px;
	}

	.auctionFirmCon_two_two_left {
		font-size: 28px;
		font-weight: bold;
		color: #D6363B;
		width: 200px !important;
		flex-shrink: 0;
	}

	.auctionFirmCon_two_two_right {
		display: flex;
		align-items: center;
		-js-display: flex;
		font-size: 16px;
		font-weight: 400;
		color: #5A5A5A;
	}

	.auctionFirmCon_two_two_right img {
		width: 16px;
		height: 16px;
		margin-left: 8px;
	}

	.auctionFirmCon_two_three {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 30px;
	}

	.auctionFirmCon_two_threes {
		width: 100%;
		height: 200px;
		overflow: hidden;
		display: flex;
		-js-display: flex;
		flex-wrap: wrap;
		margin-top: 30px;
	}

	.auctionFirmCon_two_three_view {
		width: 285px;
		height: 88px;
		background: #FFFFFF;
		display: flex;
		align-items: center;
		-js-display: flex;
		justify-content: space-around;
		margin-bottom: 16px;
		cursor: pointer;
	}

	.auctionFirmCon_two_three_views {
		margin-left: 20px;
	}

	.auctionFirmCon_two_three_view_img {
		width: 50px !important;
		height: 50px;
		background-color: red;
	}

	.auctionFirmCon_two_three_view_right {
		height: 50px;
		width: 180px !important;
		display: flex;
		-js-display: flex;
		flex-direction: column;
		justify-content: space-between;
		font-size: 16px;
		font-weight: 400;
		color: #5A5A5A;
	}

	.auctionFirmCon_two_three_view_right>div:nth-child(2) {
		font-size: 14px;
		font-weight: 400;
		color: #999999;
	}
</style>
