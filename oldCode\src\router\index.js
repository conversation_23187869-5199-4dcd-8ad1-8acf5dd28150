import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '../views/Home.vue'

Vue.use(VueRouter)

const routes = [{
		path: '/',
		name: 'Home',
		component: Home,

	},
	{
		path: '/login',
		name: 'Login',
		component: () => import('../views/Login.vue')
	},
	{
		path: '/auction',
		name: 'Auction',
		component: () => import('../views/Auction.vue')
	},
	{
		path: '/biaodi',
		name: 'Biaodi',
		component: () => import('../views/Biaodi.vue')
	},
	{
		path: '/auctionfirm',
		name: 'Auctionfirm',
		component: () => import('../views/Auctionfirm.vue')
	},
	{
		path: '/auctionfirmDetail',
		name: 'AuctionfirmDetail',
		component: () => import('../views/AuctionfirmDetail.vue')
	},
	{
		path: '/auctionDetail/:id',
		name: 'AuctionDetail',
		component: () => import('../views/AuctionDetail.vue')
	},
	{
		path: '/weishenDetail/:id',
		name: 'weishenDetail',
		component: () => import('../views/weishenDetail.vue')
	},
	{
		path: '/enter',
		name: 'Enter',
		component: () => import('../views/Enter.vue')
	},
	{
		path: '/about',
		name: 'About',
		component: () => import('../views/About.vue')
	},
	{
		path: '/news',
		name: 'News',
		component: () => import('../views/News.vue')
	},
	{ 
		path: '/quotation',
		name: 'Quotation', 
		component: () => import('../views/Quotation.vue')
	},
	{
		path: '/news_info',
		name: 'News_info',
		component: () => import('../views/News_info.vue')
	},
	{
		path: '/userInfo',
		name: 'UserInfo',
		component: () => import('../views/UserInfo.vue')
	}, {
		path: '/enterqiye',
		name: 'Enterqiye',
		component: () => import('../views/Enterqiye.vue')
	}, {
		path: '/targetmulu',
		name: 'TargetMulu',
		component: () => import('../views/TargetMulu.vue')
	}, {
		path: '/noticeDetail',
		name: 'NoticeDetail',
		component: () => import('../views/NoticeDetail.vue')
	}, {
		path: '/notice',
		name: 'Notice',
		component: () => import('../views/Notice.vue')
	}, {
		path: '/qiyesub',
		name: 'Qiyesub',
		component: () => import('../views/Qiyesub.vue')
	}, {
		path: '/disposal',
		name: 'disposal',
		component: () => import('../views/disposal/index.vue')
	}, {
		path: '/enterprise',
		name: 'enterprise',
		component: () => import('../views/enterprise.vue')
	},{
		path: '/announcement',
		name: 'announcementList',
		component: () => import('../views/announcement/list.vue')
	},{
		path: '/announcement/detail',
		name: 'announcementDetail',
		component: () => import('../views/announcement/detail.vue')
	}
	
]

const router = new VueRouter({
	// mode: 'history',
	base: process.env.BASE_URL,
	routes,
	scrollBehavior(to, from, savedPosition) {
		return {
			x: 0,
			y: 0
		}
	}
})

export default router
