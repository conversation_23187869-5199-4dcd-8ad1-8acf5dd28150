<template>
	<div class="auctionDetailc min_wrapper_1500 ">
		<Headers />
		<div class="auctionDetail_c">
			<div class="auctionDetail_c_one">
				<div class="auctionDetail_c_one_img" v-if="biaodInfo.pmh_xingshi == 1">
					同步竞价
				</div>
				<div class="auctionDetail_c_one_imgs" v-if="biaodInfo.pmh_xingshi == 2">
					线上竞价
				</div>
				<div class="auctionDetail_c_one_left">
					<div class="auctionDetail_c_one_left_s" style="cursor: pointer;">{{biaodInfo.pmh_name}}</div>
					<br>
					<div class="auctionDetail_c_one_left_da">
						<div class="auctionDetail_c_one_left_w" @click="goQiye">拍卖公司：{{biaodInfo.qiyemingcheng}}</div>
						<div class="auctionDetail_c_one_left_w">联系电话：{{biaodInfo.qiyephone}}</div>
					</div>
				</div>
				<div class="auctionDetail_c_one_right">
					<img @click="oneTitleLeftClick" src="../assets/auction/left.png">
					<div class="auctionDetail_c_one_right_div">
						<div class="auctionDetail_c_one_right_div_one"
							:style="{transform:'translateX('+ biaodListIndex * 145 + 'px' + ')'}">
							<div class="auctionDetail_c_one_right_div_one_divss " v-for="(item,i) in biaodList"
								@click="setBiaodiId(item)">
								<div :class="biaodInfo.id == item.id?'auctionDetail_c_one_right_div_one_title auctionDetail_c_one_right_div_one_divsss':'auctionDetail_c_one_right_div_one_title' ">
									<img style="width:100%;height:100%" :src="url + item.bd_url" alt="" @error="(e)=>e.target.src = item.bd_url ? 'https://oss.yunpaiwang.com/'+item.bd_url : ''">
									<div class="auctionDetail_c_one_right_div_one_divss_dd"
										v-if="item.bd_status == 2 || item.bd_status == 3">
										<div>正在进行</div>
										<div>{{i+1}}</div>
									</div>
									<div class="auctionDetail_c_one_right_div_one_divss_ddaa" v-if="item.bd_status == 1">
										<div>即将开始</div>
										<div>{{i+1}}</div>
									</div>
									<div v-if="item.bd_status == 4" class="auctionDetail_c_one_right_div_one_divss_ddss">
										<div>已流拍</div>
										<div>{{i+1}}</div>
									</div>
									<div v-if="item.bd_status == 8" class="auctionDetail_c_one_right_div_one_divss_ddss">
										<div>已撤拍</div>
										<div>{{i+1}}</div>
									</div>
									<div v-if="item.bd_status == 7" class="auctionDetail_c_one_right_div_one_divss_dd">
										<div>已暂停</div>
										<div>{{i+1}}</div>
									</div>
									<div v-if="item.bd_status == 6" class="auctionDetail_c_one_right_div_one_divss_dd">
										<div>审批中</div>
										<div>{{i+1}}</div>
									</div>
									<div v-if="item.bd_status == 5" class="auctionDetail_c_one_right_div_one_divss_ddss">
										<div>已成交</div>
										<div>{{i+1}}</div>
									</div>
								</div>
								<div class="auctionDetail_c_one_right_div_one_titles">{{item.bd_title}}</div>
							</div>
						</div>
					</div>
					<img @click="oneTitlerightClick" src="../assets/auction/right.png">
				</div>
			</div>
			<div class="auctionDetail_c_two">
				<div class="auctionDetail_c_two_left">
					<div class="auctionDetail_c_two_left_one">{{biaodInfo.bd_title}}</div>
					<div class="auctionDetail_c_two_left_two">
						<div class="auctionDetail_c_two_left_two_left">
							<div class="auctionDetail_c_two_left_two_leftssssss">
								<div class="auctionDetail_c_two_left_two_left_o">
									<img :src="url + biaodiImgs[biaodiImgsIndex]" @error="(e)=>e.target.src = biaodiImgs[biaodiImgsIndex] ? 'https://oss.yunpaiwang.com/'+biaodiImgs[biaodiImgsIndex] : ''">
									<div class="auctionDetail_c_two_left_two_left_omaskTop" @mouseenter="enterHandler"
										@mousemove="mouseMove" @mouseout="mouseLeave"></div>
									<div v-show="isShowImg" class="auctionDetail_c_two_left_two_left_omaskToptop"
										:style="topStyle"></div>
								</div>
								<div v-show="isShowImg" class="auctionDetail_c_two_left_two_left_omaskTopright">
									<img :style="r_img" class="auctionDetail_c_two_left_two_left_omaskToprightrightImg"
										:src="url + biaodiImgs[biaodiImgsIndex]" alt="" @error="(e)=>e.target.src = biaodiImgs[biaodiImgsIndex] ? 'https://oss.yunpaiwang.com/'+biaodiImgs[biaodiImgsIndex] : ''">
								</div>
							</div>
							<div class="auctionDetail_c_two_left_two_left_s">
								<img @click="oneTitleLeftClicks" src="../assets/auction/left.png">
								<div class="auctionDetail_c_two_left_two_left_s_div">
									<div class="auctionDetail_c_two_left_two_left_s_divs"
										:style="{transform:'translateX('+ biaodListIndexs * 75 + 'px' + ')'}">
										<img @click="setIndex(i)" v-for="item,i in biaodiImgs" :src="url + item" :key="i" alt="" @error="(e)=>e.target.src = item ? 'https://oss.yunpaiwang.com/'+item : ''">
									</div>
								</div>
								<img @click="oneTitlerightClicks" src="../assets/auction/right.png">
							</div>
							<div class="auctionDetail_c_two_left_two_left_w">
								<div style="cursor: pointer;" v-if="isShoucang == 0" @click="biaodishoucangClick(1)">
									<img src="../assets/auction/isFalse.png">
									<div>点击收藏</div>
								</div>
								<div style="cursor: pointer;" v-if="isShoucang == 1" @click="biaodishoucangClick(0)">
									<img src="../assets/auction/isTrue.png">
									<div style="color: #A80012;">取消收藏</div>
								</div>
								<div style="width: 200px;">
									<img src="../assets/home/<USER>">
									<div>{{biaodInfo.bd_weiguan}}次点击量</div>
								</div>
							</div>
						</div>
						<div class="auctionDetail_c_two_left_two_right">
							<div class="auctionDetail_c_two_left_two_right_one">
								<img v-if="biaodInfo.bd_status == 1" src="../assets/auction/back_title.png">
								<img v-if="biaodInfo.bd_status == 2 || biaodInfo.bd_status == 3 || biaodInfo.bd_status == 5 ||  biaodInfo.bd_status == 7 ||  biaodInfo.bd_status == 6 "
									src="../assets/auction/back_title1.png">
								<img v-if="biaodInfo.bd_status == 4 ||  biaodInfo.bd_status == 8"
									src="../assets/auction/back_title2.png">
								<div v-if="biaodInfo.bd_status == 1 && !isPaimaishi"
									class="auctionDetail_c_two_left_two_right_one_w">
									<div class="auctionDetail_c_two_left_two_right_one_w_a">
										<div>即将</div>
										<div>开始</div>
									</div>
									<div class="auctionDetail_c_two_left_two_right_one_w_b">
										<span class="auctionDetail_c_two_left_two_right_one_w_bs">&emsp;距开始&emsp;</span>
										倒计时：
										<span
											class="auctionDetail_c_two_left_two_right_one_w_bsb">{{info.dayDiff}}</span>
										天
										<span class="auctionDetail_c_two_left_two_right_one_w_bsb">{{info.hours}}
										</span> 时
										<span class="auctionDetail_c_two_left_two_right_one_w_bsb">{{info.minutes}}
										</span> 分
										<span class="auctionDetail_c_two_left_two_right_one_w_bsb">{{info.seconds}}
										</span> 秒
									</div>
								</div>
								<div v-if="(biaodInfo.bd_status == 2 || biaodInfo.bd_status == 3) && !isPaimaishi "
									class="auctionDetail_c_two_left_two_right_one_w">
									<div class="auctionDetail_c_two_left_two_right_one_w_ass">
										<div>正在</div>
										<div>进行</div>
									</div>
									<div class="auctionDetail_c_two_left_two_right_one_w_b">
										<span
											class="auctionDetail_c_two_left_two_right_one_w_bs">&emsp;{{biaodInfo.bd_status ==2?'自由竞价':'限时竞价'}}&emsp;</span>
										倒计时：
										<span
											class="auctionDetail_c_two_left_two_right_one_w_bsb">{{info.dayDiff}}</span>
										天
										<span class="auctionDetail_c_two_left_two_right_one_w_bsb">{{info.hours}}
										</span> 时
										<span class="auctionDetail_c_two_left_two_right_one_w_bsb">{{info.minutes}}
										</span> 分
										<span class="auctionDetail_c_two_left_two_right_one_w_bsb">{{info.seconds}}
										</span> 秒
									</div>
								</div>
								<div v-if="isPaimaishi && ( biaodInfo.bd_status == 2 || biaodInfo.bd_status == 3)"
									class="auctionDetail_c_two_left_two_right_one_w">
									<div class="auctionDetail_c_two_left_two_right_one_w_ass">
										<div>正在</div>
										<div>进行</div>
									</div>
									<div class="auctionDetail_c_two_left_two_right_one_w_b">
										等待拍卖师处理
									</div>
								</div>
								<div v-if="isPaimaishi &&  biaodInfo.bd_status == 1"
									class="auctionDetail_c_two_left_two_right_one_w">
									<div class="auctionDetail_c_two_left_two_right_one_w_a">
										<div>即将</div>
										<div>开始</div>
									</div>
									<div class="auctionDetail_c_two_left_two_right_one_w_b">
										等待拍卖师处理
									</div>
								</div>
								<div v-if="biaodInfo.bd_status == 7 " class="auctionDetail_c_two_left_two_right_one_w">
									<div class="auctionDetail_c_two_left_two_right_one_w_ass">
										<div>标的</div>
										<div>暂停</div>
									</div>
									<div class="auctionDetail_c_two_left_two_right_one_w_b">
										等待拍卖师处理
									</div>
								</div>
								<div v-if="biaodInfo.bd_status == 5" class="auctionDetail_c_two_left_two_right_one_w">
									<div class="auctionDetail_c_two_left_two_right_one_w_ass">
										<div>标的</div>
										<div>成交</div>
									</div>
									<div class="auctionDetail_c_two_left_two_right_one_w_b">
										结束时间：{{biaodInfo.end_time|formatDate}}
									</div>
								</div>
								<div v-if="biaodInfo.bd_status == 6" class="auctionDetail_c_two_left_two_right_one_w">
									<div class="auctionDetail_c_two_left_two_right_one_w_ass">
										<div>正在</div>
										<div>进行</div>
									</div>
									<div class="auctionDetail_c_two_left_two_right_one_w_b">
										标的审批中
									</div>
								</div>
								<div v-if="biaodInfo.bd_status == 8" class="auctionDetail_c_two_left_two_right_one_w">
									<div class="auctionDetail_c_two_left_two_right_one_w_as">
										<div>标的</div>
										<div>撤拍</div>
									</div>
									<div class="auctionDetail_c_two_left_two_right_one_w_b">
										撤拍时间：{{biaodInfo.end_time|formatDate}}
									</div>
								</div>

								<div v-if="biaodInfo.bd_status == 4" class="auctionDetail_c_two_left_two_right_one_w">
									<div class="auctionDetail_c_two_left_two_right_one_w_as">
										<div>标的</div>
										<div>流拍</div>
									</div>
									<div class="auctionDetail_c_two_left_two_right_one_w_b">
										结束时间：
										{{biaodInfo.end_time |formatDate}}
									</div>
								</div>
								<div class="auctionDetail_c_two_left_two_right_one_tw">
									<div>共计出价次数：<span>{{chujiailist.length}}</span>次</div>
									<div
										v-if="biaodInfo.pmh_type === 3 && biaodInfo.bd_status !== 4 && biaodInfo.bd_status !== 5 && biaodInfo.bd_status !== 6&& biaodInfo.bd_status !== 1">
										当前第<span>{{isChuMangNum}}</span>轮出价</div>
									<div
										v-if="biaodInfo.pmh_type === 5 && biaodInfo.bd_status !== 4 && biaodInfo.bd_status !== 5 && biaodInfo.bd_status !== 6&& biaodInfo.bd_status !== 1">
										当前第<span>{{isChuMangNum}}</span>轮出价</div>
									<div>我的竞价号牌：<span>{{jingpaihaopai || '暂无'}}</span></div>
								</div>
								<div class="auctionDetail_c_two_left_two_right_one_thre">
									<img class="auctionDetail_c_two_left_two_right_one_chengjiaobeii"
										v-if="biaodInfo.bd_status == 5" src="../assets/auction/chengjiaoback.png">
									<img class="auctionDetail_c_two_left_two_right_one_chengjiaobeii"
										v-if="biaodInfo.bd_status == 4" src="../assets/auction/liupai.png">
									<img class="auctionDetail_c_two_left_two_right_one_chengjiaobeii"
										v-if="biaodInfo.bd_status == 8" src="../assets/auction/chepai.png">
									<div style="position: relative;z-index: 2;overflow: hidden;padding-top:15px;">
										<template v-if="isTeshu">
											<div v-if="biaodInfo.bd_status == 1  || biaodInfo.bd_status == 8  || biaodInfo.bd_status == 4 "
												class="auctionDetail_c_two_left_two_right_one_thre_one">
													<div>起拍价：<span>￥{{biaodInfo.bd_qipaijia}}</span> 元/{{biaodInfo.bd_danwei}}
													</div>
													<div class="auctionDetail_c_two_left_two_right_one_thre_ones">出价人:暂无</div>
											</div>
											<div v-if="biaodInfo.bd_status == 2 || biaodInfo.bd_status == 3 || biaodInfo.bd_status == 7 || biaodInfo.bd_status == 6 "
												class="auctionDetail_c_two_left_two_right_one_thre_one">
												<div>当前价:<span>￥{{dangqianjiaInit}}</span> 元/{{biaodInfo.bd_danwei}}</div>
												<div v-if="biaodInfo.pmh_type !== 3 && biaodInfo.pmh_type !== 5"
													class="auctionDetail_c_two_left_two_right_one_thre_ones">
													出价人:<span style="font-size: 13px;"
														v-if="chujiailist.length == 0">暂无</span>
													<span v-if="chujiailist.length != 0"
														style="color: #A80012;font-size: 13px;">{{chujiailist[0].jingpaihaopai}}</span>
												</div>
												<div v-if="biaodInfo.pmh_type === 3 || biaodInfo.pmh_type === 5"
													class="auctionDetail_c_two_left_two_right_one_thre_oneType">
													出价人:<span style="font-size: 13px;"
														v-if="chujiailist.length ==0">暂无</span>
													<span v-if="chujiailist.length !=0"
														style="color: #A80012;font-size: 13px;">{{bd_jdnum}}</span>
												</div>
											</div>
											<div v-if="biaodInfo.bd_status == 5"
												class="auctionDetail_c_two_left_two_right_one_thre_one">
												<div>成交价:<span>￥{{dangqianjiaInit}}</span> 元/{{biaodInfo.bd_danwei}}</div>
												<div v-if="biaodInfo.pmh_type !== 3 && biaodInfo.pmh_type !== 4 && biaodInfo.pmh_type !== 5"
													class="auctionDetail_c_two_left_two_right_one_thre_ones">
													出价人:<span style="font-size: 13px;"
														v-if="chujiailist.length ==0">暂无</span>
													<span v-if="chujiailist.length !=0"
														style="color: #A80012  !important;font-size: 13px;">{{chujiailist[0].jingpaihaopai}}</span>
												</div>
												<div v-if="biaodInfo.pmh_type === 3 || biaodInfo.pmh_type === 4 || biaodInfo.pmh_type === 5"
													class="auctionDetail_c_two_left_two_right_one_thre_oneType">
													出价人:
													<span style="color: #A80012;font-size: 13px;">{{bd_jdnum}}</span>
												</div>
											</div>
										</template>
										<template v-else>
											<div class="auctionDetail_c_two_left_two_right_one_thre_one">
												<div>无权查看</div> 
											</div>
										</template>
										<template
											v-if="!userInfo && biaodInfo.bd_status !=4 && biaodInfo.bd_status !=5 && biaodInfo.bd_status !=6 && biaodInfo.bd_status !=8">
											<div class="auctionDetail_c_two_left_two_right_one_thre_two">
												<div @click="goLogin">请先登录</div>
											</div>
										</template>
										<template v-if=" biaodInfo.bd_status ==6 && baomingIndex!=0 ">
											<div class="auctionDetail_c_two_left_two_right_one_thre_two">
												<div>审批中</div>
											</div>
										</template>
										<template
											v-if="baomingIndex== -1 && userInfo && !isBao && biaodInfo.bd_status !=4 && biaodInfo.bd_status !=5 && biaodInfo.bd_status !=6 && biaodInfo.bd_status !=8">
											<!-- <div class="auctionDetail_c_two_left_two_right_one_thre_two">
												<div @click="isBao = true">报名标的</div>
											</div> -->
											<div class="auctionDetail_c_two_left_two_right_one_thre_two">
												<div @click="baominxieyi">报名标的</div>
											</div>
										</template>
										<template
											v-if="baomingIndex== -1&& userInfo && isBao&& biaodInfo.bd_status !=4 && biaodInfo.bd_status !=5&& biaodInfo.bd_status !=6 && biaodInfo.bd_status !=8 ">
											<div class="auctionDetail_c_two_left_two_right_one_thre_two">
												<div @click="gerenBaoming">个人报名</div>
												<div @click="qiyeBaoming" style="margin-left: 16px;">企业报名</div>
											</div>
											<div @click="isBao = false"
												class="auctionDetail_c_two_left_two_right_one_thre_three">
												取消
											</div>
										</template>
										<template
											v-if="baomingIndex == 0 && biaodInfo.bd_status !=4 && biaodInfo.bd_status !=5&& biaodInfo.bd_status !=8">
											<div class="auctionDetail_c_two_left_two_right_one_thre_two">
												<div style="background: #e7acac;">报名审核中</div>
												<div v-if="kanhuoIndex == 0" @click="viewingAgreement" style="margin-left: 10px;">确认看货</div>
												<div v-if="kanhuoIndex == 1" style="margin-left: 10px;background: #e7acac;">已看货</div>
											</div>
											<div class="auctionDetail_c_two_left_two_right_one_thre_two" style="margin-top:10px" v-if="kanhuoIndex == 1">
												<div v-if="!!zhengming" style="width: 250px;background: #e7acac;">缴纳凭证已上传</div>
												<div v-else @click="zhengmingUpload" style="width: 250px;">上传缴纳凭证</div>
											</div>
											
											<div v-if="shoukuanInfo"
												style="margin-top: 20px;font-size: 12px;padding-left: 20px;box-sizing: border-box;">
												<div style="line-height: 25px;color: #A80012;text-align: center;">
													请联系客服缴纳保证金本次标的保证金{{biaodInfo.bd_baozhengjin}}元</div>
												<template v-if="kanhuoIndex == 0">
													<div style="line-height: 25px;">公司：{{biaodInfo.qiyemingcheng}}</div>
													<div style="line-height: 25px;">银行账号：{{shoukuanInfo.kaihunum }}</div>
													<div style="line-height: 25px;">开户行：{{shoukuanInfo.kaihuname}}</div>
												</template>
											</div>
										</template>
										<template v-if="baomingIndex == 1&& biaodInfo.bd_status ==1">
											<div class="auctionDetail_c_two_left_two_right_one_thre_four"
												style="margin-top: 80px;">
												<div
													style="padding: 15px;box-sizing: border-box;background-color: #A80012;color: white;border-radius: 10px;">
													保证金审核已通过，请您耐心等待竞价会开始</div>
											</div>
										</template>
										<template
											v-if="baomingIndex == 1 && biaodInfo.bd_status !=1 && biaodInfo.bd_status !=5 && biaodInfo.bd_status !=4 && !isPaimaishi  && biaodInfo.bd_status !=8">
											<div v-if="biaodInfo.pmh_type == 1 || biaodInfo.pmh_type == 4"
												class="auctionDetail_c_two_left_two_right_one_thre_chujia">
												<div class="auctionDetail_c_two_left_two_right_one_thre_chujias">
													<input v-model="jiage" type="number">
												</div>
												<div @click="chujia"
													class="auctionDetail_c_two_left_two_right_one_thre_chujiass">提交出价
												</div>
											</div>
											<div v-if="biaodInfo.pmh_type == 1 || biaodInfo.pmh_type == 4"
												class="auctionDetail_c_two_left_two_right_one_thre_chujia_bottom">
												<div @click="zhichu(jiajiadi)">
													+{{jiajiadi}}</div>
												<div @click="zhichu(jiajiazhong)">
													+{{jiajiazhong}}</div>
												<div @click="zhichu(jiajiagao)">
													+{{jiajiagao}}</div>
											</div>
											<div v-if="biaodInfo.pmh_type == 2"
												class="auctionDetail_c_two_left_two_right_one_thre_chujia">
												<div class="auctionDetail_c_two_left_two_right_one_thre_chujias">
													<input v-model="jiage" type="number">
												</div>
												<div @click="jianjiaRequest"
													class="auctionDetail_c_two_left_two_right_one_thre_chujiass">提交出价
												</div>
											</div>
											<div v-if="biaodInfo.pmh_type == 2"
												class="auctionDetail_c_two_left_two_right_one_thre_chujia_bottom">
												<div @click="jianjia(jiajiadi)">
													-{{jiajiadi}}</div>
												<div @click="jianjia(jiajiazhong)">
													-{{jiajiazhong}}</div>
												<div @click="jianjia(jiajiagao)">
													-{{jiajiagao}}</div>
											</div>
											<div v-if="biaodInfo.pmh_type == 3"
												class="auctionDetail_c_two_left_two_right_one_thre_chujia">
												<div class="auctionDetail_c_two_left_two_right_one_thre_chujias">
													<input v-model="jiage" type="number">
												</div>
												<div @click="mangpaiChu"
													class="auctionDetail_c_two_left_two_right_one_thre_chujiass">提交出价
												</div>
											</div>
											<div v-if="biaodInfo.pmh_type == 5"
												class="auctionDetail_c_two_left_two_right_one_thre_chujia">
												<div class="auctionDetail_c_two_left_two_right_one_thre_chujias">
													<input v-model="jiage" type="number">
												</div>
												<div @click="mangpaiChuZhiding"
													class="auctionDetail_c_two_left_two_right_one_thre_chujiass">提交出价
												</div>
											</div>
										</template>
										<template v-if="biaodInfo.bd_status ==5">
											<div class="auctionDetail_c_two_left_two_right_one_thre_fours">
												<div
													style="width: 140px;height: 40px;background: #F7F7F7;border: 1px solid #C1C1C1;border-radius: 4px;text-align: center;line-height: 40px;color:#999999 ; font-size:15px">
													标的已成交</div>
												<div @click="nextBIaodi"
													v-if="biaodInfo.id != biaodList[biaodList.length -1 ].id && biaodList.length > 1 && biaodInfo.pmh_status != 2"
													style="width: 140px;height: 40px;background: #FFFFFF;border: 1px solid #A80012;border-radius: 4px;text-align: center;line-height: 40px;color:#A80012 ;margin-top: 15px;">
													进入下个标的</div>
											</div>
										</template>
										<template v-if="biaodInfo.bd_status ==4">
											<div class="auctionDetail_c_two_left_two_right_one_thre_fours">
												<div
													style="width: 140px;height: 40px;background: #F7F7F7;border: 1px solid #C1C1C1;border-radius: 4px;text-align: center;line-height: 40px;color:#999999 ;">
													标的已流拍</div>
												<div @click="nextBIaodi"
													v-if="biaodInfo.id != biaodList[biaodList.length -1 ].id && biaodList.length > 1 && biaodInfo.pmh_status != 2"
													style="width: 140px;height: 40px;background: #FFFFFF;border: 1px solid #A80012;border-radius: 4px;text-align: center;line-height: 40px;color:#A80012 ;margin-top: 15px;">
													进入下个标的</div>
											</div>
										</template>
										<template v-if="biaodInfo.bd_status ==8">
											<div class="auctionDetail_c_two_left_two_right_one_thre_fours">
												<div
													style="width: 140px;height: 40px;background: #F7F7F7;border: 1px solid #C1C1C1;border-radius: 4px;text-align: center;line-height: 40px;color:#999999 ;">
													标的已撤拍</div>
											</div>
										</template>
										<template
											v-if="isPaimaishi && ( biaodInfo.bd_status==2|| biaodInfo.bd_status==3) && baomingIndex == 1 ">
											<div class="auctionDetail_c_two_left_two_right_one_thre_fours">
												<div
													style="width: 140px;height: 40px;background: #F7F7F7;border: 1px solid #C1C1C1;border-radius: 4px;text-align: center;line-height: 40px;color:#999999 ;">
													等待拍卖师处理</div>
											</div>
										</template>
										<template v-if="biaodInfo.bd_status==7 && baomingIndex!=0 ">
											<div class="auctionDetail_c_two_left_two_right_one_thre_fours">
												<div
													style="width: 200px;height: 40px;background: #F7F7F7;border: 1px solid #C1C1C1;border-radius: 4px;text-align: center;line-height: 40px;color:#999999 ;">
													已暂停竞价等待拍卖师处理</div>
											</div>
										</template>
										<div v-if="baomingIndex == -1 && biaodInfo.bd_status !=4 && biaodInfo.bd_status !=5&& biaodInfo.bd_status !=6&& biaodInfo.bd_status !=7&& biaodInfo.bd_status !=8"
											class="auctionDetail_c_two_left_two_right_one_thre_four">
											<img src="../assets/auction/jingao.png">
											<div>{{biaodInfo.yongjin}}</div>
										</div>
										<div v-if="baomingIndex == -1 && biaodInfo.bd_status !=4 && biaodInfo.bd_status !=5&& biaodInfo.bd_status !=6&& biaodInfo.bd_status !=8"
											class="auctionDetail_c_two_left_two_right_one_thre_four">
											<img src="../assets/auction/jingao.png">
											<div>个人或企业认证审核成功后方可报名标的，先报名标的交保证金后再出价</div>
										</div>
										
										<div v-if="biaodInfo.bd_status == 5 "
											class="auctionDetail_c_two_left_two_right_one_thre_four">
											<img src="../assets/auction/jingao.png">

											<div v-if="baomingIndex == 1 && jingpaihaopai == bd_jdnum">恭喜您已中标，请买受人凭《成交通知书》联系拍卖企业支付尾款并办理交割！</div>
											<div v-else-if="baomingIndex == 1 && jingpaihaopai != bd_jdnum">非常遗憾您本次未中标，期待下次您的参与，谢谢！</div>
											<div v-else>请买受人联系拍卖公司支付尾款并办理交割！</div>
										</div>
										<div v-if="biaodInfo.bd_status == 6 && baomingIndex!=0 "
											class="auctionDetail_c_two_left_two_right_one_thre_four">
											<img src="../assets/auction/jingao.png">
											<div>标的状态审批中，请耐心等待审批</div>
										</div>
										<div v-if="biaodInfo.bd_status == 4  "
											class="auctionDetail_c_two_left_two_right_one_thre_four">
											<img src="../assets/auction/jingao.png">
											<div>因无人出价或出价未达到保留价，标的已流拍！</div>
										</div>
										<div v-if="biaodInfo.bd_status == 8  "
											class="auctionDetail_c_two_left_two_right_one_thre_four">
											<img src="../assets/auction/jingao.png">
											<div>撤拍理由：其他</div>
										</div>
									</div>
								</div>
								<div class="auctionDetail_c_two_left_two_right_onefour">
									<div >起拍价：<span>{{biaodInfo.bd_qipaijia}}{{biaodInfo.qpj_danwie}}</span></div>
									<div>保证金：<span>{{biaodInfo.bd_baozhengjin}}元</span></div>
									<div>评估价：<span>{{biaodInfo.bd_pinggujiage}}</span></div>
									<!-- {{biaodInfo.bd_baoliujia}} -->
									<div>保留价：<span>{{biaodInfo.bd_baoliujia}}</span></div>
									<div v-if="biaodInfo.pmh_type===2">减价幅度：<span>{{biaodInfo.bd_jiajiafudu}}元</span></div>
									<div v-else>加价幅度：<span>{{biaodInfo.bd_jiajiafudu}}元</span></div>
									<div>数量：<span>{{biaodInfo.bd_num}}</span></div>
									<div>自由竞价：
										<span>
											<template v-if="biaodInfo.new_ziyou_time.d>0">
												{{biaodInfo.new_ziyou_time.d}}天
											</template>
											<template>
												{{biaodInfo.new_ziyou_time.h}}时
											</template>
											<template>
												{{biaodInfo.new_ziyou_time.i}}分
											</template>
											<template>
												{{biaodInfo.new_ziyou_time.s}}秒
											</template>

										</span>
									</div>
									<div>限时竞价：
										<span>
											<template v-if="biaodInfo.new_xianshi_time.d>0">
												{{biaodInfo.new_xianshi_time.d}}天
											</template>
											<template>
												{{biaodInfo.new_xianshi_time.h}}时
											</template>
											<template>
												{{biaodInfo.new_xianshi_time.i}}分
											</template>
											<template>
												{{biaodInfo.new_xianshi_time.s}}秒
											</template>
										</span>
									</div>
									<div>单位：<span>{{biaodInfo.bd_danwei}}</span></div>
									<!-- <span v-if="biaodInfo.parentid == 2">杜长安</span><span v-if="biaodInfo.parentid == 1">张丽红</span> -->
									<div>拍卖师：<span>{{biaodInfo.pms_name}}</span><span @click="paimaishi=true"
											style="color: #A80012 !important;cursor: pointer;">查看证件</span></div>
									<div>拍卖师证件号：<span>{{biaodInfo.pms_card}}</span></div>
									<div>所在地：<span>{{province}}{{city}}{{district}}{{biaodInfo.address}}</span></div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!-- 上传缴纳凭证 -->
				<detail-upload ref="uploadImg" @getInfo="isBiaodiBoaming"></detail-upload>
				<!-- 看货协议 -->
				<el-dialog :visible.sync="dialogView" width="50%" @close="isViewQuxiao">
					<div v-if="userInfo">
						<div class="agreement_title">
							{{xieyi.title}}
						</div>
						<div class="agreement_item">
							<div class="item_label">公司名称：</div>
							<div class="item_view">{{ userInfo.qiyemingcheng || '' }}</div>
						</div>
						<div class="agreement_item">
							<div class="item_label">联系人：</div>
							<div class="item_view">{{ userInfo.cnname || '' }}</div>
						</div>
						<div class="agreement_item">
							<div class="item_label">联系电话：</div>
							<div class="item_view">{{ userInfo.mobile || '' }}</div>
						</div>
						<div class="agreement_text">
							鉴于我公司于<span class="text_bottomline">{{today}}</span>对<span class="text_bottomline">{{biaodInfo.pmh_name}}-{{biaodInfo.bd_title}}</span> 进行了现场看货，现作出如下确认声明：
						</div>
						<font v-html="xieyi.content">
						</font>
						<div class="xieyiClick">
							<el-button v-if="isXieyiClick" type="success" @click="kanhuoxieyi">
								我已看货</el-button>
							<el-button v-if="!isXieyiClick" type="info">我已看货 {{mioashu}}秒</el-button>
							<el-button type="info" @click="isViewQuxiao">取消</el-button>
						</div>
					</div>
				</el-dialog>
				<!-- 报名协议 -->
				<el-dialog :title="xieyi.title" :visible.sync="dialogVisible" width="50%" @close="isXieyiQuxiao">
					<div>
						<font v-html="xieyi.content">
						</font>
						<div class="xieyiClick">
							<el-button v-if="isXieyiClick" type="success" @click="isBao = true,dialogVisible = false">
								我同意</el-button>
							<el-button v-if="!isXieyiClick" type="info">我同意 {{mioashu}}秒</el-button>
							<el-button type="info" @click="isXieyiQuxiao">取消</el-button>
						</div>
					</div>
				</el-dialog>

				<audio ontrols="controls" ref="audio" id="audio" autoplay controls :src="bofang" :hidden="true"></audio>

				<div class="auctionDetail_c_two_right">
					<div class="auctionDetail_c_two_right_a">
						<div class="auctionDetail_c_two_right_a_a">拍卖师发言</div>
						<div class="auctionDetail_c_two_right_a_b">
							<div v-for="(item,i) in fayanList">
								<div class="auctionDetail_c_two_right_a_b_div">
									<div>{{item.type_name}}</div>
									<div>{{item.addtime_name}}</div>
								</div>
								<div class="auctionDetail_c_two_right_a_b_divs">{{item.content}}</div>
							</div>
						</div>
					</div>
					<div class="auctionDetail_c_two_right_b">
						<div class="auctionDetail_c_two_right_b_title">
							<div class="auctionDetail_c_two_right_b_titlea">状态 </div>
							<div class="auctionDetail_c_two_right_b_titleb">出价人</div>
							<div class="auctionDetail_c_two_right_b_titlec">价格 </div>
						</div>
						<div class="auctionDetail_c_two_right_b_titles" v-if="isTeshu">
							<div v-for="item,i in chujiailist" class="auctionDetail_c_two_right_b_title"
								style="background: none;color: #000000;">
								<template v-if="i == 0 && biaodInfo.pmh_type!=3 && biaodInfo.pmh_type!=5">
									<img class="auctionDetail_c_two_right_b_title_img"
										src="../assets/auction/hongqi.png">
									<div class="auctionDetail_c_two_right_b_titlea" style="color: #ff0000;">领先</div>
									<div class="auctionDetail_c_two_right_b_titleb" style="color: #ff0000;">
										{{item.jingpaihaopai}}
									</div>
									<div class="auctionDetail_c_two_right_b_titlec" style="color: #ff0000;">
										{{item.dangqianjia}}
									</div>
								</template>
								<template v-else>
									<div class="auctionDetail_c_two_right_b_titlea" v-if="biaodInfo.pmh_type!=3 && biaodInfo.pmh_type!=5">出局
									</div>
									<div class="auctionDetail_c_two_right_b_titlea" v-if="biaodInfo.pmh_type==3 || biaodInfo.pmh_type==5">
										第{{item.chujia*1}}轮</div>
									<div class="auctionDetail_c_two_right_b_titleb">{{item.jingpaihaopai}} </div>
									<div class="auctionDetail_c_two_right_b_titlec">{{item.dangqianjia}} </div>
								</template>
							</div>
						</div>
						<div v-else>
							<div
								class="auctionDetail_c_two_left_two_right_one_thre_four">
								<div>无权查看</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!--这是拍卖师的照片-->
			<el-dialog :visible.sync="paimaishi" width="800px">
				<img :src="this.url + biaodInfo.pms_zheng" alt="" width="100%">
				<!-- <img v-if="biaodInfo.parentid == 2" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/duchangan.jpg" width="100%">
				<img v-if="biaodInfo.parentid == 1" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/zhanglihong.jpg" width="100%"> -->
				
			</el-dialog>

			<div class="auctionDetail_c_three">
				<div class="auctionDetail_c_three_one">
					<div @click="tabIndex=2"
						:class="tabIndex==2?'auctionDetail_c_three_oness':'auctionDetail_c_three_ones'">竞价公告</div>
					<div @click="tabIndex=4"
						:class="tabIndex==4?'auctionDetail_c_three_oness':'auctionDetail_c_three_ones'">标的介绍</div>
					<div @click="tabIndex=0"
						:class="tabIndex==0?'auctionDetail_c_three_oness':'auctionDetail_c_three_ones'">重要声明</div>
					<div @click="tabIndex=1"
						:class="tabIndex==1?'auctionDetail_c_three_oness':'auctionDetail_c_three_ones'">竞价记录({{chujiailist.length}})</div>
					<div @click="tabIndex=3"
						:class="tabIndex==3?'auctionDetail_c_three_oness':'auctionDetail_c_three_ones'">竞价须知</div>
					<div v-if="biaodInfo.pmh_type === 4 " @click="zongheList" class="auctionDetail_c_three_ones">综合报价排名
					</div>
				</div>
				<div v-if="tabIndex ==0" class="auctionDetail_c_three_two" v-html="biaodInfo.pmh_shengming || ''">
				</div>
				<div v-if="tabIndex ==1" class="auctionDetail_c_three_two">
					<table class="chujialist" v-if="isTeshu">
						<tbody>
							<tr>
								<td>状态</td>
								<td>时间</td>
								<td>编号</td>
								<td>价格</td>
							</tr>
							<tr v-for="item,i in chujiailist">
								<template v-if="i==0 && biaodInfo.pmh_type != 3 && biaodInfo.pmh_type != 5">
									<td style="color: #ff0000;font-size: 13px;">领先</td>
									<td style="color: #ff0000;font-size: 13px;">{{item.addtime | formatDate}}</td>
									<td style="color: #ff0000;font-size: 13px;">{{item.jingpaihaopai}} </td>
									<td style="color: #ff0000;font-size: 13px;">{{item.dangqianjia}} </td>
								</template>
								<template v-else>
									<td style="font-size: 13px;" v-if="biaodInfo.pmh_type != 3 && biaodInfo.pmh_type != 5">出局</td>
									<td style="font-size: 13px;" v-if="biaodInfo.pmh_type == 3 || biaodInfo.pmh_type == 5">第{{item.chujia*1}}轮</td>
									<td style="font-size: 13px;">{{item.addtime | formatDate}}</td>
									<td style="font-size: 13px;">{{item.jingpaihaopai}} </td>
									<td style="font-size: 13px;">{{item.dangqianjia}} </td>
								</template>
							</tr>
						</tbody>
					</table>
					<div v-else class="toLogin">
						<p>无权查看</p>
					</div>
				</div>
				<div v-if="tabIndex ==2" class="auctionDetail_c_three_two" v-html="biaodInfo.pmh_gonggao || ''"> </div>
				<div v-if="tabIndex ==3" class="auctionDetail_c_three_two" v-html="biaodInfo.pmh_xuzhi || ''"> </div>
				<div v-if="tabIndex ==4" class="auctionDetail_c_three_two" style="padding-bottom: 10px;"
					v-html="biaodInfo.bd_jieshao || ''">

				</div>
				<div v-if="tabIndex ==4" class="auctionDetail_c_three_two" style="clear: both;text-align:center">
					<img v-for="item,i in biaodiImgs" :src="url + item" alt="" style="max-width: 100%;" @error="(e)=>e.target.src = item ? 'https://oss.yunpaiwang.com/'+item : ''">
				</div>
			</div>
			<el-dialog :visible.sync="zonghepaiming" width="1000px">
				<div style="" class="shuaxin" @click="shuaxin">刷新</div>
				<table class="chujialist">
					<tbody>
						<tr>
							<td>竞价号</td>
							<td>总价</td>
							<td>出价详情</td>
						</tr>
						<tr v-for="item,i in zongheListInfo">
							<template v-if="item.jingpaihaopai.substr(0,3) === jingpaihaopais">
								<td style="font-size: 13px;color: #ff0000;">{{item.jingpaihaopai|jiequzifuc}}(我)</td>
								<td style="font-size: 13px;color: #ff0000;">{{item.zongjia}}元</td>
								<td style="font-size: 13px;width: 600px;color: #ff0000;">
									<div style="color: #ff0000;" v-for="items,index in item.chujia">
										标的{{index}}：{{items}}元、</div>
								</td>
							</template>
							<template v-if="item.jingpaihaopai.substr(0,3) !== jingpaihaopais">
								<td style="font-size: 13px;">{{item.jingpaihaopai|jiequzifuc}}</td>
								<td style="font-size: 13px;">{{item.zongjia}}元</td>
								<td style="font-size: 13px;width: 600px;">
									<div v-for="items,index in item.chujia">标的{{index}}：{{items}}元、</div>
								</td>
							</template>
						</tr>
					</tbody>
				</table>
			</el-dialog>
		</div>

		<FootersBottom />
		<homeRight ref="homeright" :routerId='biaodiId' />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import detailUpload from '@/components/detailUpload.vue'
	import area from '../assets/js/area.js'
	import ajax from '../store/Ajax'
	import time from '../store/time.js'
	import md5 from 'js-md5';

	export default {
		name: 'AuctionDetail',
		components: {
			Headers,
			FootersBottom,
			homeRight,
			detailUpload
		},
		data() {
			return {
				xieyi: {
					title: '',
					content: ''
				},
				mioashu: 6,
				isXieyiClick: false,
				dialogVisible: false,
				dialogView:false,
				// 标的是否收藏
				isShoucang: 0,
				// 拍卖id
				paimaihuiId: 0,
				isShowImg: false,
				topStyle: {
					transform: ''
				},
				r_img: {},
				info: {
					dayDiff: '',
					hours: '',
					minutes: '',
					seconds: '',
				},
				slideIndex: 0,
				tabIndex: 2,
				biaodiId: 0,
				biaodInfo: {
					new_ziyou_time: {
						d: 0,
						h: 0,
						i: 0,
						s: 0
					},
					new_xianshi_time: {
						d: 0,
						h: 0,
						i: 0,
						s: 0
					},
					end_time: Date(),
					id:0
				},
				isOnline: false,
				websock: {},
				biaodiImgs: [],
				biaodiImgsIndex: 0,
				url: this.$Pc.ossUrl,
				jishi: null,
				kaipaijishi: null,
				userInfo: null,
				fayan_id: 0,
				chujia_id: 0,
				paimaishi: false,
				page: 0,
				bofang: "",
				chujiailist: [],
				fayanList: [],
				biaodList: {
					0: {
						pmh_id: 0
					}
				},
				biaodListIndex: 0,
				// 是否报名
				isBao: false,
				// 是否报名
				baomingIndex: -1,
				//是否看货
				kanhuoIndex: -1,
				//是否上传缴纳凭证
				zhengming: '',
				//报名id
				bm_id: 0,
				// 收款信息
				shoukuanInfo: {},
				// 竞价号
				jingpaihaopai: 0,
				jingpaihaopais: 0,
				// 出价
				chujias: 0,
				// 当前出价
				dangqianjia: 0,
				jiage: '0.00',
				// 记录当前价
				dangqianjiaInit: 0,
				jiajiadi: '0.00',
				jiajiazhong: '0.00',
				jiajiagao: '0.00',
				// 时间异常状态
				isPaimaishi: false,
				province: '',
				city: '',
				district: '',
				biaodListIndexs: 0,
				// 盲拍轮数
				isChuMangNum: 1,
				bd_jdnum: '无',
				mangpaichujiaLnajie: 0,
				zonghepaiming: false,
				zongheListInfo: {}
			}
		},
		computed:{
			isTeshu(){
				if(this.biaodInfo.is_sou == 5) return this.baomingIndex == 1
				else return !this.biaodInfo.is_sou || this.biaodInfo.is_sou != 5
			},
			today(){
				let date = new Date();
				// console.log(date)
				let y = date.getFullYear();
				// console.log(y)
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				return y + '年' + MM + '月' + d + '日';
			}
		},
		filters: {
			formatDate: function(value) {
				value = value * 1000
				let date = new Date(value);
				// console.log(date)
				let y = date.getFullYear();
				// console.log(y)
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s;
			},
			jiequzifuc: function(value) {
				if (!value) return
				let temp = value.substr(0, 3)
				return temp
			}
		},
		created() {
			if (localStorage.getItem('userInfo')) {
				this.userInfo = JSON.parse(localStorage.getItem('userInfo'))
				console.log(this.userInfo )
			}
			if (this.$route.query.ispaimaih == 'true') {
				this.paimaihuiId = this.$route.query.id
				this.getpaimaihui()
			} else {
				this.biaodiId = this.$route.query.id
				setTimeout(() => {
					this.$refs.homeright.creatQrCode()
				}, 800)
				this.getbiaodicanshu()
			}
		},
		beforeDestroy() {
			clearInterval(this.jishi)
			this.jishi = null
			this.websock.close()
		},
		methods: {
			shuaxin() {
				ajax.hunJialist({
					pmh_id: this.biaodInfo.pmh_id
				}).then(res => {
					console.log(res)
					if (res.code == 1) {
						this.zongheListInfo = res.data
						this.$message.success(res.msg)
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			zongheList() {
				this.zonghepaiming = true
				ajax.hunJialist({
					pmh_id: this.biaodInfo.pmh_id
				}).then(res => {
					console.log(res)
					if (res.code == 1) {
						this.zongheListInfo = res.data
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			// 上传缴纳凭证按钮
			zhengmingUpload(){
				this.$refs.uploadImg.open({bd_id: this.biaodiId,member_id: this.userInfo ? this.userInfo.id : 0,bm_id: this.bm_id})
			},
			// 看货协议
			viewingAgreement(){
				ajax.newsinfo({
					id: 201
				}).then(res => {
					this.dialogView = true
					this.xieyi = res.data;
					this.setXieyiShi()
				})
			},
			baominxieyi() {
				this.xieyiTime = null
				this.mioashu = 6
				this.isXieyiClick = false
				ajax.newsinfo({
					id: 165
				}).then(res => {
					this.dialogVisible = true;
					// 禁用页面滚动
					document.body.style.overflow = 'hidden';
					this.xieyi = res.data;
					this.setXieyiShi()
				})
			},
			setXieyiShi() {
				this.xieyiTime = setInterval(() => {
					this.mioashu--
					if (this.mioashu == 0) {
						this.isXieyiClick = true
						clearInterval(this.xieyiTime)
						this.xieyiTime = null
					}
				}, 1000)
			},
			isViewQuxiao(){
				clearInterval(this.xieyiTime)
				this.xieyiTime = null
				this.mioashu = 6
				this.isXieyiClick = false
				this.dialogView = false
			},
			isXieyiQuxiao() {
				clearInterval(this.xieyiTime)
				this.xieyiTime = null
				this.mioashu = 6
				this.isXieyiClick = false
				this.dialogVisible = false
				// 启用页面滚动
				document.body.style.overflow = '';
			},
			nextBIaodi() {
				let biaodiid = this.biaodInfo.id
				let indexs = 0
				this.biaodList.forEach((el, index) => {
					if (el.id == biaodiid) {
						indexs = index
					}
				})

				this.setBiaodiId(this.biaodList[indexs + 1])
			},
			biaodishoucangClick(idx) {
				if (!this.userInfo) {
					this.$message.error('请先登录')
					this.goLogin()
				}
				ajax.biaodiscqiye({
					member_id: this.userInfo ? this.userInfo.id : 0,
					bd_id: this.biaodiId,
					isquxiao: idx
				}).then(res => {
					if (res.code == 1) {
						this.$message.success(res.msg)
						this.isShoucang = idx
					} else if (res.code == 5) {
						this.$message.error('请先登录')
						this.goLogin()
					} else {
						this.$message.error(res)
					}
				})
			},
			getpaimaihui() {
				ajax.paimaihuibd({
					pmh_id: this.paimaihuiId
				}).then(res => {
					this.biaodiId = res.data
					setTimeout(() => {
						this.$refs.homeright.creatQrCode()
					}, 800)
					this.getbiaodicanshu()
				})
			},
			getbiaodicanshu() {
				// 标的参数
				ajax.biaodiinfo({
					id: this.biaodiId
				}).then(res => {
					if(res.code == 1){
						this.dangqianjiaInit = res.data.bd_qipaijia
						this.mangpaichujiaLnajie = res.data.bd_qipaijia
						this.biaodInfo = res.data
						this.biaodiImgs = res.data.bd_images ? res.data.bd_images.split(',') : []

						this.province = area.area.province_list[res.data.province]
						this.city = area.area.city_list[res.data.city]
						this.district = area.area.county_list[res.data.district]

						this.jiajiadi = Number(res.data.bd_jiajiafudu).toFixed(2)
						this.jiajiazhong = Number(res.data.bd_jiajiafudu * 2).toFixed(2)
						this.jiajiagao = Number(res.data.bd_jiajiafudu * 3).toFixed(2)
						if (res.data.pmh_type === 4 || res.data.pmh_type === 5) {
							this.bd_jdnum = res.data.bd_jdnum
							if (res.data.bd_status === 5) {
								this.dangqianjiaInit = res.data.bd_chengjiaojia
							}
						}
						ajax.xq_bdlist({
								pmh_id: this.biaodInfo.pmh_id
							})
							.then(res => {
								this.biaodList = res.data
							})
						if (res.data.pmh_type == 2) {
							ajax.jianjialist({
								bd_id: this.biaodInfo.id,
							}).then(res => {
								this.chujiailist = res.data;
								if (res.data.length) {
									this.dangqianjiaInit = res.data[0].dangqianjia;
								}
							})
						} else if (res.data.pmh_type == 1 || res.data.pmh_type == 4) {
							ajax.chujiaList({
								bd_id: this.biaodInfo.id,
								page: this.page
							}).then(resp => {
								this.chujiailist = resp.data;
								if (resp.data.length && res.data.pmh_type == 1) {
									this.dangqianjiaInit = resp.data[0].dangqianjia;
								} else if (res.data.pmh_type === 4) {
									if (res.data.bd_status !== 4 && res.data.bd_status !== 5) {
									this.dangqianjiaInit = resp.data[0].dangqianjia
									} else {
									this.dangqianjiaInit = res.data.bd_chengjiaojia;
									}
								}
							})
						} else if (res.data.pmh_type == 3 || res.data.pmh_type == 5) {
							let md5Uid = md5(this.biaodInfo.id + "")
							md5Uid = md5Uid + 'bdhha'
							md5Uid = md5(md5Uid)
							md5Uid = md5Uid.substr(10, 5)
							md5Uid = 'a' + md5Uid
							if (this.biaodInfo.bd_status === 4 || this.biaodInfo.bd_status === 5 || this.biaodInfo
								.bd_status === 6) {
								ajax.yicixingChujialist({
									bd_id: this.biaodInfo.id,
									lunshu: md5Uid,
									page: this.page
								}).then(res => {
									if (res.code == 1) {
										this.chujiailist = res.data;
										if (res.data.length && res.data.pmh_type == 3) {
											this.chujiaListfuilter(res.data)
										}
									}
								})
							}
						}
						if (this.userInfo) {
							this.isBiaodiBoaming()
							ajax.biaodizybdsc({
									bd_id: this.biaodInfo.id,
									member_id: this.userInfo ? this.userInfo.id : 0,
								})
								.then(res => {
									this.isShoucang = res.code
								})
						}
						ajax.fayanList({
								bd_id: this.biaodInfo.id,
								pmh_id: this.biaodInfo.pmh_id,
								page: this.page
							})
							.then(res => {
								this.fayanList = res.data
							})
						if (this.biaodInfo.bd_status != 4 && this.biaodInfo.bd_status != 5 && this.biaodInfo
							.bd_status != 8) {
							this.initWebSocket()
							this.kaipapjishis()
						}
					}
				})
			},
			kaipapjishis() {
				this.kaipaijishi = setInterval(() => {

					let temp = time.sumAge(this.biaodInfo.sheng_time, this.biaodInfo.end_time)

					if (temp) this.info = temp

				}, 1000)
			},
			kaipaijieshu() {
				if (this.biaodInfo.bd_status > 3) {
					return
				}
				clearInterval(this.kaipaijishi)
				this.kaipaijishi = null
				this.kaipapjishis()
			},
			isBiaodiBoaming() {
				ajax.bmverify({
					bd_id: this.biaodInfo.id,
					parentid: this.biaodInfo.parentid,
					member_id: this.userInfo ? this.userInfo.id : 0,
				}).then(res => {
					if (res.code == 1) {
						if (res.data == 0) {
							this.baomingIndex = -1
							this.kanhuoIndex = -1
							this.zhengming = ''
							this.bm_id = 0
						} else {
							this.baomingIndex = res.data.status
							this.kanhuoIndex = res.data.kanhuo
							this.zhengming = res.data.zhengming
							this.bm_id = res.data.bm_id
							this.shoukuanInfo = res.data
							if (res.data.status == 1) {
								this.jingpaihaopai = res.data.jingpaihaopai
								this.jingpaihaopais = res.data.jingpaihaopai.substr(0, 3)
							}
						}
					}
				})
			},
			zhichu(jiade) {
				var linshijia = Number(this.dangqianjiaInit * 100 + jiade * 100).toFixed(0);
				this.jiage = linshijia / 100;
				this.chujia()
			},
			chujia() {
				var chadejia = Number(this.jiage * 100).toFixed(0) - Number(this.dangqianjiaInit * 100).toFixed(0);
				var linshi_jiajiadi = Number(this.jiajiadi * 100).toFixed(0)
				if (this.jiage <= this.biaodInfo.dangqianjia) {
					return this.$message.error('出价不能小于最高价')
				}
				if (chadejia % linshi_jiajiadi != 0) {
					return this.$message.error('加价必须是' + this.jiajiadi + '的整倍数')
				}
				chadejia = chadejia / 100
				var msg = "您确定要以" + this.jiage + "元的价格竞价此标的吗？";
				this.$confirm(msg, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '再想想',
					type: 'warning'
				}).then(() => {
					ajax.chujia_bd({
							bd_id: this.biaodInfo.id,
							member_id: this.userInfo ? this.userInfo.id : 0,
							jingpaihaopai: this.jingpaihaopai,
							chujia: this.jiage - this.dangqianjiaInit,
							dangqianjia: this.jiage
						})
						.then(res => {
							// this.fayanList = res.data
							if (res.code == 1) {
								this.dangqianjia = this.jiage;
								this.dangqianjiaInit = this.jiage;
								return this.$message.success(res.msg)
							} else {
								return this.$message.error(res.msg)
							}
						})
				}).catch(() => {

				});
			},
			jianjia(jiade) {
				var linshijia = Number(this.dangqianjiaInit * 100 - jiade * 100).toFixed(0);
				this.jiage = linshijia / 100;
				this.jianjiaRequest()
			},
			jianjiaRequest() {
				if (this.jiage >= this.dangqianjiaInit) {
					return this.$message.error('出价不能高于当前价')
				}
				var chadejia = Number(this.dangqianjiaInit * 100).toFixed(0) - Number(this.jiage * 100).toFixed(0);
				var linshi_jiajiadi = Number(this.jiajiadi * 100).toFixed(0)
				if (chadejia % linshi_jiajiadi != 0) {
					return this.$message.error('减价必须是' + this.jiajiadi + '的整倍数')
				}
				chadejia = chadejia / 100
				var msg = "您确定要以" + this.jiage + "元的价格竞价此标的吗？";
				this.$confirm(msg, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '再想想',
					type: 'warning'
				}).then(() => {
					ajax.jianjiabd({
							bd_id: this.biaodInfo.id,
							member_id: this.userInfo ? this.userInfo.id : 0,
							jingpaihaopai: this.jingpaihaopai,
							chujia: this.dangqianjiaInit - this.jiage,
							dangqianjia: this.jiage
						})
						.then(res => {
							// this.fayanList = res.data
							if (res.code == 1) {
								this.dangqianjia = this.jiage;
								this.dangqianjiaInit = this.jiage;
								return this.$message.success(res.msg)
							} else {
								return this.$message.error(res.msg)
							}
						})
				}).catch(() => {

				});
			},
			mangpaiChu() {
				if (this.jiage <= this.dangqianjiaInit * 1) {
					return this.$message.error('出价不能低于当前价')
				}
				var msg = "您确定要以" + this.jiage + "元的价格竞价此标的吗？";
				this.$confirm(msg, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '再想想',
					type: 'warning'
				}).then(() => {
					ajax.yicixingChujia({
							bd_id: this.biaodInfo.id,
							member_id: this.userInfo ? this.userInfo.id : 0,
							jingpaihaopai: this.jingpaihaopai,
							chujia: this.isChuMangNum,
							dangqianjia: this.jiage
						})
						.then(res => {
							if (res.code == 1) {
								this.dangqianjia = this.jiage;
								// this.dangqianjiaInit = this.jiage;
								return this.$message.success(res.msg)
							} else {
								return this.$message.error(res.msg)
							}
						})
				}).catch(() => {

				});
			},
			mangpaiChuZhiding() {
				if (this.jiage < this.biaodInfo.bd_qipaijia * 1) {
					return this.$message.error('出价不能低于起拍价')
				}
				var msg = "您确定要以" + this.jiage + "元的价格竞价此标的吗？";
				this.$confirm(msg, '提示', {
					confirmButtonText: '确定',
					cancelButtonText: '再想想',
					type: 'warning'
				}).then(() => {
					ajax.yicixingChujia({
							bd_id: this.biaodInfo.id,
							member_id: this.userInfo ? this.userInfo.id : 0,
							jingpaihaopai: this.jingpaihaopai,
							chujia: this.isChuMangNum,
							dangqianjia: this.jiage
						})
						.then(res => {
							if (res.code == 1) {
								this.dangqianjia = this.jiage;
								// this.dangqianjiaInit = this.jiage;
								return this.$message.success(res.msg)
							} else {
								return this.$message.error(res.msg)
							}
						})
				}).catch(() => {

				});
			},
			setBiaodiId(item) {
				let routeData = this.$router.resolve({ //核心语句
					path: '/auctionDetail/' + item.id, //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: item.id
					}
				})
				window.open(routeData.href, '_blank');
			},
			mouseMove(e) {
				let x = event.offsetX
				let y = event.offsetY
				// 层罩的左上角坐标位置，并对其进行限制：无法超出原图区域左上角
				let topX = (x - 180) < 0 ? 0 : (x - 180)
				let topY = (y - 180) < 0 ? 0 : (y - 180)
				// 对层罩位置再一次限制，保证层罩只能在原图区域空间内
				if (topX > 180) {
					topX = 180
				}
				if (topY > 180) {
					topY = 180
				}
				// 通过 transform 进行移动控制
				this.topStyle.transform = `translate(${topX}px,${topY}px)`
				this.r_img.transform = `translate(-${2*topX}px,-${2*topY}px)`
			},
			mouseLeave() {
				this.isShowImg = false
			},
			enterHandler() {
				this.isShowImg = true
			},
			goLogin() {
				this.$router.push({ //核心语句
					path: '/Login', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						isReject: false
					}
				})
			},
			
			kanhuoxieyi() {
				if (!this.userInfo) {
					this.$message.error('请先登录')
					this.goLogin()
				}
				ajax.kanhuo_bd({
						bd_id: this.biaodInfo.id,
						member_id: this.userInfo ? this.userInfo.id : 0,
						bm_id: this.bm_id,
						type: 1
					})
					.then(res => {
						if (res.code == 0) this.$message.error(res.msg)
						else {
							this.xieyiTime = null
							this.mioashu = 6
							this.isXieyiClick = false
							this.dialogView = false
							// 启用页面滚动
							document.body.style.overflow = '';
							
							this.isBiaodiBoaming()
							// this.baomingIndex = 1
							this.$message.success(res.msg)
						}
					})
					.catch(err => {
			
					})
			
			},
			
			gerenBaoming() {
				if (!this.userInfo) {
					this.$message.error('请先登录')
					this.goLogin()
				}
				if (!this.userInfo.enteruser) return this.$message.error('请先去认证个人信息')
				ajax.baoming_bd({
						bd_id: this.biaodInfo.id,
						member_id: this.userInfo ? this.userInfo.id : 0,
						parentid: this.biaodInfo.parentid,
						type: 1
					})
					.then(res => {
						if (res.code == 0) this.$message.error(res.msg)
						else {
							this.isBiaodiBoaming()
							// this.baomingIndex = 1
							this.$message.success(res.msg)
						}
					})
					.catch(err => {

					})

			},

			qiyeBaoming() {
				if (!this.userInfo) {
					this.$message.error('请先登录')
					this.goLogin()
				}
				if (!this.userInfo.enterqiye) {
					this.$message.error('请先去认证企业信息')
					return
				}
				ajax.baoming_bd({
						bd_id: this.biaodInfo.id,
						member_id: this.userInfo ? this.userInfo.id : 0,
						parentid: this.biaodInfo.parentid,
						type: 2
					})
					.then(res => {
						if (res.code == 0) this.$message.error(res.msg)
						else {
							this.isBiaodiBoaming()
							this.$message.success(res.msg)
						}
					}).catch(err => {

					})
			},
			setIndex(i) {
				this.biaodiImgsIndex = i
			},
			initWebSocket() {
				//初始化weosocket
				//const wsuri = "wss://wss.zhonghengpaimai.com/wss";
				 const wsuri = "wss://mai-wss.hghello.com/wss";
				this.websock = new WebSocket(wsuri);
				this.websock.onmessage = this.onMessage;
				this.websock.onopen = this.onOpen;
				this.websock.onerror = this.onError;
				this.websock.onclose = this.onClose;
			},
			chujiaListfuilter(temp) {
				let oneIndex = []
				temp.forEach(el => {
					if (el.dangqianjia !== '***') {
						oneIndex.push(el)
					}
				})
				let maxValue = Math.max.apply(Math, oneIndex.map(item => {
					if (item.dangqianjia !== '***') {
						console.log(item.dangqianjia)
						return item.dangqianjia
					}
				}))
				this.bd_jdnum = '无'
				oneIndex.forEach(el => {
					if (el.dangqianjia == maxValue) {
						this.dangqianjiaInit = el.dangqianjia
						if (this.bd_jdnum == '无') this.bd_jdnum = el.jingpaihaopai
						else this.bd_jdnum += ',' + el.jingpaihaopai
					}
				})
			},
			onMessage(data) {
				let temp = JSON.parse(data.data)
				console.log(temp)
				if (temp) {
					if (this.biaodInfo.pmh_type == 4) {
						if (this.biaodInfo.bd_status !== temp.bd_info.bd_status) {
							// 标的参数
							ajax.biaodiinfo({
								id: this.biaodiId
							}).then(res => {
								this.bd_jdnum = res.data.bd_jdnum
								if (res.data.bd_status === 5) {
									this.dangqianjiaInit = res.data.bd_chengjiaojia
								}
							})
						}
					}
					if (this.biaodInfo.pmh_type == 3 || this.biaodInfo.pmh_type == 5) {
						if (this.isChuMangNum < temp.bd_info.lunshu) {
							ajax.yicixingChujialist({
								bd_id: this.biaodInfo.id,
								chujia: temp.bd_info.lunshu,
								page: this.page
							}).then(res => {
								if (res.code == 1) {
									this.chujiailist = res.data;
									if (res.data.length) {
										this.chujiaListfuilter(res.data)
									}
								}
							})
						}
						if (this.biaodInfo.bd_status !== temp.bd_info.bd_status) {
							if (temp.bd_info.bd_status === 4 || temp.bd_info.bd_status === 5 || temp.bd_info.bd_status ===
								6) {
								let md5Uid = md5(this.biaodInfo.id + "")
								md5Uid = md5Uid + 'bdhha'
								md5Uid = md5(md5Uid)
								md5Uid = md5Uid.substr(10, 5)
								md5Uid = 'a' + md5Uid
								ajax.yicixingChujialist({
									bd_id: this.biaodInfo.id,
									lunshu: md5Uid,
									page: this.page
								}).then(res => {
									this.chujiailist = res.data;
									if (res.data.length) {
										this.chujiaListfuilter(res.data)
									}
								})
							} else {
								ajax.yicixingChujialist({
									bd_id: this.biaodInfo.id,
									chujia: temp.bd_info.lunshu + 1,
									page: this.page
								}).then(res => {
									if (res.code == 1) {
										this.chujiailist = res.data;
										if (res.data.length) {
											this.chujiaListfuilter(res.data)
										}
									}
								})
							}
						}
						this.isChuMangNum = temp.bd_info.lunshu
						// this.dangqianjiaInit = temp.bd_info.dangqianjia
					}
					this.biaodInfo.bd_status = temp.bd_info.bd_status
					if (temp.bd_info.bd_status != 4 && temp.bd_info.bd_status != 5 && temp.bd_info.bd_status != 8) {
						this.biaodInfo.end_time = temp.fuwutime
						this.biaodInfo.sheng_time = temp.bd_info.sheng_time
					}
					this.isPaimaishi = temp.bd_info.sheng_time_chuo - temp.fuwutime < 0 ? true : false

					if (temp.fayan) {

						this.bofang = "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/888.mp3"
						if (temp.fayan.length > 1) {
							this.bofang = "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/chang888.mp3"
						}
						var ttsAudio = document.getElementById('audio');
						ttsAudio.load();
						ttsAudio.play();

						temp.fayan.forEach(el => {
							this.fayanList.unshift(el)
						})
					}
					if (temp.chujia) {
						this.bofang = "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/666.mp3"
						if (temp.chujia.length > 1) {
							this.bofang = "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/chang666.mp3"
						}
						var ttsAudio = document.getElementById('audio');
						ttsAudio.load();
						ttsAudio.play();

						if (this.chujiailist === '') {
							console.log('111')
							this.chujiailist = []
						}
						temp.chujia.forEach(el => {
							this.chujiailist.unshift(el)
							if (this.biaodInfo.pmh_type !== 3 && this.biaodInfo.pmh_type !== 5) {
								this.dangqianjiaInit = el.dangqianjia
							}
						})
					}
				}
			},
			onOpen() {
				this.isOnline = true

				this.jishi = setInterval(() => {
					let actions = {
						pmh_id: this.biaodInfo.pmh_id,
						bd_id: this.biaodInfo.id,
						user_id: this.userInfo ? this.userInfo.id : 0,
						fayan_id: this.fayanList.length == 0 ? 0 : this.fayanList[0].id,
						chujia_id: this.chujiailist.length == 0 ? 0 : this.chujiailist[0].id,
					}

					this.websocketsend(JSON.stringify(actions));
				}, 1000)

			},
			onError(e) {},
			onClose() {
				this.isOnline = false
				this.websock = null
				// location.reload(0)
				// 断开重连
				// this.initWebSocket()
			},
			websocketsend(Data) { //数据发送
				this.websock.send(Data);
			},
			goQiye() {
				this.$router.push({ //核心语句
					path: '/auctionfirmDetail', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: this.biaodInfo.parentid
					}
				})
			},
			oneTitleLeftClick() {
				if (this.biaodList.length > 5) {
					if (this.biaodListIndex != 0) {
						if (this.biaodList.length - 5 > this.biaodListIndex) {
							this.biaodListIndex += 5
						}
					}
				}
			},
			oneTitlerightClick(e) {
				if (this.biaodList.length > 5) {
					if (this.biaodList.length > this.biaodListIndex * -1 + 5) {
						if (this.biaodList.length - 5 > this.biaodListIndex * 1) {
							this.biaodListIndex -= 5
						}
					}
				}
			},
			oneTitleLeftClicks() {
				if (this.biaodiImgs.length > 4) {
					if (this.biaodListIndexs != 0) {
						if (this.biaodiImgs.length - 4 > this.biaodListIndexs) {
							this.biaodListIndexs += 4
						}
					}
				}
			},
			oneTitlerightClicks(e) {
				console.log(this.biaodiImgs)
				if (this.biaodiImgs.length > 4) {
					if (this.biaodiImgs.length > this.biaodListIndexs * -1 + 4) {
						if (this.biaodiImgs.length - 4 > this.biaodListIndexs * 1) {
							this.biaodListIndexs -= 4
						}
					}
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.shuaxin {
		margin-bottom: 10px;
		padding: 10px;
		box-sizing: border-box;
		background-color: #A80012;
		color: #FFFFFF;
		font-weight: 600;
		width: 80px;
		font-size: 18px;
		text-align: center;
		border-radius: 5px;
		cursor: pointer;
	}

	.auctionDetailc {
		background-color: #F7F7F7;
	}

	.auctionDetail_c {
		width: 1200px;
		margin: 0 auto;
		min-height: 1000px;
		padding-top: 20px;
		padding-bottom: 50px;
		box-sizing: border-box;
	}

	.xieyiClick {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
	}

	.auctionDetail_c_one {
		height: 200px;
		display: flex;
		-js-display: flex;
		align-items: center;
		background-color: #FFFFFF;
		position: relative;
	}

	.auctionDetail_c_one_img {
		position: absolute;
		top: 0;
		left: 20px;
		width: 66px;
		height: 24px;
		background-color: orange;
		text-align: center;
		line-height: 24px;
		font-size: 14px;
		color: #FFFFFF;
	}

	.auctionDetail_c_one_imgs {
		position: absolute;
		top: 0;
		left: 20px;
		width: 66px;
		height: 24px;
		background-color: rgb(49, 107, 204);
		text-align: center;
		line-height: 24px;
		font-size: 14px;
		color: #FFFFFF;
	}

	.auctionDetail_c_one_left {
		width: 318px;
		height: 101px;
		font-size: 20px;
		/* font-weight: bold; */
		color: #333333;
		margin-left: 20px;
	}

	.auctionDetail_c_one_left_s {
		font-weight: bold;
	}

	.auctionDetail_c_one_left_w {
		font-size: 12px;
		color: #333333;
		margin-top: 8px;
	}

	.auctionDetail_c_one_left_da:hover .auctionDetail_c_one_left_w {
		font-size: 12px;
		color: #A80012;
		margin-top: 8px;
	}

	.auctionDetail_c_one_right {
		width: 800px;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		margin-left: 0px;
	}

	.auctionDetail_c_one_right>img {
		width: 15px;
		height: 38px;
		margin: 0 5px;
	}

	.auctionDetail_c_one_right_div {
		width: 720px;
		overflow: hidden;
	}

	.auctionDetail_c_one_right_div_one {
		min-width: 720px;
		display: flex;
		-js-display: flex;
		align-items: center;
		position: relative;
		left: -15px;
		transition: all 0.3s;
	}

	.auctionDetail_c_one_right_div_one>div {
		cursor: pointer;
		width: 130px;
	}

	.auctionDetail_c_one_right_div_one_divss {
		margin-left: 16px;
		position: relative;
	}

	.auctionDetail_c_one_right_div_one_divss_dd {
		width: 100%;
		position: absolute;
		left: 0;
		bottom: 0;
		background-color: #A80012;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 12px;
		font-weight: 400;
		color: #FFFFFF;
		padding: 0 5px;
		box-sizing: border-box;
		/* margin: 2px 1px 0 1px; */
	}

	.auctionDetail_c_one_right_div_one_divss_ddaa {
		width: 100%;
		position: absolute;
		left: 0;
		bottom: 0;
		background-color: #169173;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 12px;
		font-weight: 400;
		color: #FFFFFF;
		padding: 0 5px;
		box-sizing: border-box;
		/* margin: 2px 1px 0 1px; */
	}

	.auctionDetail_c_one_right_div_one_divss_ddss {
		width: 100%;
		position: absolute;
		left: 0;
		bottom: 0;
		background-color: #919191;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 12px;
		font-weight: 400;
		color: #FFFFFF;
		padding: 0 5px;
		box-sizing: border-box;
		/* margin: 2px 1px 0 1px; */
	}

	.auctionDetail_c_one_right_div_one_divsss {
		border: 1px solid #A80012;
	}

	.auctionDetail_c_one_right_div_one_title {
		position: relative;
		width: 130px;
		height: 90px;
		box-sizing: border-box;
	}

	.auctionDetail_c_one_right_div_one_titles {
		font-size: 12px;
		font-weight: 400;
		color: #333333;
		text-align: center;
		width: 130px;
		height: 15px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-top: 8px;
	}

	.auctionDetail_c_two {
		margin-top: 19px;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auctionDetail_c_two_left {
		width: 880px;
		height: 600px;
		background: #FFFFFF;
	}

	.auctionDetail_c_two_left_one {
		padding-top: 19px;
		padding-left: 20px;
		box-sizing: border-box;
		font-size: 18px;
		font-weight: bold;
		color: #333333;
	}

	.auctionDetail_c_two_left_two {
		display: flex;
		-js-display: flex;
		margin-top: 17px;
		margin-left: 20px;
	}

	.auctionDetail_c_two_left_two_left {
		position: relative;
	}

	.auctionDetail_c_two_left_two_leftssssss {
		width: 360px;
		height: 360px;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auctionDetail_c_two_left_two_leftss {
		width: 360px;
		height: 360px;
		position: absolute;
		background-color: #FFFFFF;
		right: -360px;
		top: 0;
		z-index: 222;
		overflow: hidden;
	}

	.auctionDetail_c_two_left_two_leftss img {
		position: absolute;
		width: 720px;
		height: 720px;

	}

	.auctionDetail_c_two_left_two_left_o {
		width: 360px;
		height: 360px;
		background: #F6F6F6;
		display: flex;
		-js-display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		flex-shrink: 0;
	}

	.auctionDetail_c_two_left_two_left_omaskTop {
		width: 360px;
		height: 360px;
		position: absolute;
		z-index: 1;
		top: 0;
		left: 0;
	}

	.auctionDetail_c_two_left_two_left_omaskToptop {
		width: 180px;
		height: 180px;
		background-color: lightcoral;
		opacity: 0.4;
		position: absolute;
		top: 0;
		left: 0;
	}

	.auctionDetail_c_two_left_two_left_omaskTopright {
		width: 360px;
		height: 360px;
		border: 1px solid #A80012;
		position: relative;
		right: 0;
		z-index: 10;
		overflow: hidden;
		background-color: #FFFFFF;
		flex-shrink: 0;
		display: flex;
		-js-display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.auctionDetail_c_two_left_two_left_omaskToprightrightImg {
		display: block;
		width: 720px;
		height: auto;
		position: absolute;
		top: 0;
		left: 0;
		transform: scale(2);
	}

	.auctionDetail_c_two_left_two_left_o img {
		width: auto;
		height: auto;
		max-width: 100%;
		max-height: 100%;
	}

	.auctionDetail_c_two_left_two_left_s {
		width: 362px;
		height: 89px;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.auctionDetail_c_two_left_two_left_s img {
		width: 18px;
		height: 35px;
	}

	.auctionDetail_c_two_left_two_left_s_divs {
		width: 100%;
		display: flex;
		align-items: center;
		transition: all 0.3s;
	}

	.auctionDetail_c_two_left_two_left_s_div {
		width: 300px;
		display: flex;
		align-items: center;
		overflow: hidden;
	}

	.auctionDetail_c_two_left_two_left_s_div img {
		width: 65px;
		height: 60px;
		margin-right: 10px;
		flex-shrink: 0;
	}

	.auctionDetail_c_two_left_two_left_w {
		display: flex;
		-js-display: flex;
		align-items: center;
		margin-top: 16px;
	}

	.auctionDetail_c_two_left_two_left_w>div {
		display: flex;
		-js-display: flex;
		align-items: center;
		font-size: 14px;
		margin-right: 10px;
		width: 110px;
	}

	.auctionDetail_c_two_left_two_left_w>div img {
		margin-right: 5px;
	}

	.auctionDetail_c_two_left_two_right {
		width: 450px;
		margin-left: 30px;
	}

	.auctionDetail_c_two_left_two_right_one {
		width: 450px;
		height: 50px;
		position: relative;
	}
	
	.auctionDetail_c_two_left_two_right_one>img{
		width: 450px;
	}

	.auctionDetail_c_two_left_two_right_one_chengjiaobeii {
		width: 450px;
		height: 248px;
		position: absolute;
	}

	.auctionDetail_c_two_left_two_right>img {
		width: 450px;
		height: 50px;
	}

	.auctionDetail_c_two_left_two_right_one_w {
		display: flex;
		-js-display: flex;
		align-items: center;
		width: 450px;
		height: 50px;
		position: absolute;
		top: 0;
		left: 0;
	}

	.auctionDetail_c_two_left_two_right_one_w_a {
		width: 50px;
		height: 50px;
		background: rgb(18, 116, 92);
		font-size: 14px;
		font-weight: 400;
		color: #FFFFFF;
		display: flex;
		flex-direction: column;
		justify-content: center;
		-js-display: flex;
		align-items: center;
	}

	.auctionDetail_c_two_left_two_right_one_w_as {
		width: 50px;
		height: 50px;
		background: #808080;
		font-size: 14px;
		font-weight: 400;
		color: #FFFFFF;
		display: flex;
		flex-direction: column;
		justify-content: center;
		-js-display: flex;
		align-items: center;
	}

	.auctionDetail_c_two_left_two_right_one_w_ass {
		width: 50px;
		height: 50px;
		background: #A80012;
		font-size: 14px;
		font-weight: 400;
		color: #FFFFFF;
		display: flex;
		flex-direction: column;
		justify-content: center;
		-js-display: flex;
		align-items: center;
	}

	.auctionDetail_c_two_left_two_right_one_w_b {
		display: flex;
		-js-display: flex;
		align-items: center;
		height: 50px;
		line-height: 50px;
		font-size: 15px;
		color: white;
		padding-left: 10px;
		box-sizing: border-box;
	}

	.auctionDetail_c_two_left_two_right_one_w_bs {
		color: #FFFCA5;
	}

	.auctionDetail_c_two_left_two_right_one_w_bsb {
		font-size: 24px;
		font-weight: bold;
		color: #FFFCA5;
		margin: 0 2px;
	}

	.auctionDetail_c_two_left_two_right_one_tw {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		margin-top: 15px;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.auctionDetail_c_two_left_two_right_one_tw span {
		color: #A80012;
	}

	.auctionDetail_c_two_left_two_right_one_thre {
		width: 450px;
		height: 248px;
		background: #F7F7F7;
		margin-top: 15px;
		position: relative;
	}

	.auctionDetail_c_two_left_two_right_one_thre_one {
		display: flex;
		-js-display: flex;
		align-items: flex-end;
		justify-content: space-between;
		/* padding-top: 15px; */
		padding-left: 15px;
		padding-right: 15px;
		box-sizing: border-box;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
	}

	.auctionDetail_c_two_left_two_right_one_thre_one>div>span {
		font-size: 30px;
		font-weight: bold;
		color: #A80012;
	}

	.auctionDetail_c_two_left_two_right_one_thre_ones {
		min-width: 97px;
		height: 20px;
		text-align: center;
		line-height: 20px;
		/* background-color: #FFFFFF; */
		background: url(../assets/auction/chujiaback.png) no-repeat;
		font-size: 13px;
		font-weight: 400;
		color: #666666;
		margin-left: 5px;
	}

	.auctionDetail_c_two_left_two_right_one_thre_oneType {
		min-width: 97px;
		height: 20px;
		text-align: center;
		line-height: 20px;
		background-color: #FFFFFF;
		font-size: 13px;
		font-weight: 400;
		color: #666666;
		margin-left: 5px;
		padding: 0 5px;
		border: 1px solid #CCCCCC;
	}

	.auctionDetail_c_two_left_two_right_one_thre_two {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 25px;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.auctionDetail_c_two_left_two_right_one_thre_two>div {
		width: 120px;
		height: 40px;
		background: #A80012;
		border-radius: 4px;
		text-align: center;
		line-height: 40px;
		font-size: 16px;
		font-weight: 400;
		color: #FFFFFF;
		cursor: pointer;
	}

	.auctionDetail_c_two_left_two_right_one_thre_three {
		width: 256px;
		height: 40px;
		background: #FFFFFF;
		border-radius: 4px;
		text-align: center;
		line-height: 40px;
		margin: 0 auto;
		font-size: 16px;
		font-weight: 400;
		color: #333333;
		margin-top: 18px;
		cursor: pointer;
	}

	.auctionDetail_c_two_left_two_right_one_thre_chujia {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 30px;
	}

	.auctionDetail_c_two_left_two_right_one_thre_chujias {
		width: 160px;
		height: 40px;
		background: #FFFFFF;
		border: 1px solid #E6E6E6;
		border-radius: 4px;
	}

	.auctionDetail_c_two_left_two_right_one_thre_chujiass {
		width: 104px;
		height: 40px;
		background: #A80012;
		border-radius: 4px;
		text-align: center;
		line-height: 40px;
		font-size: 18px;
		font-weight: bold;
		color: #FFFFFF;
		margin-left: 15px;
		cursor: pointer;
	}

	.auctionDetail_c_two_left_two_right_one_thre_chujia_bottom {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 18px;
	}

	.auctionDetail_c_two_left_two_right_one_thre_chujia_bottom div {
		min-width: 86px;
		height: 40px;
		background: #A80012;
		border-radius: 4px;
		padding: 0 10px;
		box-sizing: border-box;
		text-align: center;
		line-height: 40px;
		font-size: 18px;
		font-weight: bold;
		color: #FFFFFF;
		margin: 0 5px;
		cursor: pointer;
	}

	.auctionDetail_c_two_left_two_right_one_thre_chujias input {
		width: 160px;
		height: 40px;
		padding-left: 15px;
		box-sizing: border-box;
	}

	.auctionDetail_c_two_left_two_right_one_thre_four {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
		font-size: 12px;
		font-weight: 400;
		color: #d7363b;
		margin-top: 25px;

	}

	.auctionDetail_c_two_left_two_right_one_thre_fours {
		display: flex;
		-js-display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		font-weight: 400;
		color: #A80012;
		margin-top: 30px;
	}

	.auctionDetail_c_two_left_two_right_onefour {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		font-size: 12px;
		color: #999;
		margin-top: 24px;
	}

	.auctionDetail_c_two_left_two_right_onefour>div {
		margin-bottom: 20px;
		min-width: 33.33%;
		height: 12px;
	}

	.auctionDetail_c_two_left_two_right_onefour>div span {
		color: #000;
	}

	.auctionDetail_c_two_right {
		width: 306px;
		height: 600px;
		margin-left: 14px;
	}

	.auctionDetail_c_two_right_a {
		width: 306px;
		height: 450px;
		background: #FFFFFF;
	}

	.auctionDetail_c_two_right_a_a {
		width: 306px;
		height: 36px;
		background: #A80012;
		text-align: center;
		line-height: 36px;
		font-size: 14px;
		font-weight: 400;
		color: #FEFEFE;
	}

	.auctionDetail_c_two_right_a_b {
		width: 306px;
		height: 414px;
		overflow-y: scroll;
	}

	.auctionDetail_c_two_right_a_b_div {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 25px 21px 0;
		box-sizing: border-box;
		font-size: 13px;
		font-weight: 400;
		color: #333333;
	}

	.auctionDetail_c_two_right_a_b_div>div:nth-child(2) {
		color: #777777;
		font-size: 13px;
	}

	.auctionDetail_c_two_right_a_b_divs {
		width: 248px;
		background: #F2F2F2;
		border-radius: 4px;
		margin: 0 auto;
		font-size: 13px;
		font-weight: 400;
		color: #5A5A5A;
		padding: 11px 12px;
		box-sizing: border-box;
		margin-top: 12px;
	}

	.auctionDetail_c_two_right_b {
		width: 306px;
		height: 136px;
		background: #FFFFFF;
		margin-top: 13px;
	}

	.auctionDetail_c_two_right_b_title {
		width: 306px;
		height: 32px;
		background: #A80012;
		font-size: 13px;
		font-weight: 400;
		color: #FFFFFF;
		display: flex;
		-js-display: flex;
		align-items: center;
		position: relative;
	}

	.auctionDetail_c_two_right_b_title_img {
		position: absolute;
		width: 10px;
		height: 12px;
		left: 10px;
		top: 50%;
		transform: translateY(-50%);
	}

	.auctionDetail_c_two_right_b_titlea {
		width: 30%;
		height: 32px;
		text-align: center;
		line-height: 32px;
	}

	.auctionDetail_c_two_right_b_titleb {
		width: 20%;
		height: 32px;
		text-align: center;
		line-height: 32px;
	}

	.auctionDetail_c_two_right_b_titlec {
		width: 50%;
		height: 32px;
		text-align: center;
		line-height: 32px;
	}

	.auctionDetail_c_two_right_b_titles {
		height: 102px;
		font-size: 13px;
		font-weight: 400;
		color: #5A5A5A;
		text-align: center;
		line-height: 40px;
		overflow-y: auto;
	}



	/* 底部 */
	.auctionDetail_c_three {
		margin-top: 14px;
	}

	.auctionDetail_c_three_one {
		height: 60px;
		background-color: #FFFFFF;
		border-bottom: 2px solid #A80012;
		box-sizing: border-box;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auctionDetail_c_three_one>div {
		width: 160px;
		height: 60px;
		text-align: center;
		line-height: 60px;
		cursor: pointer;
	}

	.auctionDetail_c_three_ones {
		font-size: 20px;
		font-weight: 400;
		color: #000;
	}

	.auctionDetail_c_three_oness {
		width: 160px;
		height: 60px;
		background: #A80012;
		font-size: 20px;
		font-weight: 400;
		color: #FFFFFF;
		text-align: center;
		line-height: 60px;
	}

	.auctionDetail_c_three_oness span {
		color: #FFFFFF !important;
	}

	.auctionDetail_c_three_ones:hover {
		width: 160px;
		height: 60px;
		background: #A80012;
		font-size: 20px;
		font-weight: 400;
		color: #FFFFFF;
		text-align: center;
		line-height: 60px;
	}

	.auctionDetail_c_three_ones:hover span {
		color: white !important;
	}

	.auctionDetail_c_three_two {
		width: 100%;
		padding: 42px 25px;
		box-sizing: border-box;
		background-color: #FFFFFF;
	}

	.chujialist {
		width: 100%;
		height: 100px;
		border-top: 1px solid #e5e5e5;
		border-left: 1px solid #e5e5e5;
	}

	.chujialist tr td {
		width: 333px;
		background-color: #fff;
		word-break: break-all;
		border-right: 1px solid #e5e5e5;
		border-bottom: 1px solid #e5e5e5;
		padding: 8px 0 8px 20px;
		color: #333333;
	}
	.toLogin{
		text-align: center;
		/* line-height: 20px; */
		/* font-size: 16px; */
		color: black;
	}
	.agreement_title{
		color: #333;
		font-size: 24px;
		font-weight: bold;
		text-align: center;
	}
	.agreement_item{
		display: flex;
		flex-wrap: wrap;
		width: 100%;
		margin-top: 15px;
		font-size: 19px;
		color: #333;
		// font-family: 仿宋;
		line-height: 30px;
		box-sizing: border-box;
		.item_label{
			width: 130px;
			text-indent: 2rem;
		}
		.item_view{
			position: relative;
			width: 300px;
			padding-left: 15px;
			// text-indent: 0rem;
			border-bottom: 1px solid;
		}
	}
	.agreement_text{
		margin: 15px 0;
		// padding: 0 20px;
		font-size: 19px;
		color: #333;
		// font-family: 仿宋;
		line-height: 30px;
		text-indent: 2rem;
	}
	.text_bottomline{
		position: relative;
		padding: 0 20px;
		border-bottom: 1px solid;
	}
</style>
