<template>
  <div class="steps-container">
    <div class="steps-wrapper">
      <div
        v-for="(step, index) in steps"
        :key="index"
        class="step-item"
        :class="{
          'step-active': index === current,
          'step-completed': index < current,
          'step-clickable': index < current
        }"
        @click="handleStepClick(index)"
      >
        <!-- 步骤图标 -->
        <div class="step-icon">
          <div v-if="index < current" class="step-icon-completed">
            <SvgIcon iconName="check" className="check-icon" />
          </div>
          <div v-else class="step-icon-number">{{ index + 1 }}</div>
        </div>

        <!-- 步骤内容 -->
        <div class="step-content">
          <div class="step-title">{{ step.title }}</div>
          <div class="step-description">{{ step.description }}</div>
        </div>

        <!-- 连接线 -->
        <div v-if="index < steps.length - 1" class="step-connector"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from '@/components/SvgIcon.vue'

interface Step {
  title: string
  description: string
}

interface Props {
  current: number
  steps: Step[]
}

interface Emits {
  (e: 'change', step: number): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const handleStepClick = (index: number) => {
  // 只允许点击已完成的步骤
  if (index < props.current) {
    emit('change', index)
  }
}
</script>

<style scoped lang="scss">
.steps-container {
  width: 100%;
  padding: 20px 0;
}

.steps-wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  min-width: 0;

  &.step-clickable {
    cursor: pointer;

    &:hover .step-icon {
      transform: scale(1.1);
    }
  }
}

.step-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;

  .step-icon-number {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 600;
    background-color: #f5f5f5;
    color: #999999;
    border: 2px solid #e5e5e5;
  }

  .step-icon-completed {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #004C66;
    border: 2px solid #004C66;

    .check-icon {
      width: 20px;
      height: 20px;
      color: #ffffff;
    }
  }
}

.step-item.step-active .step-icon .step-icon-number {
  background-color: #004C66;
  color: #ffffff;
  border-color: #004C66;
}

.step-content {
  text-align: center;
  max-width: 150px;

  .step-title {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 4px;
    line-height: 1.4;
  }

  .step-description {
    font-size: 14px;
    color: #666666;
    line-height: 1.4;
  }
}

.step-item.step-active .step-content .step-title {
  color: #004C66;
}

.step-connector {
  position: absolute;
  top: 20px;
  left: 50%;
  right: -50%;
  height: 2px;
  background-color: #e5e5e5;
  z-index: 1;
}

.step-item.step-completed .step-connector {
  background-color: #004C66;
}

// 响应式设计
@media (max-width: 768px) {
  .steps-wrapper {
    flex-direction: column;
    align-items: flex-start;
    max-width: 100%;
  }

  .step-item {
    flex-direction: row;
    align-items: center;
    width: 100%;
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .step-icon {
    margin-bottom: 0;
    margin-right: 16px;
    flex-shrink: 0;
  }

  .step-content {
    text-align: left;
    max-width: none;
    flex: 1;
  }

  .step-connector {
    display: none;
  }
}
</style>
