<template>
  <div
    class="auction_three_div"
    @click="goDetail(data)"
  >
    <div class="auction_three_img">
      <img :src="url + data.pmh_pic" @error="(e)=>e.target.src = data.pmh_pic ? 'https://oss.yunpaiwang.com/'+data.pmh_pic : ''" />
      <div
        v-if="data.pmh_xingshi == 2"
        class="homeCentet_three_left_two_ri_backRight"
        style="background: #316ccb"
      >
        <div>线上竞价</div>
      </div>
      <div
        v-if="data.pmh_xingshi == 1"
        class="homeCentet_three_left_two_ri_backRight"
      >
        <div>同步竞价</div>
      </div>
    </div>
    <div class="auction_three_div_right">
        <div class="auction_three_div_right_one">{{ data.pmh_name }}</div>
      <div class="auction_three_div_right_five">
        <div class="auction_three_div_right_five_o">
          <div v-if="data.pmh_status == 2">
            结束时间：{{ data.end_time_name }}
          </div>
          <div v-else>开拍时间：{{ data.start_time_name }}</div>
        </div>
        <div class="auction_three_div_right_five_i">查看详情</div>
      </div>
      <div class="auction_three_div_right_four">
      	<div class="biaodiMulu" v-if="isdetail" @click.stop="biaodiInfo(data)">标的目录</div>
        <div
          class="auction_three_div_right_four_cont"
          style="margin-left: 64px"
        >
          <img src="@/assets/images/index/redu.png" />
          <div>
            <span>{{ data.biaodinums }}</span>
            个标的
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import ajax from "@/store/Ajax";
export default {
  props: {
    data: {
      type: Object,
      default: {},
    },
    isdetail:{
      type: Boolean,
      default:true
    }
    // key: {
    //   type: Number,
    //   default: 0,
    // },
  },
  data() {
    return {
      url: this.$Pc.ossUrl,
      // USER_INFO: null,
    };
  },
  filters: {
    formatDate: function (value) {
      value = value * 1000;
      let date = new Date(value);
      // console.log(date)
      let y = date.getFullYear();
      // console.log(y)
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? "0" + MM : MM;
      let d = date.getDate();
      d = d < 10 ? "0" + d : d;
      let h = date.getHours();
      h = h < 10 ? "0" + h : h;
      let m = date.getMinutes();
      m = m < 10 ? "0" + m : m;
      let s = date.getSeconds();
      s = s < 10 ? "0" + s : s;
      return MM + "月" + d + "日" + h + ":" + m + ":" + s;
    },
  },
  created() {
    // if (localStorage.getItem("userInfo")) {
    //   this.USER_INFO = JSON.parse(localStorage.getItem("userInfo"));
    // }
  },
  methods: {
    goDetail(item) {
      let routeData = this.$router.resolve({ //核心语句
        path: '/auctionDetail/' + item.id, //跳转的路径
        query: { //路由传参时push和query搭配使用 ，作用时传递参数
          id: item.id,
          ispaimaih: true
        }
      })
      window.open(routeData.href, '_blank');
    },
    biaodiInfo(item) {
      let routeData = this.$router.resolve({ //核心语句
        path: '/targetMulu', //跳转的路径
        query: { //路由传参时push和query搭配使用 ，作用时传递参数
          id: item.id,
        }
      })
      window.open(routeData.href, '_blank');
    },
  },
};
</script>
<style lang="scss" scoped>
	.auction_three_div {
		position: relative;
		width: 290px;
		height: 350px;
		// margin-right: 12px;
		background: #FFFFFF;
		border-radius: 8px;
		margin-bottom: 16px;
		transition: all .2s;
		overflow: hidden;
		cursor: pointer;
	}
	.auction_three_div:hover{
		// background-color: #f8f8f8;
  		box-shadow: 2px 2px 10px 2px rgba(0,0,0,.1);
	}

	.auction_three_img {
		position: relative;
		display: flex;
		align-items: center;
		width: 290px;
		height: 196px;
		overflow: hidden;
	}

	.auction_three_img>img {
		width: 100%;
	}
	
	.homeCentet_three_left_two_ri_backRight {
		position: absolute;
		left: 0;
		top: 15px;
		width: 80px;
		height: 30px;
		background: #A80012;
		border-radius: 0px 100px 100px 0px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #FFFFFF;
		line-height: 30px;
		text-align: center;
	}

	.auction_three_div_right {
		position: relative;
    	box-sizing: border-box;
	}
	.auction_right_info{
		padding-top: 12px;
		padding-bottom: 24px;
		border-bottom: 1px solid #EDEDED;
	}

	.auction_three_div_right_one {
		margin: 15px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 16px;
		color: #333333;
		line-height: 25px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.auction_three_div_right_two {
		margin-top: 15px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 16px;
		color: #666666;
		line-height: 20px;
	}

	.auction_three_div_right_three {
		padding-left: 20px;
		box-sizing: border-box;
		color: #000000;
		font-size: 16px;
		font-weight: 600;
		margin-top: 20px;
	}

	.biaodiMulu {
		width: 86px;
		height: 30px;
		background: #F9664F;
		border-radius: 4px;
		font-size: 14px;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 400;
		color: #FFFFFF;
		text-align: center;
		line-height: 30px;
		cursor: pointer;
	}

	.auction_three_div_right_three span {
		color: #7A7A7A;
		font-weight: 400;
	}

	.auction_three_div_right_four {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		width: 266px;
		height: 48px;
		margin: 20px auto 0;
		border-top: 1px solid #E9E9E9;
		font-size: 14px;
		font-weight: 400;
		color: #7A7A7A;
		box-sizing: border-box;
	}
	.auction_three_div_right_four_cont{
		display: flex;
		-js-display: flex;
		align-items: center;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 17px;
	}
	.auction_three_div_right_four_cont img{
		width: 16px;
		height: 16px;
		margin-right: 3px;
	}
	.auction_three_div_right_four_cont span{
		font-size: 16px;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 400;
		color: #333333;
		line-height: 25px;
	}

	.auction_three_div_right_img {
		width: 128px;
		height: 103px;
		position: absolute;
		right: -40px;
		top: 30%;
		transform: translateX(-50%);
	}

	.auction_three_div_right_five {
		position: relative;
		display: flex;
		-js-display: flex;
		align-items: center;
		width: 280px;
		height: 30px;
	}

	.auction_three_div_right_five_o {
		position: absolute;
		z-index: 1;
		left: 0px;
		top: 0;
		width: 200px;
		height: 30px;
		padding-left: 15px;
		background: linear-gradient(90deg, #FB6952 3%, #CC120D 100%);
		border-radius: 0px 22px 22px 0px;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #fff;
		line-height: 30px;
		box-sizing: border-box;
	}


	.auction_three_div_right_five_i {
		position: absolute;
		z-index: 0;
		right: 0px;
		top: 0;
		width: 314px;
		height: 30px;
		padding-right: 20px;
		background: linear-gradient(118deg, #F9F9F9 0%, #FFC993 100%);
		border-radius: 0px 22px 22px 0px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 600;
		font-size: 12px;
		color: #CC0319;
		text-align: right;
		line-height: 30px;
		box-sizing: border-box;
	}
</style>