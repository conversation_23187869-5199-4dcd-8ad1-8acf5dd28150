<template>
  <div class="supply-step1-container">
    <h3 class="step-title">第一步：基本信息</h3>
    <div class="step-content">
      <!-- 这里将放置第一步的表单内容 -->
      <div class="placeholder">
        <p>供应信息基本信息表单</p>
        <p>包含：物料类型、物料名称、规格等基础信息</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 第一步组件逻辑
</script>

<style scoped lang="scss">
.supply-step1-container {
  .step-title {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 20px;
  }

  .step-content {
    .placeholder {
      padding: 40px;
      background-color: #f8f9fa;
      border-radius: 8px;
      text-align: center;
      border: 1px dashed #dee2e6;

      p {
        margin: 8px 0;
        color: #6c757d;
        font-size: 14px;
      }
    }
  }
}
</style>