<template>
  <div class="step-panel">
    <!-- 使用ElementPlus表单布局 -->
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="left"
      label-width="120px"
      :scroll-to-error="true"
    >
      <!-- 供应物资类型 -->
      <div class="form-section">
        <h3 class="section-title">供应物资类型</h3>
        <div class="form-row">
          <el-form-item prop="materialType" class="material-type-item">
            <el-cascader
              v-model="formData.materialType"
              :options="materialTypeOptions"
              :props="cascaderProps"
              placeholder="请选择物资类型"
              size="large"
              filterable
              clearable
              class="material-type-cascader"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 基本供应信息 -->
      <div class="form-section">
        <h3 class="section-title">基本供应信息</h3>

        <!-- 信息标题独占一行 -->
        <div class="form-row">
          <el-form-item label="信息标题" prop="basicInfo.infoTitle" class="info-title-item">
            <el-input
              v-model="formData.basicInfo.infoTitle"
              placeholder="格式建议:地区+供应+设备名称"
              size="large"
            />
          </el-form-item>
        </div>

        <!-- 物资品牌、物资型号、新旧程度三项在一行 -->
        <div class="form-row basic-three-row">
          <el-form-item label="物资品牌" prop="basicInfo.brand" class="basic-three-item">
            <el-input
              v-model="formData.basicInfo.brand"
              placeholder="请输入物资品牌"
              size="large"
            />
          </el-form-item>
          <el-form-item label="物资型号" prop="basicInfo.model" class="basic-three-item">
            <el-input
              v-model="formData.basicInfo.model"
              placeholder="请输入物资型号"
              size="large"
            />
          </el-form-item>
          <el-form-item label="新旧程度" prop="basicInfo.depreciationDegree" class="basic-three-item">
            <el-select
              v-model="formData.basicInfo.depreciationDegree"
              placeholder="请选择新旧程度"
              size="large"
            >
              <el-option :value="1" label="一成新" />
              <el-option :value="2" label="二成新" />
              <el-option :value="3" label="三成新" />
              <el-option :value="4" label="四成新" />
              <el-option :value="5" label="五成新" />
              <el-option :value="6" label="六成新" />
              <el-option :value="7" label="七成新" />
              <el-option :value="8" label="八成新" />
              <el-option :value="9" label="九成新" />
            </el-select>
          </el-form-item>
        </div>
      </div>

      <!-- 规格与存放 -->
      <div class="form-section">
        <h3 class="section-title">规格与存放</h3>

        <!-- 省市区选择框第一行 -->
        <div class="form-row location-row">
          <div class="location-selects">
            <el-form-item prop="specification.province" class="location-area-item">
              <el-cascader
                v-model="locationValue"
                :options="locationOptions"
                :props="locationProps"
                placeholder="请选择省市区"
                size="large"
                clearable
                @change="handleLocationChange"
              />
            </el-form-item>
          </div>
        </div>

        <!-- 其他项第二行 -->
        <div class="form-row specification-second-row">
          <!-- 存放方式 -->
          <el-form-item label="存放方式" prop="specification.storageMethod" class="specification-item">
            <el-select
              v-model="formData.specification.storageMethod"
              placeholder="请选择存放方式"
              size="large"
            >
              <el-option :value="1" label="仓库" />
              <el-option :value="2" label="露天堆放" />
            </el-select>
          </el-form-item>

          <!-- 物资数量 -->
          <el-form-item label="物资数量" prop="specification.quantity" class="specification-item">
            <el-input-number
              v-model="formData.specification.quantity"
              placeholder="请输入数量"
              style="width: 100%"
              size="large"
              :min="1"
            />
          </el-form-item>

          <!-- 物资单位 -->
          <el-form-item label="物资单位" prop="specification.unit" class="specification-item">
            <el-select
              v-model="formData.specification.unit"
              placeholder="请选择单位"
              size="large"
            >
              <el-option value="台" label="台" />
              <el-option value="辆" label="辆" />
              <el-option value="套" label="套" />
              <el-option value="个" label="个" />
              <el-option value="件" label="件" />
              <el-option value="批" label="批" />
              <el-option value="米" label="米" />
              <el-option value="吨" label="吨" />
              <el-option value="公斤" label="公斤" />
            </el-select>
          </el-form-item>

          <!-- 物资价格 -->
          <el-form-item label="物资价格" prop="specification.price" class="specification-item">
            <el-input-number
              v-model="formData.specification.price"
              placeholder="请输入价格"
              style="width: 100%"
              size="large"
              :min="0"
              :precision="2"
              controls-position="right"
            />
          </el-form-item>
        </div>
      </div>

      <!-- 亮点与展示 -->
      <div class="form-section">
        <h3 class="section-title">亮点与展示</h3>

        <!-- 物资照片上传 -->
        <div class="form-row">
          <el-form-item label="物资照片" prop="display.images" class="upload-item">
            <div class="upload-container">
              <el-upload
                v-model:file-list="imageFileList"
                action="#"
                list-type="picture-card"
                :auto-upload="false"
                :limit="15"
                accept="image/*"
                @change="handleImageChange"
              >
                <el-icon><Plus /></el-icon>
              </el-upload>
              <div class="upload-tip">建议尺寸800*800像素，最多上传15张</div>
            </div>
          </el-form-item>
        </div>

        <!-- 物资视频上传 -->
        <div class="form-row">
          <el-form-item label="物资视频" prop="display.videos" class="upload-item">
            <div class="upload-container">
              <el-upload
                v-model:file-list="videoFileList"
                action="#"
                :auto-upload="false"
                :limit="1"
                accept="video/*"
                @change="handleVideoChange"
              >
                <el-button type="primary">选择视频</el-button>
              </el-upload>
              <div class="upload-tip">建议视频宽高比16:9，突出商品核心卖点，时长9~30秒</div>
            </div>
          </el-form-item>
        </div>

        <!-- 供应亮点 -->
        <div class="form-row">
          <el-form-item label="供应亮点" prop="display.highlights" class="highlights-item">
            <el-input
              v-model="formData.display.highlights"
              placeholder="请输入供应亮点"
              size="large"
            />
          </el-form-item>
        </div>

        <!-- 物资详细描述 -->
        <div class="form-row">
          <el-form-item label="物资详细描述" prop="display.materialDesc" class="material-desc-item">
            <div class="rich-text-container">
              <TiptapEditor
                v-model="formData.display.materialDesc"
                placeholder="请详细描述物资信息..."
                :auto-focus="false"
              />
            </div>
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, UploadFile } from 'element-plus'
import TiptapEditor from '@/components/TiptapEditor/index.vue'
import { getMaterialTree } from '@/api/supplyDemand'
import type { MaterialTypeNode } from '@/api/supplyDemand'

// 定义 Props
interface Props {
  modelValue: {
    materialType: string
    basicInfo: {
      infoTitle: string
      brand: string
      model: string
      depreciationDegree: number
    }
    specification: {
      province: string
      city: string
      area: string
      storageMethod: number
      quantity: string
      unit: string
      price: string
    }
    display: {
      images: string
      videos: string
      highlights: string
      materialDesc: string
    }
    attachmentList: any[]
  }
  locationLoading?: boolean
  isEditMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  locationLoading: false,
  isEditMode: false
})

// 定义 Emits
interface Emits {
  (e: 'update:modelValue', value: Props['modelValue']): void
  (e: 'area-change', value: any): void
  (e: 'get-current-location'): void
}

const emit = defineEmits<Emits>()

// 表单引用
const formRef = ref<FormInstance>()

// 物资类型选项
const materialTypeOptions = ref<MaterialTypeNode[]>([])

// 地区选项和值
const locationOptions = ref<any[]>([])
const locationValue = ref<string[]>([])

// 文件列表
const imageFileList = ref<UploadFile[]>([])
const videoFileList = ref<UploadFile[]>([])

// 计算属性：表单数据
const formData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 级联选择器配置
const cascaderProps = {
  value: 'id',
  label: 'name',
  children: 'children',
  checkStrictly: true,
  emitPath: false
}

// 地区级联选择器配置
const locationProps = {
  value: 'code',
  label: 'name',
  children: 'children'
}

// 表单校验规则
const formRules = {
  materialType: [
    { required: true, message: '请选择物资类型', trigger: 'change' }
  ],
  'basicInfo.infoTitle': [
    { required: true, message: '请输入信息标题', trigger: 'blur' },
    { min: 2, max: 100, message: '信息标题长度应在2-100个字符之间', trigger: 'blur' }
  ],
  'basicInfo.depreciationDegree': [
    { required: true, message: '请选择新旧程度', trigger: 'change' }
  ],
  'specification.province': [
    { required: true, message: '请选择省份', trigger: 'change' }
  ],
  'specification.storageMethod': [
    { required: true, message: '请选择存放方式', trigger: 'change' }
  ],
  'specification.quantity': [
    { required: true, message: '请输入物资数量', trigger: 'blur' }
  ],
  'specification.unit': [
    { required: true, message: '请选择物资单位', trigger: 'change' }
  ],
  'specification.price': [
    { required: true, message: '请输入物资价格', trigger: 'blur' }
  ]
}

// 获取物资类型树形数据
const fetchMaterialTypeTree = async () => {
  try {
    const result = await getMaterialTree()
    console.log('获取物资类型树形数据:', result)
    if (result && result.success && Array.isArray(result.result)) {
      materialTypeOptions.value = result.result
    }
  } catch (error) {
    console.error('获取物资类型失败:', error)
  }
}

// 处理地区变化
const handleLocationChange = (value: string[]) => {
  if (value && value.length >= 3) {
    formData.value.specification.province = value[0]
    formData.value.specification.city = value[1]
    formData.value.specification.area = value[2]
  } else {
    formData.value.specification.province = ''
    formData.value.specification.city = ''
    formData.value.specification.area = ''
  }
  emit('area-change', value)
}

// 处理图片上传变化
const handleImageChange = (fileList: UploadFile[]) => {
  imageFileList.value = fileList
  // 这里可以处理图片上传逻辑
  const imageData = fileList.map(file => ({
    fileName: file.name,
    filePath: file.url || '',
    fileSize: file.size || 0
  }))
  formData.value.display.images = JSON.stringify(imageData)
}

// 处理视频上传变化
const handleVideoChange = (fileList: UploadFile[]) => {
  videoFileList.value = fileList
  // 这里可以处理视频上传逻辑
  const videoData = fileList.map(file => ({
    fileName: file.name,
    filePath: file.url || '',
    fileSize: file.size || 0
  }))
  formData.value.display.videos = JSON.stringify(videoData)
}

// 表单验证方法
const validateForm = async (): Promise<boolean> => {
  try {
    await formRef.value?.validate()
    return true
  } catch (error) {
    console.error('表单验证失败:', error)
    return false
  }
}

// 清除表单验证
const clearValidate = () => {
  formRef.value?.clearValidate()
}

// 组件挂载时获取物资类型数据
onMounted(() => {
  fetchMaterialTypeTree()

  // 初始化地区数据（这里可以根据实际需求加载地区数据）
  locationOptions.value = [
    // 这里应该是实际的地区数据，暂时用示例数据
    {
      code: '110000',
      name: '北京市',
      children: [
        {
          code: '110100',
          name: '北京市',
          children: [
            { code: '110101', name: '东城区' },
            { code: '110102', name: '西城区' }
          ]
        }
      ]
    }
  ]
})

// 暴露方法给父组件
defineExpose({
  validateForm,
  clearValidate
})
</script>

<style scoped lang="scss">
.step-panel {
  background: #fff;
  border-radius: 8px;
}

.form-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 16px;
  padding-bottom: 8px;

  &::before {
    content: '';
    display: block;
    width: 4px;
    height: 18px;
    margin-right: 8px;
    background-color: #004c66;
  }
}

/* 表单行布局 */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
  gap: 20px;
}

/* 物资类型选择 - 只占三分之一 */
.material-type-item {
  flex: 0 0 calc(33.333% - 14px);
  min-width: 300px;
}

/* 信息标题 - 占满一行 */
.info-title-item {
  width: 100%;
}

/* 基本信息三项在一行 */
.basic-three-row {
  gap: 20px;
}

.basic-three-item {
  flex: 1;
  min-width: calc(33.333% - 14px);
}

/* 省市区选择行 */
.location-row {
  gap: 20px;
  align-items: flex-start;
}

/* 省市区选择区域 */
.location-selects {
  flex: 1;
}

.location-area-item {
  width: 100%;

  :deep(.el-cascader) {
    width: 100%;
  }
}

/* 规格信息第二行 */
.specification-second-row {
  gap: 20px;
}

.specification-item {
  flex: 1;
  min-width: calc(25% - 15px);
}

/* 上传相关样式 */
.upload-item {
  width: 100%;
}

.upload-container {
  width: 100%;

  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #999;
    line-height: 1.4;
  }
}

/* 供应亮点 */
.highlights-item {
  width: 100%;
}

/* 物资详细描述 */
.material-desc-item {
  width: 100%;
}

.rich-text-container {
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .basic-three-item,
  .specification-item {
    min-width: calc(50% - 10px);
  }
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 16px;
  }

  .basic-three-item,
  .specification-item,
  .material-type-item {
    width: 100%;
    min-width: auto;
  }
}
</style>