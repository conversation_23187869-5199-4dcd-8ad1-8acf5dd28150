var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function r(t){if(Object.prototype.hasOwnProperty.call(t,"__esModule"))return t;var r=t.default;if("function"==typeof r){var e=function t(){return this instanceof t?Reflect.construct(r,arguments,this.constructor):r.apply(this,arguments)};e.prototype=r.prototype}else e={};return Object.defineProperty(e,"__esModule",{value:!0}),Object.keys(t).forEach(function(r){var i=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,i.get?i:{enumerable:!0,get:function(){return t[r]}})}),e}var e={exports:{}};const i=r(Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"})));
/**
 * [js-md5]{@link https://github.com/emn178/js-md5}
 *
 * @namespace md5
 * @version 0.8.3
 * <AUTHOR> Yi-Cyuan [<EMAIL>]
 * @copyright Chen, Yi-Cyuan 2014-2023
 * @license MIT
 */
var s;var n,h=(s||(s=1,n=e,function(){var r="input is invalid type",e="object"==typeof window,s=e?window:{};s.JS_MD5_NO_WINDOW&&(e=!1);var h=!e&&"object"==typeof self,o=!s.JS_MD5_NO_NODE_JS&&"object"==typeof process&&process.versions&&process.versions.node;o?s=t:h&&(s=self);var a,f=!s.JS_MD5_NO_COMMON_JS&&n.exports,u=!s.JS_MD5_NO_ARRAY_BUFFER&&"undefined"!=typeof ArrayBuffer,c="0123456789abcdef".split(""),p=[128,32768,8388608,-**********],y=[0,8,16,24],l=["hex","array","digest","buffer","arrayBuffer","base64"],d="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),b=[];if(u){var v=new ArrayBuffer(68);a=new Uint8Array(v),b=new Uint32Array(v)}var w=Array.isArray;!s.JS_MD5_NO_NODE_JS&&w||(w=function(t){return"[object Array]"===Object.prototype.toString.call(t)});var _=ArrayBuffer.isView;!u||!s.JS_MD5_NO_ARRAY_BUFFER_IS_VIEW&&_||(_=function(t){return"object"==typeof t&&t.buffer&&t.buffer.constructor===ArrayBuffer});var A=function(t){var e=typeof t;if("string"===e)return[t,!0];if("object"!==e||null===t)throw new Error(r);if(u&&t.constructor===ArrayBuffer)return[new Uint8Array(t),!1];if(!w(t)&&!_(t))throw new Error(r);return[t,!1]},g=function(t){return function(r){return new M(!0).update(r)[t]()}},O=function(t){var e,n=i,h=i.Buffer;return e=h.from&&!s.JS_MD5_NO_BUFFER_FROM?h.from:function(t){return new h(t)},function(i){if("string"==typeof i)return n.createHash("md5").update(i,"utf8").digest("hex");if(null==i)throw new Error(r);return i.constructor===ArrayBuffer&&(i=new Uint8Array(i)),w(i)||_(i)||i.constructor===h?n.createHash("md5").update(e(i)).digest("hex"):t(i)}},B=function(t){return function(r,e){return new S(r,!0).update(e)[t]()}};function M(t){if(t)b[0]=b[16]=b[1]=b[2]=b[3]=b[4]=b[5]=b[6]=b[7]=b[8]=b[9]=b[10]=b[11]=b[12]=b[13]=b[14]=b[15]=0,this.blocks=b,this.buffer8=a;else if(u){var r=new ArrayBuffer(68);this.buffer8=new Uint8Array(r),this.blocks=new Uint32Array(r)}else this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];this.h0=this.h1=this.h2=this.h3=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}function S(t,r){var e,i=A(t);if(t=i[0],i[1]){var s,n=[],h=t.length,o=0;for(e=0;e<h;++e)(s=t.charCodeAt(e))<128?n[o++]=s:s<2048?(n[o++]=192|s>>>6,n[o++]=128|63&s):s<55296||s>=57344?(n[o++]=224|s>>>12,n[o++]=128|s>>>6&63,n[o++]=128|63&s):(s=65536+((1023&s)<<10|1023&t.charCodeAt(++e)),n[o++]=240|s>>>18,n[o++]=128|s>>>12&63,n[o++]=128|s>>>6&63,n[o++]=128|63&s);t=n}t.length>64&&(t=new M(!0).update(t).array());var a=[],f=[];for(e=0;e<64;++e){var u=t[e]||0;a[e]=92^u,f[e]=54^u}M.call(this,r),this.update(f),this.oKeyPad=a,this.inner=!0,this.sharedMemory=r}M.prototype.update=function(t){if(this.finalized)throw new Error("finalize already called");var r=A(t);t=r[0];for(var e,i,s=r[1],n=0,h=t.length,o=this.blocks,a=this.buffer8;n<h;){if(this.hashed&&(this.hashed=!1,o[0]=o[16],o[16]=o[1]=o[2]=o[3]=o[4]=o[5]=o[6]=o[7]=o[8]=o[9]=o[10]=o[11]=o[12]=o[13]=o[14]=o[15]=0),s)if(u)for(i=this.start;n<h&&i<64;++n)(e=t.charCodeAt(n))<128?a[i++]=e:e<2048?(a[i++]=192|e>>>6,a[i++]=128|63&e):e<55296||e>=57344?(a[i++]=224|e>>>12,a[i++]=128|e>>>6&63,a[i++]=128|63&e):(e=65536+((1023&e)<<10|1023&t.charCodeAt(++n)),a[i++]=240|e>>>18,a[i++]=128|e>>>12&63,a[i++]=128|e>>>6&63,a[i++]=128|63&e);else for(i=this.start;n<h&&i<64;++n)(e=t.charCodeAt(n))<128?o[i>>>2]|=e<<y[3&i++]:e<2048?(o[i>>>2]|=(192|e>>>6)<<y[3&i++],o[i>>>2]|=(128|63&e)<<y[3&i++]):e<55296||e>=57344?(o[i>>>2]|=(224|e>>>12)<<y[3&i++],o[i>>>2]|=(128|e>>>6&63)<<y[3&i++],o[i>>>2]|=(128|63&e)<<y[3&i++]):(e=65536+((1023&e)<<10|1023&t.charCodeAt(++n)),o[i>>>2]|=(240|e>>>18)<<y[3&i++],o[i>>>2]|=(128|e>>>12&63)<<y[3&i++],o[i>>>2]|=(128|e>>>6&63)<<y[3&i++],o[i>>>2]|=(128|63&e)<<y[3&i++]);else if(u)for(i=this.start;n<h&&i<64;++n)a[i++]=t[n];else for(i=this.start;n<h&&i<64;++n)o[i>>>2]|=t[n]<<y[3&i++];this.lastByteIndex=i,this.bytes+=i-this.start,i>=64?(this.start=i-64,this.hash(),this.hashed=!0):this.start=i}return this.bytes>4294967295&&(this.hBytes+=this.bytes/4294967296|0,this.bytes=this.bytes%4294967296),this},M.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var t=this.blocks,r=this.lastByteIndex;t[r>>>2]|=p[3&r],r>=56&&(this.hashed||this.hash(),t[0]=t[16],t[16]=t[1]=t[2]=t[3]=t[4]=t[5]=t[6]=t[7]=t[8]=t[9]=t[10]=t[11]=t[12]=t[13]=t[14]=t[15]=0),t[14]=this.bytes<<3,t[15]=this.hBytes<<3|this.bytes>>>29,this.hash()}},M.prototype.hash=function(){var t,r,e,i,s,n,h=this.blocks;this.first?r=((r=((t=((t=h[0]-680876937)<<7|t>>>25)-271733879|0)^(e=((e=(-271733879^(i=((i=(-1732584194^2004318071&t)+h[1]-117830708)<<12|i>>>20)+t|0)&(-271733879^t))+h[2]-1126478375)<<17|e>>>15)+i|0)&(i^t))+h[3]-1316259209)<<22|r>>>10)+e|0:(t=this.h0,r=this.h1,e=this.h2,r=((r+=((t=((t+=((i=this.h3)^r&(e^i))+h[0]-680876936)<<7|t>>>25)+r|0)^(e=((e+=(r^(i=((i+=(e^t&(r^e))+h[1]-389564586)<<12|i>>>20)+t|0)&(t^r))+h[2]+606105819)<<17|e>>>15)+i|0)&(i^t))+h[3]-1044525330)<<22|r>>>10)+e|0),r=((r+=((t=((t+=(i^r&(e^i))+h[4]-176418897)<<7|t>>>25)+r|0)^(e=((e+=(r^(i=((i+=(e^t&(r^e))+h[5]+1200080426)<<12|i>>>20)+t|0)&(t^r))+h[6]-1473231341)<<17|e>>>15)+i|0)&(i^t))+h[7]-45705983)<<22|r>>>10)+e|0,r=((r+=((t=((t+=(i^r&(e^i))+h[8]+1770035416)<<7|t>>>25)+r|0)^(e=((e+=(r^(i=((i+=(e^t&(r^e))+h[9]-1958414417)<<12|i>>>20)+t|0)&(t^r))+h[10]-42063)<<17|e>>>15)+i|0)&(i^t))+h[11]-1990404162)<<22|r>>>10)+e|0,r=((r+=((t=((t+=(i^r&(e^i))+h[12]+1804603682)<<7|t>>>25)+r|0)^(e=((e+=(r^(i=((i+=(e^t&(r^e))+h[13]-40341101)<<12|i>>>20)+t|0)&(t^r))+h[14]-1502002290)<<17|e>>>15)+i|0)&(i^t))+h[15]+1236535329)<<22|r>>>10)+e|0,r=((r+=((i=((i+=(r^e&((t=((t+=(e^i&(r^e))+h[1]-165796510)<<5|t>>>27)+r|0)^r))+h[6]-1069501632)<<9|i>>>23)+t|0)^t&((e=((e+=(t^r&(i^t))+h[11]+643717713)<<14|e>>>18)+i|0)^i))+h[0]-373897302)<<20|r>>>12)+e|0,r=((r+=((i=((i+=(r^e&((t=((t+=(e^i&(r^e))+h[5]-701558691)<<5|t>>>27)+r|0)^r))+h[10]+38016083)<<9|i>>>23)+t|0)^t&((e=((e+=(t^r&(i^t))+h[15]-660478335)<<14|e>>>18)+i|0)^i))+h[4]-405537848)<<20|r>>>12)+e|0,r=((r+=((i=((i+=(r^e&((t=((t+=(e^i&(r^e))+h[9]+568446438)<<5|t>>>27)+r|0)^r))+h[14]-1019803690)<<9|i>>>23)+t|0)^t&((e=((e+=(t^r&(i^t))+h[3]-187363961)<<14|e>>>18)+i|0)^i))+h[8]+1163531501)<<20|r>>>12)+e|0,r=((r+=((i=((i+=(r^e&((t=((t+=(e^i&(r^e))+h[13]-1444681467)<<5|t>>>27)+r|0)^r))+h[2]-51403784)<<9|i>>>23)+t|0)^t&((e=((e+=(t^r&(i^t))+h[7]+1735328473)<<14|e>>>18)+i|0)^i))+h[12]-1926607734)<<20|r>>>12)+e|0,r=((r+=((n=(i=((i+=((s=r^e)^(t=((t+=(s^i)+h[5]-378558)<<4|t>>>28)+r|0))+h[8]-2022574463)<<11|i>>>21)+t|0)^t)^(e=((e+=(n^r)+h[11]+1839030562)<<16|e>>>16)+i|0))+h[14]-35309556)<<23|r>>>9)+e|0,r=((r+=((n=(i=((i+=((s=r^e)^(t=((t+=(s^i)+h[1]-1530992060)<<4|t>>>28)+r|0))+h[4]+1272893353)<<11|i>>>21)+t|0)^t)^(e=((e+=(n^r)+h[7]-155497632)<<16|e>>>16)+i|0))+h[10]-1094730640)<<23|r>>>9)+e|0,r=((r+=((n=(i=((i+=((s=r^e)^(t=((t+=(s^i)+h[13]+681279174)<<4|t>>>28)+r|0))+h[0]-358537222)<<11|i>>>21)+t|0)^t)^(e=((e+=(n^r)+h[3]-722521979)<<16|e>>>16)+i|0))+h[6]+76029189)<<23|r>>>9)+e|0,r=((r+=((n=(i=((i+=((s=r^e)^(t=((t+=(s^i)+h[9]-640364487)<<4|t>>>28)+r|0))+h[12]-421815835)<<11|i>>>21)+t|0)^t)^(e=((e+=(n^r)+h[15]+530742520)<<16|e>>>16)+i|0))+h[2]-995338651)<<23|r>>>9)+e|0,r=((r+=((i=((i+=(r^((t=((t+=(e^(r|~i))+h[0]-198630844)<<6|t>>>26)+r|0)|~e))+h[7]+1126891415)<<10|i>>>22)+t|0)^((e=((e+=(t^(i|~r))+h[14]-1416354905)<<15|e>>>17)+i|0)|~t))+h[5]-57434055)<<21|r>>>11)+e|0,r=((r+=((i=((i+=(r^((t=((t+=(e^(r|~i))+h[12]+1700485571)<<6|t>>>26)+r|0)|~e))+h[3]-1894986606)<<10|i>>>22)+t|0)^((e=((e+=(t^(i|~r))+h[10]-1051523)<<15|e>>>17)+i|0)|~t))+h[1]-2054922799)<<21|r>>>11)+e|0,r=((r+=((i=((i+=(r^((t=((t+=(e^(r|~i))+h[8]+1873313359)<<6|t>>>26)+r|0)|~e))+h[15]-30611744)<<10|i>>>22)+t|0)^((e=((e+=(t^(i|~r))+h[6]-1560198380)<<15|e>>>17)+i|0)|~t))+h[13]+1309151649)<<21|r>>>11)+e|0,r=((r+=((i=((i+=(r^((t=((t+=(e^(r|~i))+h[4]-145523070)<<6|t>>>26)+r|0)|~e))+h[11]-1120210379)<<10|i>>>22)+t|0)^((e=((e+=(t^(i|~r))+h[2]+718787259)<<15|e>>>17)+i|0)|~t))+h[9]-343485551)<<21|r>>>11)+e|0,this.first?(this.h0=t+1732584193|0,this.h1=r-271733879|0,this.h2=e-1732584194|0,this.h3=i+271733878|0,this.first=!1):(this.h0=this.h0+t|0,this.h1=this.h1+r|0,this.h2=this.h2+e|0,this.h3=this.h3+i|0)},M.prototype.hex=function(){this.finalize();var t=this.h0,r=this.h1,e=this.h2,i=this.h3;return c[t>>>4&15]+c[15&t]+c[t>>>12&15]+c[t>>>8&15]+c[t>>>20&15]+c[t>>>16&15]+c[t>>>28&15]+c[t>>>24&15]+c[r>>>4&15]+c[15&r]+c[r>>>12&15]+c[r>>>8&15]+c[r>>>20&15]+c[r>>>16&15]+c[r>>>28&15]+c[r>>>24&15]+c[e>>>4&15]+c[15&e]+c[e>>>12&15]+c[e>>>8&15]+c[e>>>20&15]+c[e>>>16&15]+c[e>>>28&15]+c[e>>>24&15]+c[i>>>4&15]+c[15&i]+c[i>>>12&15]+c[i>>>8&15]+c[i>>>20&15]+c[i>>>16&15]+c[i>>>28&15]+c[i>>>24&15]},M.prototype.toString=M.prototype.hex,M.prototype.digest=function(){this.finalize();var t=this.h0,r=this.h1,e=this.h2,i=this.h3;return[255&t,t>>>8&255,t>>>16&255,t>>>24&255,255&r,r>>>8&255,r>>>16&255,r>>>24&255,255&e,e>>>8&255,e>>>16&255,e>>>24&255,255&i,i>>>8&255,i>>>16&255,i>>>24&255]},M.prototype.array=M.prototype.digest,M.prototype.arrayBuffer=function(){this.finalize();var t=new ArrayBuffer(16),r=new Uint32Array(t);return r[0]=this.h0,r[1]=this.h1,r[2]=this.h2,r[3]=this.h3,t},M.prototype.buffer=M.prototype.arrayBuffer,M.prototype.base64=function(){for(var t,r,e,i="",s=this.array(),n=0;n<15;)t=s[n++],r=s[n++],e=s[n++],i+=d[t>>>2]+d[63&(t<<4|r>>>4)]+d[63&(r<<2|e>>>6)]+d[63&e];return t=s[n],i+=d[t>>>2]+d[t<<4&63]+"=="},S.prototype=new M,S.prototype.finalize=function(){if(M.prototype.finalize.call(this),this.inner){this.inner=!1;var t=this.array();M.call(this,this.sharedMemory),this.update(this.oKeyPad),this.update(t),M.prototype.finalize.call(this)}};var j=function(){var t=g("hex");o&&(t=O(t)),t.create=function(){return new M},t.update=function(r){return t.create().update(r)};for(var r=0;r<l.length;++r){var e=l[r];t[e]=g(e)}return t}();j.md5=j,j.md5.hmac=function(){var t=B("hex");t.create=function(t){return new S(t)},t.update=function(r,e){return t.create(r).update(e)};for(var r=0;r<l.length;++r){var e=l[r];t[e]=B(e)}return t}(),f?n.exports=j:s.md5=j}()),e.exports);export{h as m};
