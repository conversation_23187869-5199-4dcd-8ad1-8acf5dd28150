<template>
    <div class="hearder min_wrapper_1500">
      <div class="header_top_old" id="title">
        <div class="header_title_old">
          <div>您好，灰谷网欢迎您！</div>
          <div class="header_title_right">
            <div
              v-if="!userInfo"
              class="header_title_right_right"
              @click="reject"
            >
                免费注册
            </div>
            <div
              v-if="!userInfo"
              class="header_title_right_right"
              @click="goLogin"
            >
                竞买人登录
            </div>
            <!-- <div
              v-if="!userInfo"
              class="header_title_right_right"
              @click="goLogin"
            >
                委托方登录
            </div> -->
            <div v-if="userInfo" class="header_title_right_right" style="margin-right:0" @click="nextUserInfo">
              {{ userInfo.mobile }}
            </div>
            <div v-if="userInfo" class="header_title_right_right" @click="tuichu">
              退出
            </div>
            <div @click="nextEnter">拍卖企业入驻</div>
            <div @click="goHangye" style="margin-left:32px">行业资讯</div>
            <div @click="nextQuotation" style="margin-left:32px">钢价行情</div>
            <!-- <div class="header_title_right_right">
              <a
                href="https://paimai.yunpaiwang.com/"
                style="color: #999"
                target="_blank"
                >拍卖公司登录</a
              >
            </div> -->
            <!-- <div @click="nextAbout">关于我们</div> -->
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
//   import ajax from "@/store/Ajax";
  export default {
    name: "Home",
    props: ["inputShu"],
    data() {
      return {
        userInfo: null,
        pcUrl:this.$Pc.pcUrl
      };
    },
    created() {
      if (localStorage.getItem("userInfo")) {
        this.userInfo = JSON.parse(localStorage.getItem("userInfo"));
        console.log(this.userInfo,'uer')
      }
    },
    methods: {
			// 个人中心
			nextUserInfo() {
				if (localStorage.getItem("userInfo")) {
					this.$router.push("/userInfo");
				} else {
					this.$router.push({
					//核心语句
					path: "/login", //跳转的路径
					query: {
						//路由传参时push和query搭配使用 ，作用时传递参数
						isReject: false,
					},
					});
				}
			},
      reject() {
        if (this.$route.name == "Login") {
          this.$emit("setIsReject", true);
        } else {
          this.$router.push("/login");
        }
      },
      goLogin() {
        if (this.$route.name == "Login") {
          this.$emit("setIsReject", false);
        } else {
          this.$router.push({
            //核心语句
            path: "/login", //跳转的路径
            query: {
              //路由传参时push和query搭配使用 ，作用时传递参数
              isReject: false,
            },
          });
        }
      },
      goHangye() {
        this.$router.push({
          path: '/news',
          query: { //路由传参时push和query搭配使用 ，作用时传递参数
            cateid: 2
          }
        })
      },
      nextQuotation(){
        let routeData = this.$router.resolve("/Quotation");
        window.open(routeData.href, "_blank");
      },
      nextEnter() {
        this.$router.push("/enter");
      },
      tuichu() {
        localStorage.removeItem("userInfo");
        localStorage.removeItem("zgUserInfo");
        localStorage.removeItem("apptoken");
        this.userInfo = null;
        this.$message.success("退出登录成功");
        setTimeout(() => {
          this.$router.push("/#/");
        }, 1000);
      },
      nextAbout() {
        // return
        let routeData = this.$router.resolve("/about");
        window.open(routeData.href, "_blank");
      },   
    },
  };
  </script>
  <style type="text/css" scoped>
  body {
    margin: 0;
  }
  .header_top_old{
    background-color: #F2F2F2;
  }
  .header_title_old{
    display: flex;
    -js-display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1200px;
    height: 50px;
    margin: 0 auto;
    color: #666;
    font-size: 12px;
    font-weight: 400;
  }
  .header_title_right {
    display: flex;
    -js-display: flex;
    align-items: center;
    height: 24px;
  }
  .header_title_right_new{
    display: flex;
    -js-display: flex;
    align-items: center;
  }
  
  .header_title_right > div {
    cursor: pointer;
  }
  
  .header_title_right_right {
    padding-right: 6px;
    box-sizing: border-box;
    margin-right: 32px;
  }
  </style>
  