<template>
  <div class="property-detail">
    <!-- 上半部分：资产详情介绍 -->
    <div class="detail-section" ref="containerRef">
      <!-- 上部分：标题和观看数 -->
      <div class="header-section">
        <div class="breadcrumb">
          <template v-if="route.query.crumbsTitle !== '自由交易'">
            <span class="free-title" @click="handleGoBack('自由交易')"
              >自由交易</span
            >
            <span class="separator">></span>
          </template>
          <template v-else>
            <span class="free-title" @click="handleGoBack('other')">{{
              route.query.crumbsTitle
            }}</span>
            <span class="separator">></span>
          </template>
          <span class="current">{{ propertyData.productName }}</span>
        </div>
        <div class="header-right">
          <!-- 收藏 -->
          <div class="collect" @click="toggleCollect">
            <SvgIcon
              :iconName="
                isCollected
                  ? 'freedom-propertyDetail-checked-star'
                  : 'freedom-propertyDetail-star'
              "
              className="collect-icon"
            />
            <span class="collect-text">{{
              isCollected ? "已收藏" : "收藏"
            }}</span>
          </div>
          <div class="view-count">
            <SvgIcon
              iconName="freedom-propertyDetail-eye"
              className="eye-icon"
            />
            <span>{{ propertyData.viewCount }}</span>
          </div>
        </div>
      </div>

      <!-- 中部分：左中右结构 -->
      <div class="content-section">
        <!-- 左侧：图片列表 -->
        <div class="image-list-container">
          <!-- 向上滚动箭头 -->
          <div
            v-if="canScrollUp"
            class="scroll-arrow scroll-up"
            @click="scrollUp"
          >
            <div class="arrow-overlay"></div>
            <SvgIcon
              iconName="freedom-propertyDetail-down"
              className="scroll-icon scroll-icon-up"
            />
          </div>

          <!-- 图片列表 -->
          <div
            class="image-list"
            ref="imageListRef"
            @mousemove="handleScrollAreaMouseMove"
            @mouseleave="handleScrollAreaMouseLeave"
          >
            <div
              v-for="(image, index) in propertyData.images"
              :key="index"
              class="image-item"
              :class="{ active: currentImageIndex === index }"
              @mousemove="selectImage(index)"
            >
              <img :src="image" :alt="`图片${index + 1}`" />
            </div>
          </div>

          <!-- 向下滚动箭头 -->
          <div
            v-if="canScrollDown"
            class="scroll-arrow scroll-down"
            @click="scrollDown"
          >
            <div class="arrow-overlay"></div>
            <SvgIcon
              iconName="freedom-propertyDetail-down"
              className="scroll-icon"
            />
          </div>
        </div>

        <!-- 中间：图片预览 -->
        <div class="image-preview">
          <div class="preview-container">
            <!-- <div class="nav-btn prev-btn" @click="prevImage">
              <SvgIcon
                iconName="freedom-propertyDetail-arrow-right"
                className="arrow-icon arrow-icon-right"
              />
            </div> -->
            <div class="image-container">
              <img
                :src="propertyData.images[currentImageIndex]"
                :alt="propertyData.productName"
                @mouseenter="handleImageMouseEnter"
                @mouseleave="handleImageMouseLeave"
                @mousemove="handleImageMouseMove"
              />
              <!-- 放大镜蒙层 -->
              <div
                v-if="isImageHovered"
                class="magnifier-mask"
                :style="{
                  left: maskPosition.x + 'px',
                  top: maskPosition.y + 'px',
                  width: maskSize + 'px',
                  height: maskSize + 'px',
                }"
              ></div>
            </div>
            <!-- <div class="nav-btn next-btn" @click="nextImage">
              <SvgIcon
                iconName="freedom-propertyDetail-arrow-right"
                className="arrow-icon"
              />
            </div> -->
          </div>
          <!-- 放大图片显示区域 -->
          <div v-if="isImageHovered" class="magnified-view">
            <img
              :src="propertyData.images[currentImageIndex]"
              :alt="propertyData.productName"
              :style="{
                transform: `translate(-${maskPosition.translateX || 0}px, -${
                  maskPosition.translateY || 0
                }px)`,
              }"
            />
          </div>
        </div>

        <!-- 右侧：资产信息 -->
        <div class="property-info">
          <!-- 上部分：标题、副标题、地址 -->
          <div class="info-top">
            <div class="title">
              <div>
                <span class="title-status-tag">{{
                  propertyData.status === "ongoing" ? "求购" : "供应"
                }}</span>
                <span class="title-text">{{ propertyData.productName }}</span>
                <!-- 查看详情 -->
                <span class="detail-btn" @click="handleShowDetail">
                  查看详情
                  <SvgIcon
                    iconName="auction-arrows-right"
                    className="detail-icon"
                  ></SvgIcon>
                </span>
              </div>
            </div>
            <!-- 下部分：价格和按钮 -->
            <div class="info-price">
              <span class="currency">￥</span>
              <span class="price-integer">{{ priceInteger }}</span>
              <span class="price-decimal">.{{ priceDecimal }}</span>
              <span class="price-unit">元/吨</span>
            </div>
            <div class="parameters">
              <span>{{ propertyData.quantity }}台</span>
              <span>{{ propertyData.weight }}吨</span>
              <span>{{ propertyData.power }}</span>
            </div>
            <div class="location" v-for="item in 8">
              <SvgIcon
                iconName="freedom-propertyDetail-location"
                className="location-icon"
              />
              <span>{{ propertyData.location }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 下部分：详细信息导航 -->
      <div class="tabs-section">
        <!-- 固定导航栏 -->
        <div
          class="tabs-header"
          ref="tabsHeaderRef"
          :class="{ 'tabs-header-fixed': isTabsFixed }"
          :style="tabsHeaderStyle"
        >
          <div
            class="tab-item"
            :class="{ active: activeTab === 'userInfo' }"
            @click="scrollToSection('userInfo')"
          >
            <span>用户信息</span>
          </div>
          <div
            class="tab-item"
            :class="{ active: activeTab === 'assetHighlights' }"
            @click="scrollToSection('assetHighlights')"
          >
            <span>资产亮点</span>
          </div>
          <div
            class="tab-item"
            :class="{ active: activeTab === 'productParams' }"
            @click="scrollToSection('productParams')"
          >
            <span>产品参数</span>
          </div>
          <div
            class="tab-item"
            :class="{ active: activeTab === 'detailIntro' }"
            @click="scrollToSection('detailIntro')"
          >
            <span>详细介绍</span>
          </div>
        </div>

        <!-- 占位元素，防止固定定位时内容跳动 -->
        <div v-if="isTabsFixed" class="tabs-header-placeholder"></div>

        <!-- 内容区域 -->
        <div class="tabs-content" ref="tabsContentRef">
          <!-- 用户信息内容 -->
          <div
            class="content-section userinfo"
            id="userInfo"
            ref="userInfoRef"
            @click="showEnterpriseDetail('1')"
          >
            <div class="user-info-content">
              <div class="company-info">
                <div class="company-header">
                  <div class="company-logo">
                    <img
                      :src="propertyData.enterpriseLogo"
                      :alt="propertyData.enterpriseName"
                    />
                  </div>
                  <div class="company-details">
                    <div class="company-details-container">
                      <div class="company-title">
                        <span class="company-name">
                          {{ propertyData.enterpriseName }}
                        </span>
                        <div class="company-type">
                          <CompanyTag
                            :enterpriseType="propertyData.enterpriseType"
                          />
                        </div>
                        <!-- 关注按钮 -->
                        <div class="follow-btn">
                          <el-button type="primary" @click="toggleFollow">
                            + {{ isFollowed ? "已关注" : "关注" }}
                          </el-button>
                        </div>
                      </div>

                      <div class="company-rating">
                        <div class="rating">
                          <span class="rating-score">{{
                            propertyData.peopleCount
                          }}</span>
                          <span class="rating-text">账号</span>
                        </div>
                        <div class="rating">
                          <span class="rating-score">{{
                            propertyData.rating
                          }}</span>
                          <span class="rating-text">万粉丝</span>
                        </div>
                      </div>
                    </div>
                    <div class="company-description">
                      {{ propertyData.companyDescription }}
                    </div>
                  </div>
                  <div class="company-icon">
                    <SvgIcon
                      iconName="freedom-propertyDetail-down"
                      className="show-company-icon"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 资产亮点内容 -->
          <div
            class="content-section"
            id="assetHighlights"
            ref="assetHighlightsRef"
            style="margin-top: 40px"
          >
            <div class="section-content">
              <span class="section-title" ref="assetHighlightsTitleRef">
                资产亮点
              </span>
              <div class="section-body">
                <p>资产亮点内容区域，待完善...</p>
              </div>
            </div>
          </div>

          <!-- 产品参数内容 -->
          <div
            class="content-section"
            id="productParams"
            ref="productParamsRef"
          >
            <div class="section-content">
              <span class="section-title" ref="productParamsTitleRef">
                产品参数
              </span>
              <div class="section-body">
                <p>产品参数内容区域，待完善...</p>
              </div>
            </div>
          </div>

          <!-- 详细介绍内容 -->
          <div class="content-section" id="detailIntro" ref="detailIntroRef">
            <div class="section-content">
              <span class="section-title" ref="detailIntroTitleRef"
                >详细介绍</span
              >
              <div class="section-body">
                <p>详细介绍内容区域，待完善...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 下半部分：推荐资产 -->
    <div class="recommend-section">
      <div class="section-title">
        <div class="title-container">
          <SvgIcon
            iconName="freedom-propertyDetail-recommended-assets"
            className="title-icon"
          />
          <span>推荐资产</span>
        </div>
      </div>
      <div class="recommend-content">
        <div class="property-cards">
          <PropertyCard
            v-for="item in recommendItems.slice(0, 4)"
            :key="item.productId"
            v-bind="item"
            @click="handleRecommendClick"
          />
        </div>
      </div>
    </div>

    <!-- 查看资产详情弹窗 -->
    <Modal
      v-model="showDetail"
      title="查看详情"
      confirm-button-text="缴纳保证金"
    >
      <div class="detail-content">
        查看该资产详情，客户信息需要缴纳一定数额的保证金，请缴纳保证金后继续查看
      </div>
    </Modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import SvgIcon from "@/components/SvgIcon.vue";
import PropertyCard from "@/components/PropertyCard.vue";
import CompanyTag from "@/components/CompanyTag.vue";
import type { PropertyItem } from "@/types/property";
import { newApi } from "@/utils/api-new";

// 路由实例
const route = useRoute();
const router = useRouter();

// 当前图片索引
const currentImageIndex = ref(0);
const imageListRef = ref<HTMLElement>();
const canScrollUp = ref(false);
const canScrollDown = ref(false);

// 自动滚动相关变量
const autoScrollTimer = ref<number | null>(null);
const isAutoScrolling = ref<boolean>(false);
// 是否收藏
const isCollected = ref(false);
// 是否关注
const isFollowed = ref(false);
// 当前激活的标签页
const activeTab = ref("userInfo");
// 导航栏是否固定在顶部
const isTabsFixed = ref(false);
// 导航栏原始位置（用于计算是否需要固定）
const tabsOriginalTop = ref(0);
const containerRef = ref<HTMLElement | null>(null);
const tabsHeaderStyle = ref<Record<string, string>>({});

// 导航和内容区域的引用
const tabsHeaderRef = ref<HTMLElement>();
const tabsContentRef = ref<HTMLElement>();
const userInfoRef = ref<HTMLElement>();
const assetHighlightsRef = ref<HTMLElement>();
const productParamsRef = ref<HTMLElement>();
const detailIntroRef = ref<HTMLElement>();

// 各个section标题的引用
const assetHighlightsTitleRef = ref<HTMLElement>();
const productParamsTitleRef = ref<HTMLElement>();
const detailIntroTitleRef = ref<HTMLElement>();

// 图片放大镜相关
const isImageHovered = ref(false);
// 蒙层位置（相对于图片容器）
const maskPosition = ref({ x: 0, y: 0, translateX: 0, translateY: 0 });
// 蒙层尺寸
const maskSize = 100; // 蒙层的宽高（像素）

// 点击面包屑回到freedomHome
const handleGoBack = (title: string) => {
  if (title === "自由交易") {
    router.push({
      name: "freedomHome",
    });
  } else {
    router.back();
  }
};

// 加载状态
const loading = ref(false);

// 资产详情数据
const propertyData = ref({
  productId: "",
  productName: "某大型企业废旧物资一批——废旧变压器共计11台",
  location: "内蒙古自治区乌兰察布市商都县某某地址某某地址",
  quantity: "11",
  weight: "3.6",
  power: "3.6kW~10kW",
  currentPrice: 60000.0,
  viewCount: 0,
  status: "ongoing",
  enterpriseLogo:
    "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",
  enterpriseName: "天津水泥集团有限公司",
  enterpriseType: "国企",
  peopleCount: "1234567890",
  rating: 9.5,
  companyDescription:
    '天津水泥集团有限公司成立于1998年，是一家专业从事水泥生产的大型企业。公司位于天津市，占地面积约500亩，拥有员工1000余人。公司主要生产各种规格的水泥产品，年产能达到500万吨。公司秉承"质量第一，客户至上"的经营理念，致力于为客户提供优质的产品和服务。',
  images: [
    "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",
    "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png",
    "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250603/17489456557790.png",
    "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",
    "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png",
    "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png",
    "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250611/17496321666592.png",
  ],
});

/**
 * 获取资产详情数据
 * 根据type参数决定调用哪个接口
 * type为4或5时调用供求信息详情接口
 * type为2时调用资产处置详情接口
 */
const getPropertyDetail = async () => {
  const id = route.query.id as string;
  const type = route.query.type as string;
  
  if (!id) {
    ElMessage.error('缺少资产ID参数');
    return;
  }
  
  try {
    loading.value = true;
    let response;
    
    // 根据type参数调用不同的接口
    if (type === '4' || type === '5') {
      // 调用供求信息详情接口
      response = await newApi.getSupplyDemandDetail(id);
    } else if (type === '2') {
      // 调用资产处置详情接口
      response = await newApi.getAssetEntrustDetail(id);
    } else {
      // 默认调用供求信息详情接口
      response = await newApi.getSupplyDemandDetail(id);
    }
    
    if (response.success || response.code === 200) {
      const data = response.result;
      
      // 根据实际返回的数据结构更新propertyData
      // 映射API返回的数据字段到页面显示的数据结构
      if (data) {
        // 构建完整的地址信息
        const fullAddress = `${data.province || ''}${data.city || ''}${data.district || ''}${data.address || ''}`;
        
        // 处理图片列表，如果attachmentList存在则使用，否则使用默认图片
        let imageList = propertyData.value.images; // 使用默认图片
        if (data.attachmentList && Array.isArray(data.attachmentList) && data.attachmentList.length > 0) {
          imageList = data.attachmentList.map((item: any) => item.url || item);
        }
        
        propertyData.value = {
          productId: data.id || id,
          productName: data.infoTitle || propertyData.value.productName,
          location: fullAddress || propertyData.value.location,
          quantity: data.quantity ? data.quantity.toString() : propertyData.value.quantity,
          weight: data.weight ? data.weight.toString() : propertyData.value.weight,
          power: data.power || propertyData.value.power,
          currentPrice: data.price || propertyData.value.currentPrice,
          viewCount: data.viewNum,
          status: data.status === 6 ? 'completed' : (data.type === '5' ? 'purchase' : 'supply'),
          enterpriseLogo: data.enterpriseLogo || propertyData.value.enterpriseLogo,
          enterpriseName: data.enterpriseName || data.createBy || propertyData.value.enterpriseName,
          enterpriseType: data.enterpriseType || propertyData.value.enterpriseType,
          peopleCount: data.peopleCount || propertyData.value.peopleCount,
          rating: data.rating || propertyData.value.rating,
          companyDescription: data.materialDesc || data.highlights || propertyData.value.companyDescription,
          images: imageList,
        };
        
        // 更新页面标题显示的状态标签
        // 根据type字段设置状态：5为求购，4为供应
        if (data.type === '5') {
          // 求购类型
          propertyData.value.status = 'ongoing';
        } else if (data.type === '4') {
          // 供应类型  
          propertyData.value.status = 'supply';
        }
        
        // console.log('更新后的propertyData: ', propertyData.value);
      }
    } else {
      ElMessage.error(response.message || response.msg || '获取资产详情失败');
    }
  } catch (error) {
    console.error('获取资产详情失败:', error);
    ElMessage.error('获取资产详情失败，请稍后重试');
  } finally {
    loading.value = false;
  }
};

// 推荐资产数据
const recommendItems = ref<PropertyItem[]>([
  {
    productId: "3",
    productName: "200钢栈桥",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250710/17521325169594.png",
    currentPrice: 1700.0,
    priceUnit: "元/吨",
    statusName: "待发布",
    viewCount: 152,
    enterpriseLogo: "",
    enterpriseName: "湖北省宜昌市某企业",
    enterpriseType: "私企",
    status: "upcoming",
    productCount: 1,
    productCountUnit: "批",
    productWeight: 2,
    productWeightUnit: "吨",
  },
  {
    productId: "3",
    productName: "闲置铝芯电缆一批",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250522/17478958852404.png",
    currentPrice: 5000.0,
    priceUnit: "元/吨",
    statusName: "待发布",
    viewCount: 600,
    enterpriseLogo: "",
    enterpriseName: "河南省新乡市某机械厂",
    enterpriseType: "央企",
    status: "upcoming",
    productCount: 1,
    productCountUnit: "批",
    productWeight: 5,
    productWeightUnit: "吨",
  },
  {
    productId: "3",
    productName: "废旧工程车与废旧设备一批",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250707/17518775754193.png",
    currentPrice: 1600.0,
    priceUnit: "元/吨",
    statusName: "待发布",
    viewCount: 572,
    enterpriseLogo: "",
    enterpriseName: "辽宁省大连市某大型企业",
    enterpriseType: "民企",
    status: "ongoing",
    productCount: 1,
    productCountUnit: "批",
    productWeight: 3,
    productWeightUnit: "吨",
  },
  {
    productId: "3",
    productName: "废旧电动叉车一台",
    productImage:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250715/17525743322350.png",
    currentPrice: 5000.0,
    priceUnit: "元/台",
    statusName: "待发布",
    viewCount: 3500,
    enterpriseLogo: "",
    enterpriseName: "山东聊城某企业",
    enterpriseType: "私企",
    status: "upcoming",
    productCount: 1,
    productCountUnit: "台",
    productWeight: 1,
    productWeightUnit: "吨",
    productPower: 36.8,
    productPowerUnit: "kW",
  },
]);

// 计算属性
const priceInteger = computed(() => {
  return Math.floor(propertyData.value.currentPrice);
});

const priceDecimal = computed(() => {
  const decimal =
    (propertyData.value.currentPrice -
      Math.floor(propertyData.value.currentPrice)) *
    100;
  return decimal.toFixed(0).padStart(2, "0");
});

// 方法
// 选择图片
const selectImage = (index: number) => {
  currentImageIndex.value = index;
};

// 上一张图片
const prevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--;
  } else {
    currentImageIndex.value = propertyData.value.images.length - 1;
  }
};

// 下一张图片
const nextImage = () => {
  if (currentImageIndex.value < propertyData.value.images.length - 1) {
    currentImageIndex.value++;
  } else {
    currentImageIndex.value = 0;
  }
};

// 切换收藏状态
const toggleCollect = () => {
  isCollected.value = !isCollected.value;
};

// 切换关注状态
const toggleFollow = () => {
  isFollowed.value = !isFollowed.value;
};

// 弹窗相关
const showDetail = ref(false);

// 查看资产详情
const handleShowDetail = () => {
  showDetail.value = true;
};

// 检查滚动状态
const checkScrollStatus = () => {
  if (!imageListRef.value) return;

  const element = imageListRef.value;
  canScrollUp.value = element.scrollTop > 0;
  canScrollDown.value =
    element.scrollTop < element.scrollHeight - element.clientHeight;
};

// 向上滚动
const scrollUp = () => {
  if (!imageListRef.value) return;

  imageListRef.value.scrollBy({
    top: -120, // 每次滚动一个图片项的高度
    behavior: "smooth",
  });

  setTimeout(checkScrollStatus, 300);
};

// 向下滚动
const scrollDown = () => {
  if (!imageListRef.value) return;

  imageListRef.value.scrollBy({
    top: 120, // 每次滚动一个图片项的高度
    behavior: "smooth",
  });

  setTimeout(checkScrollStatus, 300);
};

/**
 * 自动向上滚动（只滚动一次）
 */
const autoScrollUp = (): void => {
  if (!imageListRef.value || isAutoScrolling.value || !canScrollUp.value)
    return;

  isAutoScrolling.value = true;
  const scrollDistance = 120; // 每次滚动距离

  imageListRef.value.scrollBy({
    top: -scrollDistance,
    behavior: "smooth",
  });

  // 滚动完成后重置状态
  setTimeout(() => {
    checkScrollStatus();
    isAutoScrolling.value = false;
  }, 400);
};

/**
 * 自动向下滚动（只滚动一次）
 */
const autoScrollDown = (): void => {
  if (!imageListRef.value || isAutoScrolling.value || !canScrollDown.value)
    return;

  isAutoScrolling.value = true;
  const scrollDistance = 120; // 每次滚动距离

  imageListRef.value.scrollBy({
    top: scrollDistance,
    behavior: "smooth",
  });

  // 滚动完成后重置状态
  setTimeout(() => {
    checkScrollStatus();
    isAutoScrolling.value = false;
  }, 400);
};

/**
 * 检查鼠标是否在滚动区域的边缘
 */
const isMouseAtScrollEdge = (
  mouseY: number
): { isTopEdge: boolean; isBottomEdge: boolean } => {
  if (!imageListRef.value) {
    return { isTopEdge: false, isBottomEdge: false };
  }

  const container = imageListRef.value;
  const containerRect = container.getBoundingClientRect();
  const containerTop = containerRect.top;
  const edgeThreshold = 80; // 边缘触发区域高度

  // 计算鼠标相对于容器的位置
  const relativeY = mouseY - containerTop;
  const containerHeight = containerRect.height;

  return {
    isTopEdge: relativeY <= edgeThreshold && canScrollUp.value,
    isBottomEdge:
      relativeY >= containerHeight - edgeThreshold && canScrollDown.value,
  };
};

/**
 * 停止自动滚动
 */
const stopAutoScroll = (): void => {
  if (autoScrollTimer.value) {
    clearTimeout(autoScrollTimer.value);
    autoScrollTimer.value = null;
  }
  isAutoScrolling.value = false;
};

/**
 * 滚动区域鼠标移动事件处理
 */
const handleScrollAreaMouseMove = (event: MouseEvent): void => {
  // 停止之前的自动滚动
  stopAutoScroll();

  // 检查鼠标是否在滚动区域的边缘
  const { isTopEdge, isBottomEdge } = isMouseAtScrollEdge(event.clientY);

  // 开发环境下的调试信息
  if (import.meta.env.DEV) {
    // 调试信息已移除
  }

  if (isTopEdge) {
    // 鼠标在上边缘且可以向上滚动时，启动向上自动滚动
    autoScrollTimer.value = window.setTimeout(() => {
      autoScrollUp();
    }, 100);
  } else if (isBottomEdge) {
    // 鼠标在下边缘且可以向下滚动时，启动向下自动滚动
    autoScrollTimer.value = window.setTimeout(() => {
      autoScrollDown();
    }, 100);
  }
};

/**
 * 滚动区域鼠标移出事件处理
 */
const handleScrollAreaMouseLeave = (): void => {
  // 停止自动滚动
  stopAutoScroll();
};

// 处理推荐卡片点击
const handleRecommendClick = (value: {
  productId: string;
  productName: string;
}) => {
  router.push({
    name: "propertyDetail", // 修正路由名称，与路由配置中的名称保持一致
    query: {
      id: value.productId,
      productName: value.productName,
    },
  });
};

// 滚动到指定区域
const scrollToSection = (sectionId: string) => {
  activeTab.value = sectionId;
  const sectionMap = {
    userInfo: userInfoRef.value,
    assetHighlights: assetHighlightsRef.value,
    productParams: productParamsRef.value,
    detailIntro: detailIntroRef.value,
  };

  const targetElement = sectionMap[sectionId as keyof typeof sectionMap];
  if (targetElement && tabsHeaderRef.value) {
    const headerHeight = tabsHeaderRef.value.offsetHeight;
    const elementRect = targetElement.getBoundingClientRect();
    const elementScrollTop =
      window.pageYOffset || document.documentElement.scrollTop;
    const elementOffsetTop = elementRect.top + elementScrollTop;

    // 计算滚动位置，考虑固定导航栏的高度
    const scrollTop = elementOffsetTop - headerHeight - 120;

    window.scrollTo({
      top: scrollTop,
      behavior: "smooth",
    });
  }
};

// 更新导航栏样式
function updateTabsHeaderStyle() {
  if (isTabsFixed.value && containerRef.value) {
    const rect = containerRef.value.getBoundingClientRect();
    tabsHeaderStyle.value = {
      position: "fixed",
      top: "92px",
      left: rect.left + "px",
      width: rect.width - 1 + "px",
      zIndex: "99",
      background: "#fff",
    };
  } else {
    tabsHeaderStyle.value = {};
  }
}

// 滚动监听，更新激活的导航项和吸顶效果
const handleScroll = () => {
  if (!tabsHeaderRef.value) return;

  const currentScrollTop = window.scrollY || document.documentElement.scrollTop;

  // 处理导航栏吸顶效果
  // 当滚动位置超过导航栏原始位置时，启用固定定位
  if (currentScrollTop >= tabsOriginalTop.value - 120) {
    isTabsFixed.value = true;
    updateTabsHeaderStyle();
  } else {
    isTabsFixed.value = false;
    updateTabsHeaderStyle();
  }

  const headerHeight = tabsHeaderRef.value.offsetHeight;
  // 如果导航栏已经固定，需要考虑固定导航栏的高度偏移
  const extraOffset = isTabsFixed.value ? headerHeight : 0;
  const scrollTop = currentScrollTop + headerHeight + extraOffset + 50;

  // 更新激活的导航项
  const sections = [
    { id: "userInfo", element: userInfoRef.value },
    { id: "assetHighlights", element: assetHighlightsRef.value },
    { id: "productParams", element: productParamsRef.value },
    { id: "detailIntro", element: detailIntroRef.value },
  ];

  for (let i = sections.length - 1; i >= 0; i--) {
    const section = sections[i];
    if (section.element) {
      // 计算元素相对于页面的偏移量
      const elementRect = section.element.getBoundingClientRect();
      const elementScrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      const elementOffsetTop = elementRect.top + elementScrollTop;

      if (scrollTop >= elementOffsetTop - 100) {
        activeTab.value = section.id;
        break;
      }
    }
  }
};

// 图片鼠标移入处理
const handleImageMouseEnter = () => {
  isImageHovered.value = true;
};

// 图片鼠标移出处理
const handleImageMouseLeave = () => {
  isImageHovered.value = false;
  maskPosition.value = { x: 0, y: 0, translateX: 0, translateY: 0 };
};

// 图片鼠标移动处理
const handleImageMouseMove = (event: MouseEvent) => {
  if (!isImageHovered.value) return;

  const imageContainer = event.currentTarget as HTMLElement;
  const rect = imageContainer.getBoundingClientRect();

  // 获取鼠标在图片容器内的相对位置
  let mouseX = event.clientX - rect.left;
  let mouseY = event.clientY - rect.top;

  // 计算蒙层左上角位置（以鼠标为中心，减去蒙层一半尺寸）
  let maskX = mouseX - maskSize / 2;
  let maskY = mouseY - maskSize / 2;

  // 边界限制 - 确保蒙层不超出图片区域
  // 左上角限制
  maskX = Math.max(0, maskX);
  maskY = Math.max(0, maskY);

  // 右下角限制 - 确保蒙层不超出图片边界
  const maxX = rect.width - maskSize;
  const maxY = rect.height - maskSize;
  maskX = Math.min(maskX, maxX);
  maskY = Math.min(maskY, maxY);

  // 为了确保放大区域始终被图片占满，我们需要计算实际的图片位移
  // 当蒙层在边界时，我们需要调整位移计算以避免空白区域
  const imageWidth = 520; // 原图宽度
  const imageHeight = 520; // 原图高度
  const magnifiedWidth = 520; // 放大容器宽度
  const magnifiedHeight = 520; // 放大容器高度
  const scale = 2.5; // 放大倍数

  // 计算放大图片应该显示的区域（基于蒙层中心点）
  const centerX = maskX + maskSize / 2;
  const centerY = maskY + maskSize / 2;

  // 计算放大图片的位移，确保显示正确的区域
  let translateX = centerX * scale - magnifiedWidth / 2;
  let translateY = centerY * scale - magnifiedHeight / 2;

  // 限制位移范围，确保放大区域始终被图片占满
  const maxTranslateX = imageWidth * scale - magnifiedWidth;
  const maxTranslateY = imageHeight * scale - magnifiedHeight;

  translateX = Math.max(0, Math.min(translateX, maxTranslateX));
  translateY = Math.max(0, Math.min(translateY, maxTranslateY));

  maskPosition.value = {
    x: maskX,
    y: maskY,
    translateX: translateX,
    translateY: translateY,
  };
};

// 查看企业详情事件
const showEnterpriseDetail = (enterpriseId: string) => {
  router.push({
    name: "enterpriseDetail",
    query: {
      id: enterpriseId,
      productName: propertyData.value.productName,
    },
  });
};

/**
 * 增加围观数量
 * 在每次进入页面时调用该接口增加围观数量
 */
const addViewCount = async () => {
  const id = route.query.id as string;
  const currentViewNum = propertyData.value.viewCount || 0;
  
  if (!id) {
    // console.warn('缺少资产ID参数，无法增加围观数量'); 已移除
    return;
  }
  
  try {
    const response = await newApi.addViewCount(id, currentViewNum);
    if (response.success || response.code === 200) {
      // console.log('围观数量增加成功'); 已移除
      // 可以选择更新本地的围观数量显示
      // propertyData.value.viewCount = response.result?.viewCount || propertyData.value.viewCount + 1;
    } else {
      // console.warn('增加围观数量失败: ', response.message || response.msg);
    }
  } catch (error) {
    console.error('增加围观数量请求失败:', error);
  }
};

// 组件挂载时获取资产详情
onMounted(async () => {
  const productId = route.params.id as string;
  propertyData.value.productId = productId;

  // 获取资产详情数据
  await getPropertyDetail();
  // console.log("获取资产详情，ID: ", productId);

  // 增加围观数量
  await addViewCount();

  // 等待DOM更新后检查滚动状态
  await nextTick();
  checkScrollStatus();

  // 计算导航栏原始位置（用于吸顶效果）
  if (tabsHeaderRef.value) {
    const headerRect = tabsHeaderRef.value.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    tabsOriginalTop.value = headerRect.top + scrollTop;
  }

  // 添加滚动事件监听
  if (imageListRef.value) {
    imageListRef.value.addEventListener("scroll", checkScrollStatus);
  }

  window.addEventListener("scroll", handleScroll);
});

// 组件卸载时移除事件监听
onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
  // 停止自动滚动
  stopAutoScroll();
});
</script>

<style lang="scss" scoped>
.property-detail {
  max-width: 1280px;
  margin: 0 auto;
  padding: 20px 0 30px 0;

  .detail-section {
    background-color: #fff;
    border-radius: 10px;
    padding: 16px 20px;
    margin-bottom: 20px;
    overflow: visible; // 允许放大镜区域溢出

    .header-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 15px;
      border-bottom: 1px solid #e5e5e5;

      .breadcrumb {
        font-size: 14px;
        color: #666;

        .free-title {
          cursor: pointer;
        }

        .separator {
          margin: 0 8px;
        }

        .current {
          color: #004c66;
          font-weight: 500;
          cursor: pointer;
        }
      }
      .header-right {
        display: flex;
        .collect {
          margin-right: 15px;
          cursor: pointer;
          .collect-icon {
            width: 15px;
            height: 15px;
          }
          .collect-text {
            display: inline-block;
            width: 45px;
            text-align: left;
          }
        }
        .view-count {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #666;

          .eye-icon {
            width: 16px;
            height: 16px;
            margin-right: 5px;
          }
        }
      }
    }

    .content-section {
      display: flex;
      gap: 20px;
      margin-bottom: 25px;
      position: relative;
      overflow: visible; // 允许放大镜区域溢出

      .image-list-container {
        position: relative;
        width: 96px;
        height: 520px;

        .scroll-arrow {
          position: absolute;
          left: 0;
          right: 0;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          z-index: 10;
          transition: opacity 0.3s ease;

          &.scroll-up {
            background: linear-gradient(
              to top,
              rgba(255, 255, 255, 0) 0%,
              rgba(255, 255, 255, 1) 80%
            );
            top: 0;
          }

          &.scroll-down {
            background: linear-gradient(
              to bottom,
              rgba(255, 255, 255, 0) 0%,
              rgba(255, 255, 255, 1) 80%
            );
            bottom: 0;
          }

          .arrow-overlay {
            position: absolute;
            right: 0;
            bottom: 0;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .scroll-icon {
            position: relative;
            bottom: -5px;
            z-index: 11;
            width: 24px;
            height: 24px;
          }

          // 翻转180度
          .scroll-icon-up {
            top: -5px;
            transform: rotate(180deg);
          }

          &:hover .arrow-icon {
            color: #004c66;
          }
        }

        .image-list {
          display: flex;
          flex-direction: column;
          gap: 10px;
          width: 120px;
          height: 100%;
          overflow-y: auto;
          scrollbar-width: none;
          -ms-overflow-style: none;
          position: relative;

          &::-webkit-scrollbar {
            display: none;
          }

          // 上边缘触发区域
          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 80px;
            background: linear-gradient(
              180deg,
              rgba(0, 76, 102, 0.05) 0%,
              transparent 100%
            );
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
          }

          // 下边缘触发区域
          &::after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 80px;
            background: linear-gradient(
              0deg,
              rgba(0, 76, 102, 0.05) 0%,
              transparent 100%
            );
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1;
          }

          // 鼠标悬停时显示边缘提示
          &:hover::before,
          &:hover::after {
            opacity: 1;
          }

          .image-item {
            width: 96px;
            height: 96px;
            border-radius: 10px;
            overflow: hidden;
            cursor: pointer;
            border: 2px solid transparent;
            transition: border-color 0.3s;
            flex-shrink: 0;

            &.active {
              border-color: #004c66;
            }

            &:hover {
              border-color: #004c66;
            }

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }
        }
      }

      .image-preview {
        flex: 1;
        max-width: 520px;
        position: relative;

        .preview-container {
          position: relative;
          width: 100%;
          height: 520px;
          border-radius: 10px;
          overflow: hidden;

          .image-container {
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              display: block;
            }

            // 放大镜蒙层 - 小方块
            .magnifier-mask {
              position: absolute;
              background: rgba(0, 76, 102, 0.2);
              pointer-events: none;
              z-index: 10;
              box-sizing: border-box; // 确保边框不影响尺寸
            }
          }

          .nav-btn {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
            color: rgba(255, 255, 255, 0.8);

            &:hover {
              color: rgba(255, 255, 255, 1);
            }

            &.prev-btn {
              left: 15px;
              z-index: 10;
            }

            &.next-btn {
              right: 15px;
            }

            .arrow-icon {
              width: 40px;
              height: 40px;
            }

            .arrow-icon-right {
              transform: rotate(180deg);
            }
          }
        }

        // 放大图片显示区域
        .magnified-view {
          position: absolute;
          top: 0;
          left: calc(100% + 20px);
          width: 520px;
          height: 520px;
          background: #fff;
          border: 1px solid #ddd;
          border-radius: 8px;
          overflow: hidden;
          z-index: 20;

          img {
            width: 1300px; // 原图尺寸的2.5倍 (520 * 2.5)
            height: 1300px; // 原图尺寸的2.5倍 (520 * 2.5)
            object-fit: cover;
            position: absolute;
            top: 0;
            left: 0;
          }
        }
      }

      .property-info {
        flex: 1;
        display: flex;
        flex-direction: column;

        .info-top {
          margin-top: 11px;
          .title {
            display: flex;
            position: relative;
            width: 510px;

            .title-status-tag {
              display: inline-block;
              width: 42px;
              height: 23px;
              line-height: 23px;
              text-align: center;
              font-size: 12px;
              border-radius: 10px 0;
              background: linear-gradient(
                90deg,
                rgba(0, 150, 120, 1) 0%,
                rgba(0, 76, 102, 1) 100%
              );
              color: #fff;
              position: absolute;
              top: 6px;
              left: 4px;
            }

            .title-text {
              font-size: 24px;
              margin-left: 52px;
              font-family: "PingFang Bold";
            }

            .detail-btn {
              text-wrap: nowrap;
              color: #004c66;
              cursor: pointer;
              .detail-icon {
                width: 8px;
                height: 14px;
              }
            }
          }

          .subtitle {
            font-size: 16px;
            color: #666;
            margin-bottom: 10px;
          }

          .parameters {
            font-family: "PingFang Medium";
            display: flex;
            margin-top: 11px;
            margin-bottom: 10px;
            span {
              color: #333;
              font-size: 18px;
              &:not(:last-child)::after {
                content: "|";
                margin: 0 10px;
                color: #333;
              }
            }
          }

          .location {
            display: flex;
            align-items: center;
            font-size: 14px;
            color: #999;
            margin-bottom: 10px;

            .location-icon {
              width: 16.8px;
              height: 16px;
              margin-right: 5px;
            }
          }

          .info-price {
            margin-top: 10px;
            display: flex;
            align-items: baseline;
            gap: 2px;
            font-family: "DIN Bold";

            .currency {
              font-size: 24px;
              color: #004c66;
            }

            .price-integer {
              font-size: 40px;
              color: #004c66;
            }

            .price-decimal {
              font-size: 24px;
              color: #004c66;
            }

            .price-unit {
              font-size: 18px;
              color: #004c66;
              margin-left: 5px;
              font-family: "PingFang Bold";
            }
          }
        }
      }
    }

    .tabs-section {
      .tabs-header {
        display: flex;
        border-bottom: 1px solid;
        border-image: linear-gradient(
            to right,
            rgba(221, 221, 221, 1),
            rgba(255, 255, 255, 1)
          )
          1;
        margin-bottom: 20px;
        background-color: #fff;
        z-index: 100;

        // 吸顶效果样式
        &.tabs-header-fixed {
          /* 只保留美化，不要写 position/left/width */
          margin-bottom: 0;
          padding-left: 20px;
          /* position/left/width 由 style 绑定控制 */
        }

        .tab-item {
          display: flex;
          align-items: center;
          padding: 15px 0;
          margin-right: 20px;
          font-size: 16px;
          cursor: pointer;
          color: #666;
          border-bottom: 2px solid transparent;
          transition: all 0.3s;
          position: relative;
          bottom: -1px;

          &.active {
            color: #004c66;
            border-bottom-color: #004c66;
          }

          &:hover {
            color: #004c66;
          }

          .tab-icon {
            width: 18px;
            height: 18px;
            margin-right: 8px;
          }
        }
      }

      // 占位元素样式，防止固定定位时内容跳动
      .tabs-header-placeholder {
        height: 57px; // 与tabs-header的高度保持一致（15px padding * 2 + 文字高度 + 边框）
        margin-bottom: 20px;
      }

      .tabs-content {
        /* background: linear-gradient(
          90deg,
          rgba(0, 76, 102, 0) 0%,
          rgba(0, 76, 102, 0.1) 100%
        );
        margin: -20px;
        padding: 20px; */
        border-radius: 10px 0 10px 0;
        .user-info-content {
          .company-info {
            .company-header {
              display: flex;
              align-items: center;
              gap: 20px;

              .company-logo {
                width: 72px;
                height: 72px;
                border-radius: 50%;
                overflow: hidden;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                }
              }

              .company-details {
                flex: 1;

                .company-details-container {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;

                  .company-title {
                    display: flex;
                    align-items: center;
                    .company-name {
                      font-size: 20px;
                      font-weight: bold;
                      color: #333;
                    }

                    .company-type {
                      margin-left: 5px;
                    }

                    .follow-btn {
                      margin-left: 10px;
                      .el-button {
                        padding: 1px 6px;
                        min-width: 49px;
                        height: 21px;
                      }
                    }
                  }
                }

                .company-rating {
                  display: flex;
                  align-items: center;
                  gap: 15px;

                  .people {
                    font-size: 14px;
                    color: #666;
                  }

                  .rating {
                    display: flex;
                    align-items: end;
                    gap: 5px;

                    .rating-score {
                      font-size: 22px;
                      font-weight: 500;
                      color: #333;
                      line-height: 1;
                    }

                    .rating-text {
                      font-size: 12px;
                      color: #999;
                    }

                    .stars {
                      display: flex;
                      gap: 2px;

                      .star-filled {
                        color: #ffd700;
                      }

                      .star-empty {
                        color: #ddd;
                      }
                    }
                  }
                }
              }

              .company-icon {
                height: 100%;
                .show-company-icon {
                  transform: rotate(-90deg);
                  width: 34px;
                  height: 34px;
                }
              }
            }

            .company-description {
              font-size: 14px;
              color: #666;
              line-height: 1.6;
            }
          }
        }

        .photos-content {
          .photos-placeholder {
            text-align: center;
            padding: 40px;
            color: #999;
          }
        }
      }

      // 新增内容区域样式
      .content-section {
        min-height: 300px;
        margin: 21px 0;

        &:last-child {
          border-bottom: none;
        }

        .section-content {
          .section-title {
            display: block;
            font-family: "PingFang Bold";
            font-size: 20px;
            color: #333;
            margin-bottom: 20px;
          }

          .section-body {
            border-radius: 8px;
            padding: 40px;
            text-align: center;

            p {
              color: #666;
              font-size: 16px;
              margin: 0;
            }
          }
        }
      }

      .userinfo {
        margin-top: 31px;
        margin-bottom: 0;
        min-height: 0;
        padding: 0;
        border-bottom: none;
        cursor: pointer;
      }
    }
  }

  .recommend-section {
    background-color: #fff;
    border-radius: 10px;
    padding: 20px;

    .section-title {
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid;
      border-image: linear-gradient(
          to right,
          rgba(221, 221, 221, 1),
          rgba(255, 255, 255, 1)
        )
        1;

      .title-container {
        display: flex;
        align-items: center;
        font-size: 18px;
        font-family: "PingFang Bold";
        color: #004c66;

        .title-icon {
          width: 22px;
          height: 25px;
          margin-right: 6px;
          color: #99b7c2;
        }
      }
    }

    .recommend-content {
      .property-cards {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
      }
    }
  }
}

.detail-content {
  padding: 20px 60px 0 60px;
  text-align: center;
}

// 响应式设计
@media (max-width: 1200px) {
  .property-detail {
    .detail-section {
      .content-section {
        flex-direction: column;

        .image-list-container {
          width: 100%;
          height: 120px;

          .scroll-arrow {
            display: none; // 横向滚动时隐藏箭头
          }

          .image-list {
            flex-direction: row;
            width: 100%;
            height: 120px;
            overflow-x: auto;
            overflow-y: hidden;
            padding: 0;
            gap: 12px;

            .image-item {
              width: 100px;
              height: 100px;
            }
          }
        }

        .image-preview {
          max-width: none;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .property-detail {
    padding: 10px;

    .detail-section {
      padding: 20px;

      .header-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }

      .content-section {
        .property-info {
          .action-buttons {
            flex-direction: column;
          }
        }
      }
    }

    .recommend-section {
      .recommend-content {
        .property-cards {
          flex-direction: column;
        }
      }
    }
  }
}
</style>
