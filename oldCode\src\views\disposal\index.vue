<template>
    <div style="background-color: #f7f7f7" class="min_wrapper_1500">
        <headers-detail />
        <div class="disposal_content">
            <div class="disposal_top">
                <div class="disposal_tab">
                    <div class="tab_head">
                        <div :class="['head_item',{'active':tabIndex == 1}]" @mouseenter="handleMouseOver(1)"><span>二手</span>设备</div>
                        <div :class="['head_item',{'active':tabIndex == 2}]" @mouseenter="handleMouseOver(2)"><span>废旧</span>物资</div>
                    </div>
                    <div class="tab_list" v-show="tabIndex == 1">
                        <div class="tab_cont" v-for="item,i in eslist" :key="i">
                            <div class="tab_second">
                                <div class="second_item">{{item.firstname}}</div>
                                <div class="second_item">{{item.secondname}}</div>
                            </div>
                            <div class="tab_third"></div>
                        </div>
                    </div>
                    <div class="tab_list" v-show="tabIndex == 2">
                        <div class="tab_cont" v-for="item,i in fjlist" :key="i">
                            <div class="tab_second">
                                <div class="second_item">{{item.firstname}}</div>
                                <div class="second_item">{{item.secondname}}</div>
                            </div>
                            <div class="tab_third"></div>
                        </div>
                    </div>
                </div>
                <div class="disposal_swiper">
                    <el-carousel height="357px">
                        <el-carousel-item v-for="item in 4" :key="item">
                            <img src="@/assets/disposal/banner.png" alt="" srcset="">
                        </el-carousel-item>
                    </el-carousel>
                </div>
                <div class="top_right">
                    <div class="entrance">
                        <div class="entrance_label"><span>快捷</span>入口</div>
                        <div class="entrance_list">
                            <div class="entrance_item" v-for="item in entranceList" :key="item.name">
                                <img class="entrance_icon" :src="item.icon" alt="" srcset="">
                                <div class="entrance_title">{{ item.name }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="kefu_label">
                        <img class="kefu_icon" src="@/assets/disposal/customer.png" alt="" srcset="">
                        联系客服
                    </div>
                    <div class="kefu_tel">400-000-000</div>
                </div>
            </div>
            <div class="disposal_industry">
                <div class="industry">
                    <div class="industry_header">
                        <div class="header_title">行业资讯</div>
                        <div class="industry_more">
                            查看更多
                            <img class="more_icon" src="@/assets/disposal/more.png" alt="" srcset="">
                        </div>
                    </div>
                    <seamless-scroll ref="scroll" :list="tableData"></seamless-scroll>
                    <!-- <div class="industry_list">
                        <div class="industry_item" v-for="item in 7" :key="item">
                            <div class="item_status">[新闻资讯]</div>
                            <div class="item_text">设备更新，拉动投资增长助力产业升级</div>
                        </div>
                    </div> -->
                </div>
                <div class="industry">
                    <div class="industry_header">
                        <div class="header_title">行业分析</div>
                        <div class="industry_more">
                            查看更多
                            <img class="more_icon" src="@/assets/disposal/more.png" alt="" srcset="">
                        </div>
                    </div>
                    <seamless-scroll ref="scroll" :list="tableData1"></seamless-scroll>
                    <!-- <div class="industry_list">
                        <div class="industry_item" v-for="item in 7" :key="item">
                            <div class="item_status">[废金属]</div>
                            <div class="item_text">2024年10月22日废不锈钢最新报价</div>
                        </div>
                    </div> -->
                </div>
                <div class="publish">
                    <div class="publish_label">快速卖货/找货</div>
                    <div class="publish_tips">
                        <div class="tips_line"></div>
                        <div class="tips_text"><span>无需注册</span>快速发布</div>
                        <div class="tips_line"></div>
                    </div>
                    <div class="publish_tel">拨打************快速委托</div>
                    <el-input
                        type="textarea"
                        :rows="5"
                        placeholder="您的采购描述/处置描述"
                        v-model="textarea">
                    </el-input>
                    <el-input v-model="input" placeholder="联系电话"></el-input>
                    <div class="publish_btn">立即提交</div>
                </div>
            </div>
            <div class="disposal_superior">
                <div class="superior_header"><span>优秀</span>回收商</div>
                <div class="superior_list">
                    <el-carousel height="257px" indicator-position="outside" arrow="never">
                        <el-carousel-item v-for="item in 4" :key="item">
                            <div class="superior_item_list">
                                <div class="superior_item" v-for="ite in 3" :key="ite">
                                    <div class="item_top">
                                        <div class="item_icon">梁山沃德</div>
                                        <div class="item_info">
                                            <div class="item_name">梁山沃德二手设备有限公司</div>
                                            <div class="item_tel">18678782828 王总</div>
                                        </div>
                                    </div>
                                    <div class="item_text">简介：高价回收，食品厂，饮装机等乳制品生产料厂，果汁厂，灌装机，杀菌机，均质机，配料罐，发酵罐，乳化罐，吹瓶机，灌装线等</div>
                                </div>
                            </div>
                        </el-carousel-item>
                    </el-carousel>
                </div>
            </div>
            <div class="disposal_device">
                <div class="device_header">
                    <div class="head_title">
                        <span>二手</span>设备
                    </div>
                    <div class="head_more">
                        查看全部
                        <img src="@/assets/disposal/onright.png" alt="">
                    </div>
                </div>
                <div class="device_filter">
                    <div :class="['filter_item',{'active':i==typeIndex}]" v-for="item,i in filterlist" :key="i" @click="typeIndex = i">{{item}}</div>
                </div>
                <div class="device_list">
                    <div class="device_item" v-for="item,i in list" :key="i">
                        <div class="item_img">
                            <img :src="url + item.url" alt="" srcset="">
                        </div>
                        <div class="item_title">
                            <span>供应</span>
                            {{item.title}}
                        </div>
                        <div class="item_price">
                            <span style="font-size:14px;letter-spacing: -6px;">¥</span>
                            {{item.price}}
                            <span style="font-size:16px;margin-left: -6px;">万</span>
                        </div>
                        <div class="item_address">
                            <img src="@/assets/disposal/dingwei.png" alt="" srcset="">
                            {{item.address}}
                        </div>
                    </div>
                </div>
                <div class="see_more">
                    点击查看更多
                    <img src="@/assets/disposal/quanbu.png" alt="" srcset="">
                </div>
            </div>
        </div>
        <footers-bottom />
        <home-right />
    </div>
  </template>
  
  <script>
  import ajax from "@/store/Ajax";
  import seamlessScroll from "@/components/seamlessScroll"
  export default {
    name: "disposal",
    components:{seamlessScroll},
    data() {
      return {
        tabIndex:1,
        eslist:[
            {
                firstname:'化工设备',
                secondname:'机床设备'
            },
            {
                firstname:'纺织设备',
                secondname:'塑料设备'
            },
            {
                firstname:'制冷设备',
                secondname:'电力设备'
            },
            {
                firstname:'印刷设备',
                secondname:'锅炉设备'
            },
            {
                firstname:'食品机械',
                secondname:'木工设备'
            },
            {
                firstname:'造纸设备',
                secondname:'水泥设备'
            },
            {
                firstname:'医药设备',
                secondname:'橡胶设备'
            },
            {
                firstname:'工程机械',
                secondname:'矿山设备'
            },
            {
                firstname:'冶金设备',
                secondname:'其它再生'
            }
        ],
        fjlist:[
            {
                firstname:'造纸设备',
                secondname:'水泥设备'
            },
            {
                firstname:'医药设备',
                secondname:'橡胶设备'
            },
            {
                firstname:'工程机械',
                secondname:'矿山设备'
            },
            {
                firstname:'冶金设备',
                secondname:'其它再生'
            }
        ],
        entranceList:[
            {
                icon:require('@/assets/disposal/caigou.png'),
                name:'采购物资'
            },
            {
                icon:require('@/assets/disposal/ziyuan.png'),
                name:'资源回收'
            },
            {
                icon:require('@/assets/disposal/shebei.png'),
                name:'设备采购'
            },
            {
                icon:require('@/assets/disposal/ruzhu.png'),
                name:'企业入驻'
            },
            {
                icon:require('@/assets/disposal/shichang.png'),
                name:'市场行情'
            },
            {
                icon:require('@/assets/disposal/huishoushang.png'),
                name:'回收商'
            },
        ],
        textarea:'',
        input:'',
        tableData:[
            {
                label:'新闻资讯',
                text:'设备更新，拉动投资增长助力产业升级'
            },
            {
                label:'新闻资讯',
                text:'全国首创！《企业重点用能设备更新改造规范》…'
            },
            {
                label:'新闻资讯',
                text:'新央企中国资环集团成立，循环经济产业大有可为'
            },
            {
                label:'新闻资讯',
                text:'郑栅洁：加快推进“两重”建设和“两新”工作'
            },
            {
                label:'新闻资讯',
                text:'经济日报金观平：推动“两新”勿忘回收循环利用'
            },
            {
                label:'新闻资讯',
                text:'兵团启动交通运输领域大规模设备更新十大行动'
            },
            {
                label:'新闻资讯',
                text:'打造全国性功能性资源回收再利用平台 推动国民经…'
            }
        ],
        tableData1:[
            {
                label:'废纸',
                text:'024年10月22日废纸最新报价'
            },
            {
                label:'废金属',
                text:'2024年10月22日废不锈钢最新报价'
            },
            {
                label:'废金属',
                text:'2024年10月18日废不锈钢最新报价'
            },
            {
                label:'废金属',
                text:'2024年10月16日废不锈钢最新报价'
            },
            {
                label:'废金属',
                text:'2024年10月14日废不锈钢最新报价'
            },
            {
                label:'废金属',
                text:'2024年10月2日废不锈钢最新报价'
            },
            {
                label:'废金属',
                text:'2024年10月1日废不锈钢最新报价'
            }
        ],
        filterlist:[
            '推荐设备',
            '化工设备',
            '机床设备',
            '纺织设备',
            '塑料设备',
            '制冷设备',
            '电力设备',
            '印刷设备',
            '锅炉设备',
            '食品机械',
            '木工设备',
            '造纸设备',
            '医药设备',
            '橡胶设备',
            '工程机械',
            '矿山设备',
            '冶金设备',
            '电梯'
        ],
        typeIndex:0,
        list:[
            {
                url:'images/xunhuan/tu/tu1.png',
                title:'模具厂转让广州C6132 AI普车4号炮',
                price:'29.37',
                unit:'万',
                address:'内蒙古自治区锡林郭勒盟市'
            },{
                url:'images/xunhuan/tu/tu2.png',
                title:'出售二手双锥3000干燥机',
                price:'2.37',
                unit:'万',
                address:'新疆维吾尔自治区乌鲁木齐市'
            },{
                url:'images/xunhuan/tu/tu3.png',
                title:'高价回收二手高速离心喷雾干燥机 二手浆膜',
                price:'327.12',
                unit:'万',
                address:'新疆维吾尔自治区哈密市'
            },{
                url:'images/xunhuan/tu/tu4.png',
                title:'公司出售英国原装COPLEY药物NGI药',
                price:'100.00',
                unit:'万',
                address:'广西壮族自治区南宁市'
            },{
                url:'images/xunhuan/tu/tu5.png',
                title:'工厂出售闲置大压片机',
                price:'3337',
                unit:'元',
                address:'河北省石家庄市'
            },{
                url:'images/xunhuan/tu/tu6.png',
                title:'模具厂转让广州C6132 AI普车4号炮',
                price:'29.37',
                unit:'万',
                address:'内蒙古自治区锡林郭勒盟市'
            },{
                url:'images/xunhuan/tu/tu7.png',
                title:'出售二手双锥3000干燥机',
                price:'2.37',
                unit:'万',
                address:'新疆维吾尔自治区乌鲁木齐市'
            },{
                url:'images/xunhuan/tu/tu8.png',
                title:'高价回收二手高速离心喷雾干燥机 二手浆膜',
                price:'327.12',
                unit:'万',
                address:'新疆维吾尔自治区哈密市'
            },{
                url:'images/xunhuan/tu/tu9.png',
                title:'公司出售英国原装COPLEY药物NGI药',
                price:'100.00',
                unit:'万',
                address:'广西壮族自治区南宁市'
            },{
                url:'images/xunhuan/tu/tu10.png',
                title:'工厂出售闲置大压片机',
                price:'3337',
                unit:'元',
                address:'河北省石家庄市'
            },{
                url:'images/xunhuan/tu/tu11.png',
                title:'模具厂转让广州C6132 AI普车4号炮',
                price:'29.37',
                unit:'万',
                address:'内蒙古自治区锡林郭勒盟市'
            },{
                url:'images/xunhuan/tu/tu12.png',
                title:'出售二手双锥3000干燥机',
                price:'2.37',
                unit:'万',
                address:'新疆维吾尔自治区乌鲁木齐市'
            },{
                url:'images/xunhuan/tu/tu13.png',
                title:'高价回收二手高速离心喷雾干燥机 二手浆膜',
                price:'327.12',
                unit:'万',
                address:'新疆维吾尔自治区哈密市'
            },{
                url:'images/xunhuan/tu/tu14.png',
                title:'公司出售英国原装COPLEY药物NGI药',
                price:'100.00',
                unit:'万',
                address:'广西壮族自治区南宁市'
            },{
                url:'images/xunhuan/tu/tu15.png',
                title:'工厂出售闲置大压片机',
                price:'3337',
                unit:'元',
                address:'河北省石家庄市'
            },{
                url:'images/xunhuan/tu/tu16.png',
                title:'模具厂转让广州C6132 AI普车4号炮',
                price:'29.37',
                unit:'万',
                address:'内蒙古自治区锡林郭勒盟市'
            },{
                url:'images/xunhuan/tu/tu17.png',
                title:'出售二手双锥3000干燥机',
                price:'2.37',
                unit:'万',
                address:'新疆维吾尔自治区乌鲁木齐市'
            },{
                url:'images/xunhuan/tu/tu18.png',
                title:'高价回收二手高速离心喷雾干燥机 二手浆膜',
                price:'327.12',
                unit:'万',
                address:'新疆维吾尔自治区哈密市'
            },{
                url:'images/xunhuan/tu/tu19.png',
                title:'公司出售英国原装COPLEY药物NGI药',
                price:'100.00',
                unit:'万',
                address:'广西壮族自治区南宁市'
            },{
                url:'images/xunhuan/tu/tu20.png',
                title:'工厂出售闲置大压片机',
                price:'3337',
                unit:'元',
                address:'河北省石家庄市'
            },
        ],
        // errorImg: require('@/assets/imgerror.png'),
        // bannerLists: [],
        url: this.$Pc.ossUrl,
        // cateList: [],
        // goodList: [],
        // page: 1,
        // last_page: 1,
        // xiaoList: [],
        // chengjiaoList:[],
        // newStopList: [],
        // total: 1,
        // per_page: 1,
      };
    },
    filters: {
      formatDate: function (value) {
        value = value * 1000;
        let date = new Date(value);
        // console.log(date)
        let y = date.getFullYear();
        // console.log(y)
        let MM = date.getMonth() + 1;
        MM = MM < 10 ? "0" + MM : MM;
        let d = date.getDate();
        d = d < 10 ? "0" + d : d;
        let h = date.getHours();
        h = h < 10 ? "0" + h : h;
        let m = date.getMinutes();
        m = m < 10 ? "0" + m : m;
        let s = date.getSeconds();
        s = s < 10 ? "0" + s : s;
        return y + "-" + MM + "-" + d + " " + h + ":" + m + ":" + s;
      },
    },
    created() {

    },
    methods: {
        handleMouseOver(index){
            this.tabIndex = index;
        },
    },
  };
  </script>
  
  <style lang="scss" scoped>
    .disposal_content{
        width: 1200px;
        margin: 20px auto ;
    }
    .disposal_top{
        display: flex;
        justify-content: space-between;
        .disposal_tab{
            position: relative;
            width: 224px;
            height: 357px;
            background: #FFFFFF;
            border-radius: 5px;
            .tab_head{
                display: flex;
                justify-content: center;
                margin: 20px auto 0;
                .head_item{
                    height: 25px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 18px;
                    color: #999999;
                    line-height: 25px;
                    box-sizing: border-box;
                    cursor: pointer;
                    &.active{
                        color: #333;
                        border-bottom: 6px solid #e5b3b8;
                        span{
                            color: #CC0319;
                        }                        
                    }
                    &:first-child{
                        margin-right: 20px;
                    }
                }
            }
            .tab_list{
                margin-top: 5px;
                .tab_cont{
                    padding: 5px 0;
                    cursor: pointer;
                    .tab_second{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 16px;
                        color: #2A2A2A;
                        line-height: 20px;
                        :first-child{
                            margin-right: 38px;
                        }
                    }
                    .tab_third{
                        display: none;
                        z-index: 3;
                        position: absolute;
                        left: 219px;
                        top: 0;
                        padding: 20px 30px;
                        width: 686px;
                        height: 357px;
                        border: 2px solid #e60000;
                        background-color: #fff;
                        box-sizing: border-box;
                    }
                    &:hover{
                        .tab_second{
                            color: #CC0319;
                        }
                        .tab_third{
                            display: block;
                        }
                    }
                }
            }
        }
        .disposal_swiper{
            width: 714px;
            height: 357px;
            img{
                width: 100%;
            }
        }
        .top_right{
            width: 224px;
            height: 357px;
            background: #FFFFFF;
            border-radius: 5px;
            .entrance{
                margin: 20px;
                .entrance_label{
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 18px;
                    color: #333;
                    line-height: 25px;
                    span{
                        color: #A80012;
                    }
                }
                .entrance_list{
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    .entrance_item{
                        display: flex;
                        align-items: center;
                        flex-direction: column;
                        margin-top: 20px;
                        cursor: pointer;
                        .entrance_icon{
                            width: 35px;
                            margin-bottom: 5px;
                        }
                        .entrance_title{
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 12px;
                            color: #2A2A2A;
                            line-height: 17px;
                        }
                    }
                }
            }
            .kefu_label{
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 40px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 16px;
                color: #666666;
                line-height: 22px;
                img{
                    display: block;
                    width: 22px;
                    margin-right: 10px;
                }
            }
            .kefu_tel{
                margin-top: 17px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 20px;
                color: #2A2A2A;
                line-height: 28px;
                text-align: center;
            }
        }
    }
    .disposal_industry{
        display: flex;
        justify-content: space-between;
        margin-top: 14px;
        .industry{
            width: 393px;
            height: 346px;
            background: #FFFFFF;
            border-radius: 5px;
            .industry_header{
                display: flex;
                justify-content: space-between;
                align-items: center;
                width: 100%;
                height: 40px;
                background: linear-gradient( 90deg, #A80012 0%, rgba(168,0,18,0.5) 100%);
                border-radius: 5px 5px 0px 0px;
                .header_title{
                    margin-left: 20px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 16px;
                    color: #FFFFFF;
                    line-height: 40px;
                }
                .industry_more{
                    display: flex;
                    align-items: center;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 12px;
                    color: #FFFFFF;
                    .more_icon{
                        width: 12px;
                        margin-left: 5px;
                        margin-right: 14px;
                    }
                }
            }
        }
        .publish{
            width: 393px;
            height: 346px;
            background: #FFFFFF;
            border-radius: 5px;
            background-image: url('../../assets/disposal/zhaohuo_bg.png');
            background-repeat: no-repeat;
            background-size: 100%;
            .publish_label{
                margin-top: 20px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 16px;
                color: #CC0319;
                line-height: 22px;
                text-align: center;
            }
            .publish_tips{
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 14px;
                .tips_line{
                    width: 24px;
                    height: 1px;
                    background: #333333;
                }
                .tips_text{
                    margin: 0 10px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 12px;
                    color: #333;
                    line-height: 17px;
                    span{
                        color: #080808;
                    }
                }
            }
            .publish_tel{
                margin-top: 5px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 12px;
                color: #444444;
                line-height: 17px;
                text-align: center;
            }
            .el-textarea{
                display: block;
                width: 353px;
                height: 117px;
                margin: 20px auto 0;
                border-radius: 2px;
                border: 1px solid #CCCCCC;
                /deep/ .el-textarea__inner{
                    border: 0;
                }
            }
            .el-input{
                display: block;
                width: 353px;
                height: 32px;
                margin: 10px auto 0;
                border-radius: 2px;
                border: 1px solid #CCCCCC;
                overflow: hidden;
                /deep/ .el-input__inner{
                    width: 100%;
                    height: 100%;
                    border: 0;
                }
            }
            .publish_btn{
                width: 353px;
                height: 32px;
                margin: 20px auto 0;
                background: #A80012;
                border-radius: 16px;
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 12px;
                color: #FFFFFF;
                line-height: 32px;
                text-align: center;
            }
        }
    }
    .disposal_superior{
        position: relative;
        width: 100%;
        height: 257px;
        margin-top: 20px;
        background: #FFFFFF;
        border-radius: 5px;
        .superior_header{
            // width: 100%;
            height: 77px;
            margin-left: 20px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 26px;
            color: #333;
            line-height: 77px;
            span{
                color: #CC0319;
            }
        }
        .superior_list{
            width: 1160px;
            // height: 150px;
            margin: -77px auto 0;
            .superior_item_list{
                display: flex;
                align-items: center;
                width: 100%;
                padding: 5px;
                box-sizing: border-box;
                margin-top: 82px;
            }
            .superior_item{
                width: 374px;
                height: 150px;
                margin-right: 20px;
                padding: 20px;
                background: #FFFFFF;
                box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.1);
                border-radius: 5px;
                box-sizing: border-box;
                &:last-child{
                    margin-right: 0;
                }
                .item_top{
                    display: flex;
                    align-items: center;
                    .item_icon{
                        width: 50px;
                        height: 50px;
                        margin-right: 10px;
                        padding: 3px 0 0 8px;
                        background: #FD7855;
                        border-radius: 5px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 600;
                        font-size: 16px;
                        color: #FFFFFF;
                        line-height: 22px;
                        letter-spacing: 3px;
                        box-sizing: border-box;
                    }
                    .item_name{
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 600;
                        font-size: 16px;
                        color: #2A2A2A;
                        line-height: 22px;
                    }
                    .item_tel{
                        margin-top: 7px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 400;
                        font-size: 12px;
                        color: #666666;
                        line-height: 17px;
                    }
                }
                .item_text{
                    margin-top: 10px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 12px;
                    color: #333333;
                    line-height: 17px;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    text-overflow: ellipsis;
                    overflow: hidden;
                }
            }
            /deep/.el-carousel__indicators{
                position: absolute;
                right: 0;
                top: 25px;
                left: inherit;
                bottom: inherit;
            }
            /deep/.el-carousel__indicator.is-active button{
                width: 40px;
                height: 8px;
                background: #CC0319;
                border-radius: 8px;
            }
            /deep/.el-carousel__indicators--outside button{
                width: 15px;
                height: 8px;
                background: #CCCCCC;
                border-radius: 8px;
            }
        }
    }
    .disposal_device{
        width: 100%;
        margin-top: 20px;
        padding: 0 20px;
        background: #FFFFFF;
        border-radius: 5px;
        box-sizing: border-box;
        overflow: hidden;
        .device_header{
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 77px;
            .head_title{
                font-family: PingFangSC, PingFang SC;
                font-weight: 600;
                font-size: 26px;
                color: #333;
                span{
                    color: #CC0319;
                }
            }
            .head_more{
                display: flex;
                align-items: center;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 16px;
                color: #666666;
                cursor: pointer;
                img{
                    width: 18px;
                    margin-left: 5px;
                }
            }
        }
        .device_filter{
            display: flex;
            flex-wrap: wrap;
            padding: 0 10px 0 20px;
            box-sizing: border-box;
            .filter_item{
                min-width: 84px;
                height: 40px;
                background: rgba(241,241,241,0.8);
                border-radius: 8px;
                margin: 15px 10px 15px 0;
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 16px;
                color: #2A2A2A;
                line-height: 40px;
                text-align: center;
                cursor: pointer;
                &.active{
                    background: #A80012;
                    color: #fff;
                }
            }
        }
        .device_list{
            display: flex;
            flex-wrap: wrap;
            .device_item{
                width: 216px;
                margin-top: 20px;
                margin-right: 20px;
                cursor: pointer;
                &:nth-child(5n){
                    margin-right: 0;
                }
                .item_img{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 216px;
                    height: 216px;
                    border-radius: 10px;
                    overflow: hidden;
                    img{
                        display: block;
                        width: 100%;
                    }
                }
                .item_title{
                    height: 44px;
                    margin-top: 10px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 16px;
                    color: #333333;
                    line-height: 22px;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 2;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    span{
                        height: 21px;
                        padding: 2px 6px 2px 10px;
                        background: linear-gradient( 135deg, #FF3F54 0%, #CC0319 100%);
                        border-radius: 3px;
                        font-family: PingFangSC, PingFang SC;
                        font-weight: 600;
                        font-size: 12px;
                        color: #FFFFFF;
                        line-height: 17px;
                        letter-spacing: 4px;
                        text-align: center;
                    }
                }
                .item_price{
                    margin-top: 10px;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 600;
                    font-size: 24px;
                    color: #CC0319;
                    line-height: 33px;
                }
                .item_address{
                    display: flex;
                    align-items: center;
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 12px;
                    color: #666666;
                    line-height: 17px;
                    img{
                        display: block;
                        width: 10px;
                        margin-right: 5px;
                    }
                }
            }
        }
        .see_more{
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 32px 0;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            font-size: 16px;
            color: #333333;
            line-height: 22px;
            cursor: pointer;
            img{
                display: block;
                width: 18px;
                margin-left: 5px;
            }
        }
    }
  </style>
  