<template>
	<div class="min_wrapper_1500 " style="background-color: #F7F7F7;">
		<Headers />
		<div class="About_one">
			<div class="About_one_title">{{info.pmh_name}}</div>
			<div class="About_one_title_view" v-html="info.pmh_gonggao"></div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import ajax from '../store/Ajax'
	export default {
		name: 'about',
		components: {
			Headers,
			FootersBottom,
			homeRight
		},
		data() {
			return {
				info: null
			}
		},
		created() {
			this.gerInfo()
		},
		methods: {
			gerInfo() {
				ajax.pmhinfo({
						id: this.$route.query.id
					})
					.then(res => {
						if (res.code == 1) this.info = res.data
					})
			}
		},
	}
</script>

<style scoped="scoped">
	.About_one {
		width: 1200px;
		min-height: 800px;
		margin: 20px auto;
		padding: 20px 10px;
		box-sizing: border-box;
		background-color: #FFFFFF;
	}

	.About_one_title {
		text-align: center;
		line-height: 130px;
		font-size: 600;
		font-size: 30px;
	}
	.About_one_title_view{
		width: 90%;
		margin: 0 auto;
	}
</style>
