# Tiptap富文本编辑器

基于Tiptap的现代化富文本编辑器组件，专为JeecgBoot Vue3项目设计。

## 特性

- ✅ **轻量级**：相比TinyMCE更小的体积
- ✅ **现代化**：基于ProseMirror，支持协作编辑
- ✅ **Vue友好**：专为Vue 3设计，完美集成
- ✅ **可商用**：MIT许可证，完全免费商用
- ✅ **高度可定制**：模块化设计，易于扩展
- ✅ **功能丰富**：支持文本格式化、标题、列表、链接、图片等

## 安装依赖

```bash
npm install @tiptap/vue-3 @tiptap/starter-kit @tiptap/extension-image @tiptap/extension-link @tiptap/extension-table @tiptap/extension-table-row @tiptap/extension-table-cell @tiptap/extension-table-header @tiptap/extension-text-align @tiptap/extension-color @tiptap/extension-text-style @tiptap/extension-font-family @tiptap/extension-underline @tiptap/extension-subscript @tiptap/extension-superscript
```

## 基础使用

### 1. 直接使用TiptapEditor组件

```vue
<template>
  <TiptapEditor
    v-model="content"
    height="400px"
    placeholder="请输入内容..."
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import { TiptapEditor } from '/@/components/TiptapEditor'

const content = ref('<p>初始内容</p>')

const handleChange = (value) => {
  console.log('内容变化:', value)
}
</script>
```

### 2. 在表单中使用JEditorTiptap

```vue
<template>
  <BasicForm :schemas="schemas" @submit="handleSubmit" />
</template>

<script setup>
import { BasicForm } from '/@/components/Form'

const schemas = [
  {
    field: 'content',
    component: 'JEditorTiptap',
    label: '内容',
    required: true,
    componentProps: {
      height: '300px',
      placeholder: '请输入内容...',
    },
  },
]

const handleSubmit = (values) => {
  console.log('表单数据:', values)
}
</script>
```

### 3. 替换现有的JEditor组件

只需要将模板中的 `JEditor` 替换为 `JEditorTiptap`：

```vue
<!-- 原来的写法 -->
<JEditor v-model:value="content" placeholder="请输入内容" />

<!-- 新的写法 -->
<JEditorTiptap v-model:value="content" placeholder="请输入内容" />
```

## 组件属性

### TiptapEditor Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| modelValue | string | '' | 编辑器内容 |
| placeholder | string | '请输入内容...' | 占位符文本 |
| height | string | '300px' | 编辑器高度 |
| disabled | boolean | false | 是否禁用 |
| hideToolbar | boolean | false | 是否隐藏工具栏 |
| autoFocus | boolean | true | 是否自动聚焦 |

### JEditorTiptap Props

继承TiptapEditor的所有属性，并保持与原JEditor组件的API兼容性：

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | string | '' | 编辑器内容（兼容原JEditor） |
| disabled | boolean | false | 是否禁用 |
| autoFocus | boolean | true | 是否自动聚焦 |
| height | string | '300px' | 编辑器高度 |
| placeholder | string | '请输入内容...' | 占位符文本 |
| hideToolbar | boolean | false | 是否隐藏工具栏 |

## 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | (value: string) | 内容变化时触发 |
| update:modelValue | (value: string) | v-model更新事件 |
| update:value | (value: string) | 兼容原JEditor的更新事件 |

## 工具栏功能

- **文本格式化**：粗体、斜体、下划线、删除线
- **标题**：H1-H6标题和正文
- **文本对齐**：左对齐、居中、右对齐、两端对齐
- **列表**：有序列表、无序列表
- **链接**：插入和编辑链接
- **图片**：插入图片
- **撤销/重做**：支持操作历史

## 与原JEditor的对比

| 特性 | JEditor (TinyMCE) | JEditorTiptap (Tiptap) |
|------|-------------------|------------------------|
| 体积 | 较大 (~500KB+) | 较小 (~100KB) |
| 许可证 | GPL/商业许可 | MIT (完全免费) |
| Vue集成 | 第三方包装 | 原生Vue 3支持 |
| 现代化 | 传统架构 | 现代化架构 |
| 可定制性 | 配置复杂 | 模块化，易扩展 |
| 性能 | 一般 | 更好 |

## 迁移指南

### 从JEditor迁移到JEditorTiptap

1. **替换组件名**：
   ```vue
   <!-- 原来 -->
   <JEditor v-model:value="content" />
   
   <!-- 现在 -->
   <JEditorTiptap v-model:value="content" />
   ```

2. **更新表单配置**：
   ```javascript
   // 原来
   {
     component: 'JEditor',
     // ...
   }
   
   // 现在
   {
     component: 'JEditorTiptap',
     // ...
   }
   ```

3. **API兼容性**：JEditorTiptap保持了与JEditor相同的API，无需修改其他代码。

## 扩展功能

如需添加更多功能，可以修改 `TiptapEditor.vue` 组件，添加更多Tiptap扩展：

```javascript
// 例如添加表格支持
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableCell from '@tiptap/extension-table-cell'
import TableHeader from '@tiptap/extension-table-header'

// 在extensions数组中添加
extensions: [
  StarterKit,
  Table.configure({
    resizable: true,
  }),
  TableRow,
  TableHeader,
  TableCell,
  // ... 其他扩展
]
```

## 故障排除

### 1. 组件无法加载
确保已安装所有必需的依赖包。

### 2. 样式问题
检查是否正确导入了组件样式。

### 3. 功能缺失
如需特定功能，可能需要安装对应的Tiptap扩展包。

## 许可证

MIT License - 完全免费商用
