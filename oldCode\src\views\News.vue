<template>
	<div style="background-color: #F7F7F7;">
		<Headers />
		<div style="padding-top: 20px;box-sizing: border-box;">
			<div class="auction">
				<div class="fl">
					
					<div class="menu_z">
						{{catinfo[this.selectIndex]}}
					</div>
					<div class="flf" style="margin: 15px 0 30px;">
						<ul class="flfg">
							<li>
								<p :class='selectIndex == 2?"flfgaa":""'
									style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
									@click="setIndex(2)">行业资讯</p>
							</li>
							<li>
								<p :class='selectIndex == 1?"flfgaa":""'
									style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
									@click="setIndex(1)">常见问题</p>
							</li>
							<li>
								<p :class='selectIndex == 3?"flfgaa":""'
									style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
									@click="setIndex(3,156)">竞价流程</p>
							</li>
							<li>
								<p :class='selectIndex == 4?"flfgaa":""'
									style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
									@click="setIndex(4,155)">竞价规则</p>
							</li>
							<li>
								<p :class='selectIndex == 5?"flfgaa":""'
									style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
									@click="setIndex(5,126)">注册流程</p>
							</li>
						</ul>
					</div>
					<div><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/images/lxwm.jpg" width="221" height="44"></div>
					<div class="menu_k">
						<div class="menu_rx">咨询热线</div>
						<div class="menu_dh">400-999-0233</div>
						<!-- <div class="menu_wz">https://www.yunpaiwang.com/</div> -->
					</div>
				</div>

				<div v-if="!isShow" class="listNews">
					<div class="newsTitle">
						<span> </span>

						<span >当前位置：首页 | {{catinfo[this.selectIndex]}}</span>
						
					</div>
					<div class="newsList">
						<ul class="newsList-ul">
							<li v-for="(item,i) in nesList" @click="goDetail(item.id)">
								<p target="_blank">
									<span class="spanOne">{{item.title}}</span>
								</p>
								<span class="spanTwo"></span>
							</li>
						</ul>
					</div>
					<div class="auctionpage">
						<el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
							:current-page='page' :page-size="per_page" layout="prev, pager, next, jumper"
							:total="totle">
						</el-pagination>
					</div>
				</div>
				<div v-if="isShow" class="fr" style=" width:740px; padding:12px 15px;">
					<h2 align="center" style="  font-size: 20px;">{{info.title}}</h2>
					<h2 align="center"
						style=" border-bottom:#CCC 1px solid; color:#999; padding-bottom:10px; font-size: 14px;font-weight:100;">
						发布时间：{{info.addtime}}</h2>
					<p style="line-height:2;">
						<font v-html="info.content">
						</font>
					</p>
				</div>
				<div class="clear"></div>
			</div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import ajax from '../store/Ajax'
	export default {
		name: 'News',
		components: {
			Headers,
			FootersBottom,
			homeRight
		},
		data() {
			return {
				selectIndex: 0,
				nesList: [],
				per_page: 0,
				totle: 0,
				page: 1,
				isShow: false,
				info: {},
				catinfo:{1:'常见问题',2:'行业资讯',3:'竞价流程',4:'竞价规则',5:'注册流程',6:'竞价演示',7:'名词解释'}
			}
		},
		created() {
			console.log(this.$route.query)
			this.selectIndex = this.$route.query.cateid || 1
			// 新闻列表
			ajax.newslist({
					cateid: this.$route.query.cateid || 1,
					page: this.page
				}).then(res => {
					if (res.code == 1) {
						this.nesList = res.data.data
						this.totle = res.data.total
						this.per_page = res.data.per_page
					}
				})
				.catch(err => {

				})
			if(this.$route.query.id) this.goDetail(this.$route.query.id)
		},
		methods: {
			setIndex(i,id) {
				this.selectIndex = i
				if(!!id){
					this.goDetail(id)
					return
				}
				this.isShow = false
				this.page = 1
				ajax.newslist({
						cateid: i,
						page: this.page
					}).then(res => {
						if (res.code == 1) {
							this.nesList = res.data.data
							this.totle = res.data.total
							this.per_page = res.data.per_page
						}
					})
					.catch(err => {

					})
			},
			getList() {
				ajax.newslist({
						cateid: this.selectIndex,
						page: this.page
					}).then(res => {
						if (res.code == 1) {
							this.nesList = res.data.data
							this.totle = res.data.total
							this.per_page = res.data.per_page
						}
					})
					.catch(err => {

					})
			},
			formatRichText(html) {
				//控制小程序中图片大小
				let newContent = html.replace(/<img[^>]*>/gi, function(match, capture) {
					console.log(match.search(/style=/gi));
					if (match.search(/style=/gi) == -1) {
						match = match.replace(/\<img/gi, '<img style=""');
					}
					return match;
				});
				
				newContent = newContent.replace(/style="/gi, '$& max-width:100% !important; ');
				newContent = newContent.replace(/<br[^>]*\/>/gi, '');
				return newContent;
			},
			handleSizeChange(e) {
				this.page = e
				this.getList()
			},
			handleCurrentChange(e) {
				this.page = e
				this.getList()
			},
			goDetail(id) {
				ajax.newsinfo({
						id: id
					}).then(res => {
						if (res.code == 1) {
							res.data.content=this.formatRichText(res.data.content)
							this.isShow = true
							this.info = res.data
						}
					})
					.catch(err => {

					})
			}
		}

	}
</script>

<style type="text/css" scoped="scoped">
	.auction {
		width: 1160px;
		min-height: 1000px;
		padding-bottom: 50px;
		box-sizing: border-box;
		margin: 0 auto;
		background: #ffffff;
		padding: 0 20px 20px;
	}

	.auctionpage {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
	}

	/deep/.el-pagination.is-background .el-pager li:not(.disabled).active {
		background-color: #D6363B;
		color: white;
	}

	.fl {
		float: left;
		display: inline;
	}

	.fr {
		float: right;
	}


	.clear,
	.cl {
		clear: both;
		height: 0;
		overflow: hidden;
	}


	p,
	ul,
	li {
		list-style: none;
		margin: 0px;
		padding: 0px;
	}
	


	.rel {
		position: relative;
	}





	.xuan {
		position: fixed;
		top: 60%;
		right: 45px;
		z-index: 99;
		display: flex;
		-js-display: flex;
		flex-direction: column;
	}

	.xuan li {
		width: 55px;
		height: 55px;
		border-radius: 50%;
		background: #949494;
		margin-top: 20px;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
	}

	.xuan li:hover {
		background: #A80012;
	}

	.xuan li img {
		width: 38px;
		height: 38px;
	}

	.listNews {
		width: 700px;
		padding: 5px 10px;

		float: right;


		margin-right: 30px;
	}

	.newsTitle {
		width: 100%;
		height: 40px;

		border-bottom: 2px solid #dedddd;
		position: relative;
		margin-top: 16px;
	}

	.newsTitle span:first-child {
		font-size: 18px;
		font-weight: 700;
		left: 0;
		position: absolute;
		bottom: 10px;
	}

	.newsTitle span:last-child {
		font-size: 16px;
		position: absolute;
		right: 0;
		bottom: 10px;
	}

	.newsList {
		width: 100%;

		position: relative;
		font-size: 14px;
	}

	.newsList-ul {
		width: 670px;

		left: 15px;

		color: #333333;
	}

	.newsList-ul li {
		width: 100%;
		border-bottom: 1px #AFAEA9 dashed;
		list-style-type: square;
		color: #AFAEA9;
		font-size: 10px;
		margin-top: 16px;
		margin-bottom: 19px;
		height: 25px;
		padding-bottom: 10px;
		cursor: pointer;
	}

	.spanOne {
		color: black;
		display: block;
		width: 450px;
		font-size: 16px;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		float: left;
		/*border: 1px solid red;*/
	}

	.spanTwo {
		float: right;
		font-size: 16px;
	}

	/*页数*/
	.pageNumber {
		width: 1200px;
		display: flex;
		-js-display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		margin: 50px auto;
	}

	.pageNumber li {
		width: 40px;
		height: 33px;
		line-height: 33px;
		text-align: center;
		background: #fff;
		color: #A80012;
		font-size: 15px;
	}

	.pageNumber li:nth-of-type(1) {
		margin-right: 10px;

	}

	.pageNumber .open a {
		color: #fff;
	}

	.pageNumber .open {
		background: #A80012;
	}

	.pageNumber li:hover {
		color: #fff;
		background: #A80012;
	}

	.pageNumber li a:hover {
		color: #fff;
		background: #A80012;
	}





	/* .menu { padding-top:15px;} */
	.menu {
		padding-bottom: 30px;
	}

	.menu {
		padding-bottom: 0px;
	}

	.wz {
		border-bottom: #e5e5e5 1px solid;
		color: #a4a4a4;
		line-height: 30px;
	}

	.menu_z {
		font-size: 26px;
		color: #384368;
		padding-top: 10px;
		font-weight: bold;
		
	}

	.menu_z span {
		font-size: 20px;
		color: #d0d0d0;
	}

	.menu_k {
		width: 300px;
		height: 76px;
		border: #e5e5e5 1px solid;
		padding: 18px 15px;
	}

	.menu_rx {
		font-size: 16px;
		color: #a1a1a1;
	}

	.menu_dh {
		font-size: 26px;
		color: #A80012;
		font-weight: bold;
	}

	.menu_wz {
		font-size: 14px;
		color: #d8d8d8;
	}

	.wz_l a {
		color: #fe0000;
		font-size: 14px;
		font-weight: bolder;
	}

	.wz_jl {
		margin-top: 30px;
	}


	.flf {
		border-top: #A80012 3px solid;
		margin: 30px 0;
		width: 330px;
	}

	.flfg {
		border-right: #c9c9c9 1px solid;
		border-bottom: #c9c9c9 1px solid;
		border-left: #c9c9c9 1px solid;
		width: 328px;
	}

	.flfg li {
		border-bottom: #c9c9c9 1px solid;
		text-align: center;
		height: 60px;
		line-height: 60px;
		cursor: pointer;
	}

	.flfg li:hover {
		text-align: center;
		height: 60px;
		line-height: 60px;
	}

	.flfg li a {
		display: block;
		font-size: 16px;
		width: 100%;
		height: 60px;
		text-align: center;
		color: #999;
	}

	.flfg li p:hover {
		background: #A80012;
		color: #FFF;
	}

	.flfgaa {
		background: #A80012 !important;
		color: #FFF !important;
	}
</style>
