<template>
	<div class="min_wrapper_1500 UserInfo ">
		<Headers />
		<div class="UserInfoCenter">
			<div class="UserInfoCenter_left">
				<div class="UserInfoCenter_left_one">个人中心</div>
				<div @click="selectIndex = 1 , ispassword =true"
					:class="selectIndex==1?'UserInfoCenter_left_two UserInfoCenter_left_twos':'UserInfoCenter_left_two'">
					<img :class="selectIndex != 1?'UserInfoCenter_left_two_img':''" src="../assets/userInfo/one/1.png">
					<img :class="selectIndex == 1?'UserInfoCenter_left_two_img':''"
						src="../assets/userInfo/one/1.1.png">
					<div>个人信息</div>
				</div>
				<div @click="selectIndex = 2"
					:class="selectIndex==2?'UserInfoCenter_left_two UserInfoCenter_left_twos':'UserInfoCenter_left_two'">
					<img :class="selectIndex != 2?'UserInfoCenter_left_two_img':''" src="../assets/userInfo/one/2.png">
					<img :class="selectIndex == 2?'UserInfoCenter_left_two_img':''"
						src="../assets/userInfo/one/2.2.png">
					<div>账户安全</div>
				</div>
				<div class="UserInfoCenter_left_one" style="margin-top: 20px;">相关标的</div>
				<div @click="selectIndex = 3"
					:class="selectIndex==3?'UserInfoCenter_left_two UserInfoCenter_left_twos':'UserInfoCenter_left_two'">
					<img :class="selectIndex != 3?'UserInfoCenter_left_two_img':''" src="../assets/userInfo/one/3.png">
					<img :class="selectIndex == 3?'UserInfoCenter_left_two_img':''"
						src="../assets/userInfo/one/3.3.png">
					<div>我的收藏</div>
				</div>
				<div @click="selectIndex = 4"
					:class="selectIndex==4?'UserInfoCenter_left_two UserInfoCenter_left_twos':'UserInfoCenter_left_two'">
					<img :class="selectIndex != 4?'UserInfoCenter_left_two_img':''" src="../assets/userInfo/one/4.png">
					<img :class="selectIndex == 4?'UserInfoCenter_left_two_img':''"
						src="../assets/userInfo/one/4.4.png">
					<div>我的报名</div>
				</div>
				<div @click="selectIndex = 5"
					:class="selectIndex==5?'UserInfoCenter_left_two UserInfoCenter_left_twos':'UserInfoCenter_left_two'">
					<img :class="selectIndex != 5?'UserInfoCenter_left_two_img':''" src="../assets/userInfo/one/5.png">
					<img :class="selectIndex == 5?'UserInfoCenter_left_two_img':''"
						src="../assets/userInfo/one/5.5.png">
					<div>我参与的标的</div>
				</div>
				<div @click="selectIndex = 6"
					:class="selectIndex==6?'UserInfoCenter_left_two UserInfoCenter_left_twos':'UserInfoCenter_left_two'">
					<img :class="selectIndex != 6?'UserInfoCenter_left_two_img':''" src="../assets/userInfo/one/6.png">
					<img :class="selectIndex == 6?'UserInfoCenter_left_two_img':''"
						src="../assets/userInfo/one/6.6.png">
					<div>我竞得的标的</div>
				</div>
				<div class="UserInfoCenter_left_one" style="margin-top: 20px;">相关企业</div>
				<div @click="selectIndex = 7"
					:class="selectIndex==7?'UserInfoCenter_left_two UserInfoCenter_left_twos':'UserInfoCenter_left_two'">
					<img :class="selectIndex != 7?'UserInfoCenter_left_two_img':''" src="../assets/userInfo/one/7.png">
					<img :class="selectIndex == 7?'UserInfoCenter_left_two_img':''"
						src="../assets/userInfo/one/7.7.png">
					<div>我的关注</div>
				</div>
				<!-- <template>
					<div class="UserInfoCenter_left_one" style="margin-top: 20px;">我的积分</div>
					<div @click="selectIndex = 8"
						:class="selectIndex==8?'UserInfoCenter_left_two UserInfoCenter_left_twos':'UserInfoCenter_left_two'">
						<img :class="selectIndex != 8?'UserInfoCenter_left_two_img':''"
							src="../assets/userInfo/one/8.png">
						<img :class="selectIndex == 8?'UserInfoCenter_left_two_img':''"
							src="../assets/userInfo/one/8.8.png">
						<div>订单管理</div>
					</div>
					<div @click="selectIndex = 9"
						:class="selectIndex==9?'UserInfoCenter_left_two UserInfoCenter_left_twos':'UserInfoCenter_left_two'">
						<img :class="selectIndex != 9?'UserInfoCenter_left_two_img':''"
							src="../assets/userInfo/one/9.png">
						<img :class="selectIndex == 9?'UserInfoCenter_left_two_img':''"
							src="../assets/userInfo/one/9.9.png">
						<div>积分明细</div>
					</div>
					<div @click="selectIndex =10"
						:class="selectIndex==10?'UserInfoCenter_left_two UserInfoCenter_left_twos':'UserInfoCenter_left_two'">
						<img :class="selectIndex != 10?'UserInfoCenter_left_two_img':''"
							src="../assets/userInfo/one/10.png">
						<img :class="selectIndex == 10?'UserInfoCenter_left_two_img':''"
							src="../assets/userInfo/one/10.1.png">
						<div>收货地址</div>
					</div>
					<div @click="selectIndex = 11"
						:class="selectIndex==11?'UserInfoCenter_left_two UserInfoCenter_left_twos':'UserInfoCenter_left_two'">
						<img :class="selectIndex != 11?'UserInfoCenter_left_two_img':''"
							src="../assets/userInfo/one/11.png">
						<img :class="selectIndex == 11?'UserInfoCenter_left_two_img':''"
							src="../assets/userInfo/one/11.1.png">
						<div>兑换密码</div>
					</div>
				</template> -->
			</div>
			<div class="UserInfoCenter_right">
				<div class="UserInfoCenter_right_one" v-if="selectIndex != 2">
					<div>
						<el-upload :action="uploadUrl" :data="dataObj"
							list-type="picture" :multiple="false" :show-file-list="false" :file-list="fileList"
							:before-upload="beforeUpload"
							:on-success="(res, file)=>handleUploadSuccess(res, file,'avatar')">
							<img class="UserInfoCenter_right_one_i" v-if="userInfo.avatar" :src="url + userInfo.avatar" @error="(e)=>e.target.src = userInfo.avatar ? 'https://oss.yunpaiwang.com/'+userInfo.avatar : ''">
							<img class="UserInfoCenter_right_one_i" v-if="!userInfo.avatar"
								src="../assets/home/<USER>">
						</el-upload>
					</div>
					<div class="UserInfoCenter_right_one_div" v-if="userInfo">
						<div class="UserInfoCenter_right_one_div_one">
							<div class="UserInfoCenter_right_one_div_one_a">{{userInfo.qiyemingcheng || userInfo.cnname || '注册用户'}}
								<span>（{{userInfo.mobile||15566666666}}）</span>
							</div>
							<div class="UserInfoCenter_right_one_div_one_ab" v-if="userInfo.enteruser == 1">个人已认证</div>
							<div class="UserInfoCenter_right_one_div_one_abs" v-else >个人未认证</div>
							<div class="UserInfoCenter_right_one_div_one_ab" v-if="userInfo.enterqiye == 1">企业已认证</div>
							<div class="UserInfoCenter_right_one_div_one_abs" v-else>企业未认证</div>
						</div>
						<div class="UserInfoCenter_right_one_div_two">
							<img src="../assets/userInfo/ziliao.png">
							<div @click="getrzgeren()" class="UserInfoCenter_right_one_div_two_div">极速认证，尊享拍卖特权
								<span>立即认证</span>
							</div>
						</div>
					</div>
					<div class="UserInfoCenter_right_one_div" v-if="!userInfo">
						<div class="UserInfoCenter_right_one_div_one">
							<div class="UserInfoCenter_right_one_div_one_a">注册用户
								<span>（15566666666）</span>
							</div>
							<div class="UserInfoCenter_right_one_div_one_abs">个人未认证</div>
							<div class="UserInfoCenter_right_one_div_one_abs">企业未认证</div>
						</div>
						<div class="UserInfoCenter_right_one_div_two">
							<img src="../assets/userInfo/ziliao.png">
							<div class="UserInfoCenter_right_one_div_two_div">极速认证，尊享拍卖特权 <span>立即认证</span></div>
						</div>
					</div>
				</div>
				<div class="UserInfoCenter_right_two" v-if="selectIndex == 1 && ispassword">账户安全</div>
				<div class="UserInfoCenter_right_two" v-if="selectIndex == 3">我的收藏</div>
				<div class="UserInfoCenter_right_two" v-if="selectIndex == 4">我的报名</div>
				<div class="UserInfoCenter_right_two" v-if="selectIndex == 5">我参与的标的</div>
				<div class="UserInfoCenter_right_two" v-if="selectIndex == 6">我竞得的标的</div>
				<div class="UserInfoCenter_right_two" v-if="selectIndex == 7">我的关注</div>
				<div v-if="selectIndex == 3 && isCollectList.length == 0 "
					style="text-align: center;line-height: 180px;">您还没有收藏的标的</div>
				<div v-if="selectIndex == 4 && baomingList.length == 0 " style="text-align: center;line-height: 180px;">
					您还没有报名的标的</div>
				<div v-if="selectIndex == 5 && canyuList.length == 0 " style="text-align: center;line-height: 180px;">
					您还没有参与过标的</div>
				<div v-if="selectIndex == 6 && jingdeList.length == 0 " style="text-align: center;line-height: 180px;">
					您还没有竞得过标的</div>
				<div v-if="selectIndex == 7 && qiyeList.length == 0 " style="text-align: center;line-height: 180px;">
					您还没有关注的企业</div>
				<!-- 收藏 -->
				<div v-if="selectIndex == 3 " class="UserInfoCenter_right_three" v-for="(item,i) in isCollectList">
					<div class="UserInfoCenter_right_three_o">
						<div>拍卖公司：{{item.parentname}}</div>
						<div>联系方式：{{item.qiyephone}}</div>
						<!-- <div v-if="selectIndex == 6" class="UserInfoCenter_right_three_o_roght">
							<div style="margin-right: 20px;">下载成功确认书</div>
							<div>下载结案报告书</div>
						</div> -->
						<img v-if="item.bd_status == 1" style="position: relative;right: -27px;"
							src="../assets/userInfo/mock.png">
						<img v-if="item.bd_status == 2 || item.bd_status == 3" style="position: relative;right: -27px;"
							src="../assets/userInfo/mock1.png">
						<img v-if="item.bd_status == 5 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock2.png">
						<img v-if="item.bd_status == 4 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock3.png">
						<img v-if="item.bd_status == 6 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock4.png">
						<img v-if="item.bd_status == 7 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock1.png">
						<img v-if="item.bd_status == 8 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock8.png">
						<!-- <img style="position: relative;right: -27px;" v-if="selectIndex == 5 || selectIndex == 4"
							src="../assets/userInfo/mock1.png"> -->
					</div>
					<div class="UserInfoCenter_right_three_t">
						<div class="UserInfoCenter_right_three_t_one">
							<div class="UserInfoCenter_right_three_t_i">
								<img style="width: 100%;height: 100%;" :src="url + item.bd_url" alt="" @error="(e)=>e.target.src = item.bd_url ? 'https://oss.yunpaiwang.com/'+item.bd_url : ''">
							</div>
							<div class="UserInfoCenter_right_three_t_r">{{item.bd_title}}</div>
						</div>
						<div class="UserInfoCenter_right_three_t_two">
							<div>起拍价 </div>
							<div>{{item.bd_qipaijia}}{{item.qpj_danwie}}</div>
						</div>
						<div v-if="item.bd_status == 5" class="UserInfoCenter_right_three_t_two">
							                            <div>成交价 </div>
							                            <div>{{item.bd_chengjiaojia}}{{item.cjj_danwie}}</div>
							                        </div>
						                        <div v-else class="UserInfoCenter_right_three_t_two">
							                            <div>保留价 </div>
							                            <div>{{item.bd_baoliujia}}</div>
							                        </div>
						<div v-if="item.bd_status != 5  && item.bd_status != 4 "
							class="UserInfoCenter_right_three_t_two" style="width: 178px;">
							<div>开拍时间 </div>
							<div>{{item.start_time_name}} </div>
						</div>
						<div v-if="item.bd_status == 5 ||  item.bd_status == 4 "
							class="UserInfoCenter_right_three_t_two" style="width: 178px;">
							<div>结束时间 </div>
							<div>{{item.end_time_name}} </div>
						</div>
						<div class="UserInfoCenter_right_three_t_three">
							<!-- <div v-if="selectIndex == 6 || selectIndex == 5 || selectIndex == 4">出价记录</div> -->
							<div class="UserInfoCenter_right_three_t_three_div" style="cursor: pointer;"
								@click="biaodiDetail(item)">标的详情</div>
							<div class="UserInfoCenter_right_three_t_three_div" style="cursor: pointer;"
								v-if="selectIndex == 3" @click="quxiaoshouchang(item)">取消收藏
							</div>
						</div>
					</div>
				</div>

				<div v-if="selectIndex == 4" class="UserInfoCenter_right_three" v-for="(item,i) in baomingList">
					<div class="UserInfoCenter_right_three_o">
						<div>拍卖公司：{{item.parentname}}</div>
						<div>联系方式：{{item.qiyephone}}</div>
						<img v-if="item.bd_status == 1" style="position: relative;right: -27px;"
							src="../assets/userInfo/mock.png">
						<img v-if="item.bd_status == 2 || item.bd_status == 3" style="position: relative;right: -27px;"
							src="../assets/userInfo/mock1.png">
						<img v-if="item.bd_status == 5 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock2.png">
						<img v-if="item.bd_status == 4 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock3.png">
						<img v-if="item.bd_status == 6 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock4.png">
						<img v-if="item.bd_status == 7 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock1.png">
						<img v-if="item.bd_status == 8 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock8.png">
						<!-- <img style="position: relative;right: -27px;" v-if="selectIndex == 5 || selectIndex == 4"
							src="../assets/userInfo/mock1.png"> -->
					</div>
					<div class="UserInfoCenter_right_three_t">
						<div class="UserInfoCenter_right_three_t_one">
							<div class="UserInfoCenter_right_three_t_i">
								<img style="width: 100%;height: 100%;" :src="url + item.bd_url" alt="" @error="(e)=>e.target.src = item.bd_url ? 'https://oss.yunpaiwang.com/'+item.bd_url :''">
							</div>
							<div class="UserInfoCenter_right_three_t_r">{{item.bd_title}}</div>
						</div>
						<div class="UserInfoCenter_right_three_t_two">
							<div>起拍价 </div>
							<div>{{item.bd_qipaijia}}{{item.qpj_danwie}}</div>
						</div>
						<div v-if="item.bd_status == 5" class="UserInfoCenter_right_three_t_two">
							                            <div>成交价 </div>
							                            <div>{{item.bd_chengjiaojia}}{{item.cjj_danwie}}</div>
							                        </div>
						                        <div v-else class="UserInfoCenter_right_three_t_two">
							                            <div>保留价 </div>
							                            <div>{{item.bd_baoliujia}}</div>
							                        </div>
						<div v-if="item.bd_status != 5  && item.bd_status != 4 "
							class="UserInfoCenter_right_three_t_two" style="width: 178px;">
							<div>开拍时间 </div>
							<div>{{item.start_time_name}} </div>
						</div>
						<div v-if="item.bd_status == 5 ||  item.bd_status == 4 "
							class="UserInfoCenter_right_three_t_two" style="width: 178px;">
							<div>结束时间 </div>
							<div>{{item.end_time_name}} </div>
						</div>
						<div class="UserInfoCenter_right_three_t_three">
							<div v-if="item.bd_status == 4 || item.bd_status == 5 "
								class="UserInfoCenter_right_three_t_three_one" style="cursor: pointer;"
								@click="setChujia(item)">出价记录</div>
							<div v-else class="UserInfoCenter_right_three_t_three_ones" style="cursor: pointer;">出价记录
							</div>
							<div class="UserInfoCenter_right_three_t_three_div" style="cursor: pointer;"
								@click="biaodiDetail(item)">标的详情</div>
						</div>
					</div>
				</div>
				<div v-if="selectIndex == 5" class="UserInfoCenter_right_three" v-for="(item,i) in canyuList">
					<div class="UserInfoCenter_right_three_o">
						<div>拍卖公司：{{item.parentname}}</div>
						<div>联系方式：{{item.qiyephone}}</div>
						<img v-if="item.bd_status == 1" style="position: relative;right: -27px;"
							src="../assets/userInfo/mock.png">
						<img v-if="item.bd_status == 2 || item.bd_status == 3" style="position: relative;right: -27px;"
							src="../assets/userInfo/mock1.png">
						<img v-if="item.bd_status == 5 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock2.png">
						<img v-if="item.bd_status == 4 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock3.png">
						<img v-if="item.bd_status == 6 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock4.png">
						<img v-if="item.bd_status == 7 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock1.png">
						<img v-if="item.bd_status == 8 " style="position: relative;right: -27px;"
							src="../assets/userInfo/mock8.png">
						<!-- <img style="position: relative;right: -27px;" v-if="selectIndex == 5 || selectIndex == 4"
							src="../assets/userInfo/mock1.png"> -->
					</div>
					<div class="UserInfoCenter_right_three_t">
						<div class="UserInfoCenter_right_three_t_one">
							<div class="UserInfoCenter_right_three_t_i">
								<img style="width: 100%;height: 100%;" :src="url + item.bd_url" alt="" @error="(e)=>e.target.src = item.bd_url ? 'https://oss.yunpaiwang.com/'+item.bd_url : ''">
							</div>
							<div class="UserInfoCenter_right_three_t_r">{{item.bd_title}}</div>
						</div>
						<div class="UserInfoCenter_right_three_t_two">
							<div>起拍价 </div>
							<div>{{item.bd_qipaijia}}{{item.qpj_danwie}}</div>
						</div>
						<div v-if="item.bd_status == 5" class="UserInfoCenter_right_three_t_two">
							                            <div>成交价 </div>
							                            <div>{{item.bd_chengjiaojia}}{{item.cjj_danwie}}</div>
							                        </div>
						                        <div v-else class="UserInfoCenter_right_three_t_two">
							                            <div>保留价 </div>
							                            <div>{{item.bd_baoliujia}}</div>
							                        </div>
						<div v-if="item.bd_status != 5  && item.bd_status != 4 "
							class="UserInfoCenter_right_three_t_two" style="width: 178px;">
							<div>开拍时间 </div>
							<div>{{item.start_time_name}} </div>
						</div>
						<div v-if="item.bd_status == 5 ||  item.bd_status == 4 "
							class="UserInfoCenter_right_three_t_two" style="width: 178px;">
							<div>结束时间 </div>
							<div>{{item.end_time_name}} </div>
						</div>
						<div class="UserInfoCenter_right_three_t_three">
							<div v-if="item.bd_status == 4 || item.bd_status == 5 " @click="setChujia(item)"
								class="UserInfoCenter_right_three_t_three_one" style="cursor: pointer;">出价记录</div>
							<div v-else class="UserInfoCenter_right_three_t_three_ones" style="cursor: pointer;">出价记录
							</div>
							<div class="UserInfoCenter_right_three_t_three_div" style="cursor: pointer;"
								@click="biaodiDetail(item)">标的详情</div>
						</div>
					</div>
				</div>
				<div v-if="selectIndex == 6" class="UserInfoCenter_right_three" v-for="(item,i) in jingdeList">
					<div class="UserInfoCenter_right_three_o">
						<div>拍卖公司：{{item.parentname}}</div>
						<div>联系方式：{{item.qiyephone}}</div>
						<div class="UserInfoCenter_right_three_o_roght">
							<div style="margin-right: 20px;" @click="xiazaiwenjian()">下载成功确认书</div>
							<div @click="xiazaiwenjian()">下载结案报告书</div>
						</div>
					</div>
					<div class="UserInfoCenter_right_three_t">
						<div class="UserInfoCenter_right_three_t_one">
							<div class="UserInfoCenter_right_three_t_i">
								<img style="width: 100%;height: 100%;" :src="url + item.bd_url" alt="" @error="(e)=>e.target.src = item.bd_url ? 'https://oss.yunpaiwang.com/'+item.bd_url : ''">
							</div>
							<div class="UserInfoCenter_right_three_t_r">{{item.bd_title}}</div>
						</div>
						<div class="UserInfoCenter_right_three_t_two">
							<div>起拍价 </div>
							<div>{{item.bd_qipaijia}}{{item.qpj_danwie}}</div>
						</div>
						<div class="UserInfoCenter_right_three_t_two">
							<div>成交价 </div>
							<div>{{item.bd_chengjiaojia}}{{item.cjj_danwie}}</div>
						</div>
						<div class="UserInfoCenter_right_three_t_two" style="width: 178px;">
							<div>结束时间 </div>
							<div>{{item.end_time_name}} </div>
						</div>
						<div class="UserInfoCenter_right_three_t_three">
							<div v-if="item.bd_status == 4 || item.bd_status == 5 " @click="setChujia(item)"
								class="UserInfoCenter_right_three_t_three_one" style="cursor: pointer;">出价记录</div>
							<div v-else class="UserInfoCenter_right_three_t_three_ones" style="cursor: pointer;">出价记录
							</div>
							<div class="UserInfoCenter_right_three_t_three_div" style="cursor: pointer;"
								@click="biaodiDetail(item)">标的详情</div>
						</div>
					</div>
				</div>

				<div v-if="selectIndex == 1 && ispassword " class="UserInfoCenter_right_slectOne">
					<div class="UserInfoCenter_right_slectOne_one">
						<div class="UserInfoCenter_right_slectOne_one_1">
							<img src="../assets/userInfo/2.png">
							<div>登陆密码</div>
						</div>
						<div style="width: 1px;height: 40px;background-color: #7B7B7B;"></div>
						<div class="UserInfoCenter_right_slectOne_one_2">互联网账号存在被盗风险，建议您定期更改密码以保护账户安全</div>
						<div class="UserInfoCenter_right_slectOne_one_3" @click="selectIndex = 2">修改</div>
					</div>
					<div class="UserInfoCenter_right_slectOne_one">
						<div class="UserInfoCenter_right_slectOne_one_1">
							<img src="../assets/userInfo/3.png">
							<div>个人资料</div>
						</div>
						<div style="width: 1px;height: 40px;background-color: #7B7B7B;"></div>
						<div class="UserInfoCenter_right_slectOne_one_2">立即完善个人资料信息，有利于保护您的资金与交易安全</div>
						<div @click="getrzgeren()" class="UserInfoCenter_right_slectOne_one_3">完善资料</div>
					</div>
					<div class="UserInfoCenter_right_slectOne_one">
						<div class="UserInfoCenter_right_slectOne_one_1">
							<img src="../assets/userInfo/4.png">
							<div>企业资料</div>
						</div>
						<div style="width: 1px;height: 40px;background-color: #7B7B7B;"></div>
						<div class="UserInfoCenter_right_slectOne_one_2">立即完善企业资料信息，尊享拍卖特权</div>
						<div @click="getrzqiye()" class="UserInfoCenter_right_slectOne_one_3">完善资料</div>
					</div>
					<div class="UserInfoCenter_right_slectOne_one">
						<div class="UserInfoCenter_right_slectOne_one_1">
							<img src="../assets/userInfo/5.png">
							<div>联系方式</div>
						</div>
						<div style="width: 1px;height: 40px;background-color: #7B7B7B;"></div>
						<div class="UserInfoCenter_right_slectOne_one_2">提供有效联系方式，随时随地获得商机</div>
						<div class="UserInfoCenter_right_slectOne_one_3" @click="setMobile">更换</div>
					</div>
				</div>
				<div v-if="selectIndex == 2 " class="UserInfoCenter_right_slectOne">
					<div class="About_title_one">账号设置</div>
					<div class="info">
						<div class="UserInfoCenter_right_fours_two_s">
							<div style="width: 110px;font-size: 14px;">旧密码：</div>
							<el-input type="password" v-model="chongzhi.password" style="width: 310px;" placeholder="">
							</el-input>
						</div>
						<div class="UserInfoCenter_right_fours_two_s">
							<div style="width: 110px;font-size: 14px;">新密码：</div>
							<el-input type="password" v-model="chongzhi.newpassword" style="width: 310px;"
								placeholder=""></el-input>
						</div>
						<div class="UserInfoCenter_right_fours_two_s">
							<div style="width: 110px;font-size: 14px;">确认：</div>
							<el-input type="password" v-model="chongzhi.newpassword1" style="width: 310px;"
								placeholder=""></el-input>
						</div>
						<div class="infobtn">
							<div @click="xiugaimima">保存</div>
							<div @click="selectIndex = 1">取消</div>
						</div>
					</div>
				</div>
				<div v-if="selectIndex == 7" class="UserInfoCenter_right_threes" v-for="(item,i) in qiyeList">
					<div class="UserInfoCenter_right_three_t">
						<div class="UserInfoCenter_right_three_t_one" style="width: 322px;">
							<div class="UserInfoCenter_right_three_t_i">
								<img style="width: 100%;height: 100%;" :src="url + item.qiye.qiyelogo" @error="(e)=>e.target.src = item.qiye.qiyelogo ? 'https://oss.yunpaiwang.com/'+item.qiye.qiyelogo : ''">
							</div>
							<div class="UserInfoCenter_right_three_t_r" style="width: 175px;">
								{{item.qiye.qiyemingcheng}}
							</div>
						</div>
						<div class="UserInfoCenter_right_three_t_two" style="width: 188px;">
							<div>联系方式</div>
							<div style="color: #000000 !important;">{{item.qiye.qiyephone}}</div>
						</div>
						<div class="UserInfoCenter_right_three_t_two" style="width: 189px;">
							<div>所在地区 </div>
							<div style="color: #000000 !important;">
								{{item.qiye.province | province}}{{item.qiye.city | city}}
							</div>
						</div>
						<div class="UserInfoCenter_right_three_t_three">
							<div class="UserInfoCenter_right_three_t_three_div" style="cursor: pointer;"
								@click="qiyeListClick(item)">企业详情</div>
							<div class="UserInfoCenter_right_three_t_three_div" style="cursor: pointer;"
								@click="quxiaoqiye(item)">取消关注</div>
						</div>
					</div>
				</div>
				<div v-if='selectIndex == 3 || selectIndex == 4 || selectIndex == 5 || selectIndex == 6 ||  selectIndex == 7 || selectIndex == 8 || selectIndex == 9 '
					class="auctionpage">
					<el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
						:current-page='page' :page-size="per_page" layout="prev, pager, next, jumper" :total="total">
					</el-pagination>
				</div>
				<div v-if="!ispassword && selectIndex  == 1 " class="UserInfoCenter_right_fours">
					<!-- <div style="margin-bottom: 15px;" class="UserInfoCenter_right_fours_title">
						<el-radio v-model="radio" label="1">企业</el-radio>
						<el-radio v-model="radio" label="2">个人</el-radio>
					</div> -->
					<template v-if="radio == 1">
						<div style="width:600px;margin-bottom:30px;">
							<el-alert v-if="info.gerenshstatus == 1" :title="'您的审核已通过，备注：'+beizhu" type="success" :closable="false" show-icon></el-alert>
							<el-alert v-if="info.gerenshstatus == 4" :title="'您的审核未通过，备注：'+beizhu" type="error" effect="dark" :closable="false" show-icon></el-alert>
						</div>
						<div class="UserInfoCenter_right_fours_one">
							<div class="UserInfoCenter_right_fours_ones">
								<div
									:class="oneSubmits<=3?'UserInfoCenter_right_fours_onessred':'UserInfoCenter_right_fours_oness' ">
									1</div>
								<div :class="oneSubmits<=3?'UserInfoCenter_right_fours_onesrr':''">填写个人资料</div>
							</div>
							<img class="UserInfoCenter_right_fours_onesI" src="../assets/userInfo/right.png">
							<div class="UserInfoCenter_right_fours_ones">
								<div
									:class="oneSubmits>1?'UserInfoCenter_right_fours_onessred':'UserInfoCenter_right_fours_oness' ">
									2</div>
								<div :class="oneSubmits>1?'UserInfoCenter_right_fours_onesrr':''">提交完善资料</div>
							</div>
							<img v-if="oneSubmits != 1" class="UserInfoCenter_right_fours_onesI"
								src="../assets/userInfo/right.png">
							<img v-if="oneSubmits == 1" class="UserInfoCenter_right_fours_onesI"
								src="../assets/userInfo/white.png">
							<div class="UserInfoCenter_right_fours_ones">
								<div
									:class="oneSubmits==3?'UserInfoCenter_right_fours_onessred':'UserInfoCenter_right_fours_oness' ">
									3</div>
								<div :class="oneSubmits==3?'UserInfoCenter_right_fours_onesrr':''">提交完成</div>
							</div>
						</div>
						<div class="UserInfoCenter_right_fours_two" v-if="oneSubmits == 1">
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>真实姓名：</div>
								<el-input v-model="info.cnname" style="width: 298px;" placeholder="请输入内容"
									:readonly="gerenreadonly"></el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>身份证号：</div>
								<el-input v-model="info.cardnum" style="width: 298px;" placeholder="请输入内容"
									:readonly="gerenreadonly">
								</el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>开户行：</div>
								<el-input v-model="info.gerenyinhang" style="width: 298px;" placeholder="请输入内容"
									:readonly="gerenreadonly">
								</el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>银行账号：</div>
								<el-input v-model="info.gerenkahao" style="width: 298px;" placeholder="请输入内容"
									:readonly="gerenreadonly">
								</el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_ss">
								<div style="width: 110px;font-size: 14px;">
									<!-- <span style="color: #A80012;">*</span> -->
									上传证件：
								</div>
								<div class="UserInfoCenter_right_fours_two_sss">
									<div class="UserInfoCenter_right_fours_two_sss_one"
										v-if="info.gerenshstatus!=1 && info.gerenshstatus!=0">
										<el-upload :action="uploadUrl"
											:data="dataObj" list-type="picture" :multiple="false"
											:show-file-list="false" :file-list="fileList" :before-upload="beforeUpload"
											:on-success="(res, file)=>handleUploadSuccess(res, file,'cardpicz')">
											<img v-if="!info.cardpicz" style="margin-right: 10px;"
												src="../assets/userInfo/zheng1.png">
											<img style="margin-right: 10px;" v-if="info.cardpicz" :src=" url + info.cardpicz" @error="(e)=>e.target.src = info.cardpicz ? 'https://oss.yunpaiwang.com/'+info.cardpicz : ''">
										</el-upload>
										<el-upload :action="uploadUrl"
											:data="dataObj" list-type="picture" :multiple="false"
											:show-file-list="false" :file-list="fileList" :before-upload="beforeUpload"
											:on-success="(res, file)=>handleUploadSuccess(res, file,'cardpicf')">
											<img v-if="!info.cardpicf" src="../assets/userInfo/zheng.png">
											<img v-if="info.cardpicf" :src="url + info.cardpicf" @error="(e)=>e.target.src = info.cardpicf ? 'https://oss.yunpaiwang.com/'+info.cardpicf : ''">
										</el-upload>
									</div>
									<div class="UserInfoCenter_right_fours_two_sss_one" v-else>
										<img class="UserInfoCenter_right_one_i" :src="url + info.cardpicz" @error="(e)=>e.target.src = info.cardpicz ? 'https://oss.yunpaiwang.com/'+info.cardpicz : ''">

										<img style="margin-left: 10px;" class="UserInfoCenter_right_one_i"
											:src="url + info.cardpicf" @error="(e)=>e.target.src = info.cardpicf ? 'https://oss.yunpaiwang.com/'+info.cardpicf : ''">
									</div>


									<div v-if="info.gerenshstatus!=1 && info.gerenshstatus!=0"
										class="UserInfoCenter_right_fours_two_ss_w">
										请按照要求上传证件照片， 类型支持gif、png、jpg、bmp；<span>认证成功后资料信息不可修改</span>
									</div>
									<div v-if="info.gerenshstatus==1" class="UserInfoCenter_right_fours_two_ss_ws">认证通过
									</div>
									<div v-else-if="info.gerenshstatus==0" class="UserInfoCenter_right_fours_two_ss_ws">
										审核中</div>
									<div @click="addNexts" v-else class="UserInfoCenter_right_fours_two_ss_ws">下一步</div>
								</div>
							</div>
						</div>
						<div class="UserInfoCenter_right_fours_two" v-if="oneSubmits == 2">
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>回收种类：</div>
								<el-select style="width: 340px;" v-model="info.biaoqian" multiple placeholder="请选择">
									<el-option v-for="item,i in options" :key="i" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</div>
							<div>
								<div class="UserInfoCenter_right_fours_two_sssss">
									<div>
										<div style="width: 110px;font-size: 14px;"><span
												style="color: #A80012;">*</span>所属地区：
										</div>
										<div class="UserInfoCenter_right_fours_two_ss_shengshi">
											<el-select @change="provinceIndex" style="margin-right: 5px;"
												v-model="province" placeholder="请选择省份">
												<el-option v-for="(item,i) in areaList" :label="item.name" :value="i">
												</el-option>
											</el-select>
											<el-select @change="cityIndex" v-if="province!== ''"
												style="margin-right: 5px;" v-model="city" placeholder="请选择市区">
												<el-option v-for="(item,i) in cityList[province].city"
													:label="item.name" :value="i">
												</el-option>
											</el-select>
											<el-select @change="districtIndex" v-if="city!== ''"
												style="margin-right: 5px;" v-model="districts" placeholder="请选择县">
												<el-option v-for="(item,i) in district[province].city[city]"
													:label="item.name" :value="i">
												</el-option>
											</el-select>
										</div>
									</div>
									<div class="UserInfoCenter_right_fours_two_sss_onesss">
										<div @click="submitClicks(2)" style="margin-right: 15px;"
											class="UserInfoCenter_right_fours_two_ss_wsss">保存
										</div>
										<div @click="submitClicks(3)" style="margin-right: 15px;"
											class="UserInfoCenter_right_fours_two_ss_wsss">保存并提交
										</div>
										<div @click="quxiaoClick" class="UserInfoCenter_right_fours_two_ss_wss">取消</div>
									</div>
								</div>
							</div>
						</div>
					</template>
					<template v-if="radio == 2">
						<div style="width:600px;margin:0 auto 30px;">
							<el-alert v-if="info.qiyeshstatus == 1" :title="'您的审核已通过，备注：'+beizhu" type="success" :closable="false" show-icon></el-alert>
							<el-alert v-if="info.qiyeshstatus == 4" :title="'您的审核未通过，备注：'+beizhu" type="error" effect="dark" :closable="false" show-icon></el-alert>
						</div>
						<div class="UserInfoCenter_right_fours_one" style="width:600px">
							<div class="UserInfoCenter_right_fours_ones">
								<div
									:class="oneSubmit<=4?'UserInfoCenter_right_fours_onessred':'UserInfoCenter_right_fours_oness' ">
									1</div>
								<div :class="oneSubmit<=4?'UserInfoCenter_right_fours_onesrr':''">填写企业资料</div>
							</div>
							<img class="UserInfoCenter_right_fours_onesI" src="../assets/userInfo/right.png">
							<div class="UserInfoCenter_right_fours_ones">
								<div
									:class="oneSubmit >= 2?'UserInfoCenter_right_fours_onessred':'UserInfoCenter_right_fours_oness' ">
									2</div>
								<div :class="oneSubmit >= 2?'UserInfoCenter_right_fours_onesrr':''">填写法人信息</div>
							</div>
							<img v-if="oneSubmit > 1" class="UserInfoCenter_right_fours_onesI"
								src="../assets/userInfo/right.png">
							<img v-else class="UserInfoCenter_right_fours_onesI"
								src="../assets/userInfo/white.png">
							<div class="UserInfoCenter_right_fours_ones">
								<div
									:class="oneSubmit >=3?'UserInfoCenter_right_fours_onessred':'UserInfoCenter_right_fours_oness' ">
									3</div>
								<div :class="oneSubmit >=3?'UserInfoCenter_right_fours_onesrr':''">填写开票信息</div>
							</div>
							<img v-if="oneSubmit > 2" class="UserInfoCenter_right_fours_onesI"
								src="../assets/userInfo/right.png">
							<img v-else class="UserInfoCenter_right_fours_onesI"
								src="../assets/userInfo/white.png">
							<div class="UserInfoCenter_right_fours_ones">
								<div
									:class="oneSubmit==4?'UserInfoCenter_right_fours_onessred':'UserInfoCenter_right_fours_oness' ">
									4</div>
								<div :class="oneSubmit==4?'UserInfoCenter_right_fours_onesrr':''">提交完善资料</div>
							</div>
						</div>
						<div class="UserInfoCenter_right_fours_two" v-if="oneSubmit == 1" style="padding-left:50px">
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>公司名称：</div>
								<el-input v-model="info.qiyemingcheng" style="width: 298px;" placeholder="请输入内容"
									:readonly="qiyereadonly">
								</el-input>
							</div>
							<!-- <div class="UserInfoCenter_right_fours_two_s">
								<div><span>*</span>证件类型：</div>
								<el-select style="width: 298px;" v-model="value" placeholder="请选择">
									<el-option v-for="item in options" :key="item.value" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</div> -->
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>信用代码：</div>
								<el-input v-model="info.xinyongdaima" style="width: 298px;" placeholder="请输入内容"
									:readonly="qiyereadonly">
								</el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>业务联系人：</div>
								<el-input v-model="info.lianxiren" style="width: 298px;" placeholder="请输入联系人"></el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>联系电话：</div>
								<el-input v-model="info.lianxidianhua" style="width: 298px;" placeholder="请输入联系电话"></el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_ss">
								<!-- <span style="color: #A80012;">*</span> -->
								<div style="width: 110px;font-size: 14px;">上传证件：</div>
								<div class="UserInfoCenter_right_fours_two_sss">
									<el-upload v-if="info.qiyeshstatus!=1 && info.qiyeshstatus!=0"
										:action="uploadUrl" :data="dataObj"
										list-type="picture" :multiple="false" :show-file-list="false"
										:file-list="fileList" :before-upload="beforeUpload"
										:on-success="(res, file)=>handleUploadSuccess(res, file,'yingye')">
										<img v-if="!info.qiyepic" src="../assets/userInfo/upload.png">
										<img v-if="info.qiyepic" :src="url + info.qiyepic" @error="(e)=>e.target.src = info.qiyepic ? 'https://oss.yunpaiwang.com/'+info.qiyepic : ''">
									</el-upload>
									<div class="UserInfoCenter_right_fours_two_sss_one" v-else>
										<img class="UserInfoCenter_right_one_i" :src="url + info.qiyepic" @error="(e)=>e.target.src = info.qiyepic ? 'https://oss.yunpaiwang.com/'+info.qiyepic : ''">
									</div>
									<div v-if="info.qiyeshstatus!=1 && info.qiyeshstatus!=0"
										class="UserInfoCenter_right_fours_two_ss_w">
										请上传营业执照副本，要求为彩色，
										<span>最多可上传1个附件，单文件最大5MB</span>
										， 类型支持gif、png、jpg、bmp；
									</div>

									<div style="margin-top: 10px;" class="UserInfoCenter_right_fours_two_sss_one"
										v-else>
										<img class="UserInfoCenter_right_one_i" :src="url + info.fr_cardpicz" @error="(e)=>e.target.src = info.fr_cardpicz ? 'https://oss.yunpaiwang.com/'+info.fr_cardpicz : ''">
										<img style="margin-left: 10px;" class="UserInfoCenter_right_one_i"
											:src="url + info.fr_cardpicf" @error="(e)=>e.target.src = info.fr_cardpicf ? 'https://oss.yunpaiwang.com/'+info.fr_cardpicf : ''">
									</div>

								</div>
							</div>
							
							<div class="UserInfoCenter_right_fours_two_ss">
								<!-- <span style="color: #A80012;">*</span> -->
								<div style="width: 110px;font-size: 14px;">其他资质证件：</div>
								<div class="UserInfoCenter_right_fours_two_sss">
									<uploads v-if="oneSubmit == 1" :fileListNew="qtfileList" :tips="'请上传开户许可证及特殊资质证件，'" @upload="uploadCallbacks"></uploads>
									<div v-if="info.qiyeshstatus==1" style="cursor: pointer;"
										class="UserInfoCenter_right_fours_two_ss_ws">认证通过</div>
									<div v-else-if="info.qiyeshstatus==0" style="cursor: pointer;"
										class="UserInfoCenter_right_fours_two_ss_ws">审核中</div>
									<div v-else style="cursor: pointer;" @click="nextPage"
										class="UserInfoCenter_right_fours_two_ss_ws">下一步</div>
									</div>
							</div>
						</div>
						<div class="UserInfoCenter_right_fours_two" v-if="oneSubmit == 2" style="padding-left:50px">
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>法人姓名：</div>
								<el-input v-model="info.fr_name" style="width: 298px;" placeholder="请输入内容"
									:readonly="farenreadonly"></el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>法人证件号：</div>
								<el-input v-model="info.fr_cardnum" style="width: 298px;" placeholder="请输入内容"
									:readonly="farenreadonly">
								</el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_ss">
								<!-- <span style="color: #A80012;">*</span> -->
								<div style="width: 150px;font-size: 14px;">上传法人证件：
								</div>
								<div class="UserInfoCenter_right_fours_two_sss">
									<div class="UserInfoCenter_right_fours_two_sss_one">
										<el-upload :action="uploadUrl"
											:data="dataObj" list-type="picture" :multiple="false"
											:show-file-list="false" :file-list="fileList" :before-upload="beforeUpload"
											:on-success="(res, file)=>handleUploadSuccess(res, file,'fr_cardpicz')">
											<img v-if="!info.fr_cardpicz" style="margin-right: 10px;"
												src="../assets/userInfo/zheng1.png">
											<img style="margin-right: 10px;" v-if="info.fr_cardpicz"
												:src=" url + info.fr_cardpicz" @error="(e)=>e.target.src = info.fr_cardpicz ? 'https://oss.yunpaiwang.com/'+info.fr_cardpicz : ''">
										</el-upload>
										<el-upload :action="uploadUrl"
											:data="dataObj" list-type="picture" :multiple="false"
											:show-file-list="false" :file-list="fileList" :before-upload="beforeUpload"
											:on-success="(res, file)=>handleUploadSuccess(res, file,'fr_cardpicf')">
											<img v-if="!info.fr_cardpicf" src="../assets/userInfo/zheng.png">
											<img v-if="info.fr_cardpicf" :src="url + info.fr_cardpicf" @error="(e)=>e.target.src = info.fr_cardpicf ? 'https://oss.yunpaiwang.com/'+info.fr_cardpicf : ''">
										</el-upload>
									</div>
									<div class="UserInfoCenter_right_fours_two_ss_w">
										请按照要求上传证件照片， 类型支持gif、png、jpg、bmp；<span>认证成功后资料信息不可修改</span>
									</div>
									<div @click="addNext(3)" class="UserInfoCenter_right_fours_two_ss_ws">下一步</div>
								</div>
							</div>
						</div>
						<div class="UserInfoCenter_right_fours_two" v-if="oneSubmit == 3" style="padding-left:50px">
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>公司名称：</div>
								<el-input v-model="info.qiyemingcheng" style="width: 298px;" placeholder="请输入公司名称" readonly></el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>公司地址：</div>
								<el-input v-model="info.address" style="width: 298px;" placeholder="请输入公司地址"></el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>开票电话：</div>
								<el-input v-model="info.kpdianhua" style="width: 298px;" placeholder="请输入开票电话"></el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>开户行：</div>
								<el-input v-model="info.kaihuhang" style="width: 298px;" placeholder="请输入开户行"></el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>账号：</div>
								<el-input v-model="info.gonghu" style="width: 298px;" placeholder="请输入账号"></el-input>
							</div>
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span style="color:#fff">*</span>行号：</div>
								<el-input v-model="info.hanghao" style="width: 298px;" placeholder="请输入行号"></el-input>
							</div>
							
							<!-- <div class="UserInfoCenter_right_fours_two_s">
								<span>*</span>
								<div style="width: 110px;font-size: 14px;">其他资质证件：</div>
								<uploads v-if="oneSubmit == 1" :fileListNew="qtfileList" :tips="'请上传开户许可证及特殊资质证件，'" @upload="uploadCallbacks"></uploads>
							</div> -->
							<div @click="addNext(4)" class="UserInfoCenter_right_fours_two_ss_ws" style="margin-left:110px">下一步</div>
						</div>
						<div class="UserInfoCenter_right_fours_two" v-if="oneSubmit == 4" style="padding-left:50px">
							<div class="UserInfoCenter_right_fours_two_s">
								<div style="width: 110px;font-size: 14px;"><span>*</span>回收种类：</div>
								<el-select style="width: 340px;" v-model="info.biaoqian" multiple placeholder="请选择">
									<el-option v-for="item,i in options" :key="i" :label="item.label"
										:value="item.value">
									</el-option>
								</el-select>
							</div>
							<div>
								<div class="UserInfoCenter_right_fours_two_sssss">
									<div>
										<div style="width: 110px;font-size: 14px;"><span
												style="color: #A80012;">*</span>所属地区：
										</div>
										<div class="UserInfoCenter_right_fours_two_ss_shengshi">
											<el-select @change="provinceIndex" style="margin-right: 5px;"
												v-model="province" placeholder="请选择省份">
												<el-option v-for="(item,i) in areaList" :label="item.name" :value="i">
												</el-option>
											</el-select>
											<el-select @change="cityIndex" v-if="province!== ''"
												style="margin-right: 5px;" v-model="city" placeholder="请选择市区">
												<el-option v-for="(item,i) in cityList[province].city"
													:label="item.name" :value="i">
												</el-option>
											</el-select>
											<el-select @change="districtIndex" v-if="city!== ''"
												style="margin-right: 5px;" v-model="districts" placeholder="请选择县">
												<el-option v-for="(item,i) in district[province].city[city]"
													:label="item.name" :value="i">
												</el-option>
											</el-select>
										</div>
									</div>
									<div class="UserInfoCenter_right_fours_two_sss_onesss">
										<div @click="submitClick(2)" style="margin-right: 15px;"
											class="UserInfoCenter_right_fours_two_ss_wsss">保存
										</div>
										<div @click="submitClick(3)" style="margin-right: 15px;"
											class="UserInfoCenter_right_fours_two_ss_wsss">保存并提交
										</div>
										<div @click="quxiaoClick" class="UserInfoCenter_right_fours_two_ss_wss">取消</div>
									</div>
								</div>
							</div>
						</div>
					</template>
				</div>
			</div>
		</div>
		<el-dialog title="出价记录" :visible.sync="dialogVisible" width="80%">
			<div class="chujialists">
				<table class="chujialist">
					<tbody>
						<tr>
							<td>状态</td>
							<td>时间</td>
							<td>编号</td>
							<td>价格</td>
						</tr>
						<tr v-for="item,i in chujiailist">
							<template>
								<td style="font-size: 13px;">{{i+1}}</td>
								<td style="font-size: 13px;">{{item.addtime | formatDate}}</td>
								<td style="font-size: 13px;">{{item.jingpaihaopai}} </td>
								<td style="font-size: 13px;">{{item.dangqianjia}} </td>
							</template>
						</tr>
					</tbody>
				</table>
			</div>
		</el-dialog>
		<el-dialog
			title="友情提示"
			:visible.sync="centerDialogVisible"
			width="30%"
			center>
			<div style="text-align:center;font-size: 16px;">请尽快填充资料，完成企业或个人实名认证！</div>
			<span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="centerDialogVisible = false">确 定</el-button>
			</span>
		</el-dialog>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import ajax from '../store/Ajax'
	import area from '../assets/js/area.js'
	import axios from 'axios'
    import Uploads from "@/components/Upload/Uploads";
	export default {
		name: 'UserInfo',
		components: {
			Headers,
			FootersBottom,
			homeRight,
			Uploads
		},
		data() {
			return {
				centerDialogVisible:false,
				uploadUrl:this.$Pc.uploadUrl,
				dialogVisible: false,
				ispassword: true,
				oneSubmits: 1,
				qiyereadonly: false,
				gerenreadonly: false,
				farenreadonly: false,
				radio: '1',
				selectIndex: 1,
				oneSubmit: 1,
				options: [{
					value: '1',
					label: '物资设备'
				}, {
					value: '2',
					label: '机动车'
				}, {
					value: '3',
					label: '房产'
				}, {
					value: '4',
					label: '土地'
				}, {
					value: '5',
					label: '其他'
				}],
				value: '',
				url: '',
				userInfo: {},
				page: 1,
				ossInfo: {},
				dataObj: {
					policy: '',
					signature: '',
					key: '',
					ossaccessKeyId: '',
					dir: '',
					host: ''
				},
				chongzhi: {
					password: '',
					newpassword: '',
					newpassworda: ''
				},
				PreviewPic: false,
				showFileList: false,
				fileList: [{
					name: '',
					url: ''
				}],
				areaList: [],
				// 个人认证信息
				infos: {
					cnname: '',
					cardnum: '',
					cardpicz: '',
					cardpicf: '',
				},
				info: {
					cnname: '',
					cardnum: '',
					cardpicz: '',
					cardpicf: '',
					gerenyinhang:'',
					gerenkahao:'',
					gerenshstatus: 9,
					biaoqian: [],
					// 企业名称
					qiyemingcheng: '',
					// 信用代码
					xinyongdaima: '',
					// 营业执照
					qiyepic: '',
					// 法人
					fr_name: '',
					// 	法人身份证号
					fr_cardnum: '',
					// 	法人身份证正
					fr_cardpicz: '',
					// 法人身份证反
					fr_cardpicf: '',
					qiyeshstatus: 9,
					province: '',
					city: '',
					district: '',
					member_id: 0,
					type: 1,
					lianxiren:'',
					lianxidianhua:'',
					gonghu:'',
					kaihuhang:'',
					qitazizhi:'',
					address:'',
					kpdianhua:'',
					hanghao:''
				},
				biaoqians: '',
				per_page: 1,
				total: 0,
				// 收藏数据
				isCollectList: [],
				// 报名数据
				baomingList: [],
				// 我参与的标的
				canyuList: [],
				// 我竞得的价格
				jingdeList: [],
				// 我关注的企业
				qiyeList: [],
				// 企业营业执照
				qiyepic: '',
				// 身份证正反
				fr_cardpicz: '',
				fr_cardpicf: '',
				// 城市数据
				cityList: [],
				// 区数据
				district: [],
				province: '',
				city: '',
				districts: '',
				// 个人身份证正反
				cardpicz: '',
				cardpicf: '',
				chujiailist: [],
				paypassword: '',
				paypasswords: '',
				jiu_password: '',
				jifenInfo: [],
				qtfileList:[],
				beizhu:'',//审核备注
			}
		},
		created() {
			this.url = this.$Pc.ossUrl
			this.userInfo = JSON.parse(localStorage.getItem('userInfo'))
			this.getFileName()
			this.getCity()
			if (this.$route.query.selectindex) {
				this.selectIndex = this.$route.query.selectindex
			}
			if (!localStorage.getItem('userInfo')) {
				this.$router.push('/#/login?isReject=false')
			} else {
				this.isrenzheng();
			}

		},
		watch: {
			selectIndex() {
				this.page = 1
				if (this.selectIndex == 5) {
					this.getcanyuBiao()
				} else if (this.selectIndex == 7) {
					this.getQiye()
				} else if (this.selectIndex == 3) {
					this.getbiaodi()
				} else if (this.selectIndex == 6) {
					this.jingdebaiodi()
				} else if (this.selectIndex == 4) {
					this.baomingbiaodi()
				}
			},
			page() {
				if (this.selectIndex == 5) {
					this.getcanyuBiao()
				} else if (this.selectIndex == 7) {
					this.getQiye()
				} else if (this.selectIndex == 3) {
					this.getbiaodi()
				} else if (this.selectIndex == 6) {
					this.jingdebaiodi()
				} else if (this.selectIndex == 4) {
					this.baomingbiaodi()
				}
			},
		},
		filters: {
			province(id) {
				let arae = area.area.province_list
				let res = arae[id]
				return res
			},
			city(id) {
				let arae = area.area.city_list
				let res = arae[id]
				return res
			},
			formatDate: function(value) {
				value = value * 1000
				let date = new Date(value);
				// console.log(date)
				let y = date.getFullYear();
				// console.log(y)
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s;
			}
		},
		methods: {
			gohome() {
				this.$router.push({ //核心语句
					path: '/integral', //跳转的路径
				})
			},
			setChujia(item) {
				ajax.chujiaList({
					bd_id: item.id,
					page: 1
				}).then(res => {
					this.dialogVisible = true
					this.chujiailist = res.data;
				})
			},
			isrenzheng() {
				ajax.isRenzheng({
					member_id: this.userInfo.id,
				}).then(res => {
					if (res.code == 1) {
						let { enterpm, enterqiye, enteruser, enterwt } = res.data

						this.userInfo.enteruser = res.data.enteruser
						this.userInfo.enterqiye = res.data.enterqiye
						localStorage.setItem('userInfo', JSON.stringify(this.userInfo));

						if(enterpm == 0 && enterqiye == 0 && enteruser == 0 && enterwt == 0){
							// this.centerDialogVisible = true
							this.$confirm('请尽快填充资料，完成企业或个人实名认证！', '友情提示', {
								confirmButtonText: '确定',
								// cancelButtonText: '取消',
								showCancelButton:false,
								type: 'warning',
								center: true
								
							})
						}
					}
				})
			},
			getrzgeren() {
				this.radio = 1
				this.ispassword = false
				ajax.getRenzheng({
					member_id: this.userInfo.id,
					type: 1
				}).then(res => {
					if (res.code == 1) {
						this.info.cnname = res.data.cnname
						this.info.cardnum = res.data.cardnum
						this.info.cardpicz = res.data.cardpicz
						this.info.cardpicf = res.data.cardpicf
						this.info.gerenyinhang = res.data.gerenyinhang
						this.info.gerenkahao = res.data.gerenkahao
						// if(res.data.province){
						// 	this.getCity()
						// 	this.provinceIndex(res.data.province)
						// }
						// this.info.province = res.data.province
						// this.info.city = res.data.city
						// this.info.district = res.data.district
						this.info.gerenshstatus = res.data.shstatus
						this.beizhu = res.data.beizhu
						// this.province = res.data.province
						// this.city = res.data.city
						// this.districts = res.data.district
						if(res.data.biaoqian){
							this.info.biaoqian = res.data.biaoqian.split(',')
						}
						if (res.data.shstatus == 1 || res.data.shstatus == 0) {
							this.gerenreadonly = true
						}
					}
				})
			},
			getrzqiye() {
				this.radio = 2
				this.ispassword = false
				ajax.getRenzheng({
					member_id: this.userInfo.id,
					type: 2
				}).then(res => {
					if (res.code == 1) {
						this.info.qiyemingcheng = res.data.qiyemingcheng
						this.info.xinyongdaima = res.data.xinyongdaima
						this.info.qiyepic = res.data.qiyepic
						this.info.fr_name = res.data.fr_name
						this.info.fr_cardnum = res.data.fr_cardnum
						this.info.fr_cardpicz = res.data.fr_cardpicz
						this.info.fr_cardpicf = res.data.fr_cardpicf
						this.info.qiyeshstatus = res.data.shstatus
						this.info.lianxiren = res.data.lianxiren
						this.info.lianxidianhua = res.data.lianxidianhua
						this.info.gonghu = res.data.gonghu
						this.info.kaihuhang = res.data.kaihuhang
						this.info.address = res.data.address
						this.info.kpdianhua = res.data.kpdianhua
						this.info.hanghao = res.data.hanghao
						this.beizhu = res.data.beizhu
						if(res.data.biaoqian){
							this.info.biaoqian = res.data.biaoqian.split(',')
						}
						if (res.data.qitazizhi && res.data.qitazizhi.length > 0) {
							let { qitazizhi } = res.data
							qitazizhi.forEach((item, index) => {
								let name = item.substring(item.lastIndexOf("/") + 1);
								this.qtfileList.push({ name: name, url: this.imgHttpPath + item, key: item });
							});
							this.info.qitazizhi = qitazizhi.join(",")
						}
						if (res.data.shstatus == 1 || res.data.shstatus == 0) {
							this.qiyereadonly = true
						}
					}
				})
			},
			provinceIndex(e) {
				this.province = e
				this.city = 0
				this.districts = 0
				this.info.province = this.areaList[this.province].id
				this.info.city = this.cityList[this.province].city[this.city].id
				this.info.district = this.district[this.province].city[this.city][this.districts].id
			},
			cityIndex(e) {
				this.city = e
				this.districts = 0
				this.info.city = this.cityList[this.province].city[this.city].id
				this.info.district = this.district[this.province].city[this.city][this.districts].id
			},
			districtIndex(e) {
				this.districts = e
				this.info.district = this.district[this.province].city[this.city][this.districts].id
			},
			setMobile() {
				this.$message.success('不支持修改，请重新注册！')
			},
			getCity() {
				let arae = area.area.province_list
				for (var key in arae) {
					this.areaList.push({
						id: key,
						name: arae[key]
					})
				}

				let city = area.area.city_list
				let temp = []
				for (var key in city) {
					temp.push({
						id: key,
						name: city[key]
					})
				}
				temp.forEach((el, index) => {
					if (this.cityList.length == 0) {
						this.cityList.push({
							city: [el]
						})
					} else {
						let idTitle = el.id.substr(0, 2)
						let id = this.cityList[this.cityList.length - 1].city[0].id
						let cityId = id.substr(0, 2)
						if (idTitle != cityId) {
							this.cityList.push({
								city: [el]
							})
						} else {
							let objects = this.cityList[this.cityList.length - 1]
							objects.city.push(el)
						}
					}
				})
				let county = area.area.county_list
				let temps = []
				for (var key in county) {
					temps.push({
						id: key,
						name: county[key]
					})
				}
				temps.forEach((el, index) => {
					if (this.district.length == 0) {
						this.district.push({
							city: [
								[el]
							]
						})
					} else {
						let idTitles = el.id.substr(0, 2)
						let oneArray = this.district[this.district.length - 1]
						let twoArray = oneArray.city[oneArray.city.length - 1]
						let threeArray = twoArray[twoArray.length - 1]
						let ids = threeArray.id
						let cityIds = ids.substr(0, 2)
						if (idTitles != cityIds) {
							this.district.push({
								city: [
									[el]
								]
							})
						} else {
							let oneArrays = this.district[this.district.length - 1]
							let twoArrays = oneArrays.city[oneArrays.city.length - 1]
							let threeArrays = twoArrays[twoArrays.length - 1]
							let idTitle = el.id.substr(0, 4)
							let id = threeArrays.id
							let cityId = id.substr(0, 4)
							if (idTitle != cityId) {
								this.district[this.district.length - 1].city.push([el])
							} else {
								let objects = twoArrays
								objects.push(el)
							}
						}
					}
				})

			},
			submitClick(status) {
				// if (this.info.biaoqian.length == 0) return this.$message.error('请检查信息是否填写完整')
				if(this.info.biaoqian.length > 0)
				this.info.biaoqian.forEach(el => {
					el = el * 1
				})
				let temp = JSON.parse(JSON.stringify(this.info))
				temp.biaoqian = temp.biaoqian.join(',')
				temp.member_id = this.userInfo.id
				temp.type = this.radio * 1

				ajax.rzupload({...temp,status}).then((res) => {
					if (res.code == 0) {
						this.$message.error(res.msg)
					} else {
						this.$message.success(res.msg)
						setTimeout(() => {
							this.getrzqiye()
							this.oneSubmit = 1;
						}, 1000)
					}
				})
			},
			xiugaimima() {
				if (this.chongzhi.password == '') {
					this.$message.error('请输入原密码')
				} else if (this.chongzhi.newpassword != this.chongzhi.newpassword1 || this.chongzhi.newpassword == '') {
					this.$message.error('请检查确认密码')
				} else {
					var passarr = {
						password: this.chongzhi.newpassword,
						jiu_password: this.chongzhi.password,
						member_id: this.userInfo.id,
						apptoken: this.userInfo.apptoken,
					};

					ajax.xiugaimima(passarr).then((res) => {
						if (res.code == 1) this.$message.success(res.msg);
						else this.$message.error(res.msg);
						var that = this;
						setTimeout(function() {
							that.$router.go(0)
						}, 1000);

					});
				}
			},
			nextPage() {
				// if (this.info.qiyepic) {
					if (this.info.qiyemingcheng == 'FailInRecognition' || this.info.xinyongdaima == 'FailInRecognition') {
						this.$message.error('图片过于模糊识别失败，请上传清晰照')
					}else if(!this.info.qiyemingcheng){
						this.$message.error('请检查公司名称')
					}else if(this.info.xinyongdaima.length != 18){
						this.$message.error('请检查信用代码是否正确')
					}else if(!this.info.lianxiren){
						this.$message.error('请检查业务联系人')
					}else if(!this.info.lianxidianhua){
						this.$message.error('请检查联系电话')
					} else {
						this.oneSubmit = 2
					}
				// } else {
				// 	this.$message.error('请上传营业执照')
				// }
			},
			addNext(status) {
				if(status == 3){
					// && this.info.fr_cardpicz && this.info.fr_cardpicf
					if (this.info.fr_cardnum) {
						if(this.info.fr_cardnum.length != 18){
							return this.$message.error('请检查法人证件号是否正确')
						}
						this.oneSubmit = status
					} else {
						this.$message.error('请检查法人信息')
					}
				}
				if(status == 4){
					if (this.info.address && this.info.kpdianhua && this.info.gonghu && this.info.kaihuhang ) {
						this.oneSubmit = status
					} else {
						this.$message.error('请检查开户信息')
					}
				}
			},
			addNexts() {
				// this.info.cardpicf && this.info.cardpicz &&
				console.log(this.info)
				if (this.info.cnname && this.info.cardnum && this.info.gerenyinhang && this.info.gerenkahao) {
					if (this.info.cardnum == 'FailInRecognition' || this.info.cnname == 'FailInRecognition') {
						this.$message.error('图片过于模糊识别失败，请上传清晰照')
					}else if(this.info.cardnum.length != 18){
						this.$message.error('请检查身份证号是否正确')
					} else {
						this.oneSubmits = 2
					}
				} else {
					this.$message.error('请检查个人信息')
				}
			},
            uploadCallbacks(src) {
                // 图片上传回调
                this.info.qitazizhi = src.map((item) => item.key).join(",");
                this.qtfileList = src
                // console.log(this.form.image,src)
            },
			submitClicks(status) {
				// if (this.info.biaoqian.length == 0) return this.$message.error('请检查信息是否填写完整')
				if(this.info.biaoqian.length > 0)
				this.info.biaoqian.forEach(el => {
					el = el * 1
				})
				let temp = JSON.parse(JSON.stringify(this.info))
				temp.biaoqian = temp.biaoqian.join(',')
				temp.member_id = this.userInfo.id
				ajax.rzupload({...temp,status}).then((res) => {
					if (res.code == 1) {
						this.$message.success(res.msg)
						setTimeout(() => {
							this.getrzgeren()
							this.oneSubmits = 1;
							// this.$router.push('userInfo')
						}, 1000)
					} else {
						/*
						this.$message.success(res.msg)
						this.oneSubmit = 1
						this.oneSubmits = 1
						this.info = this.$options.data().info;
						this.cardpicz = ''
						this.cardpicf = ''
						this.fr_cardpicz = ''
						this.fr_cardpicf = ''*/
						this.$message.error(res.msg)
					}
				})
			},
			xiazaiwenjian() {
				this.$message.success('暂未开放')
			},
			quxiaoClick() {
				this.biaoqians = ''
				this.oneSubmit = 1
				this.oneSubmits = 1
			},
			beforeUpload(file) {
				const _self = this
				return new Promise((resolve, reject) => {
					ajax.aliosspolicy().then((response) => {
							const isJPG = file.type === 'image/jpeg'
							const isGIF = file.type === 'image/gif'
							const isPNG = file.type === 'image/png'
							const isBMP = file.type === 'image/bmp'
							if (!isJPG && !isGIF && !isPNG && !isBMP) {
								window.alert('上传图片格式文件，必须是JPG/GIF/PNG/BMP 格式!')
								return
							}
							var fileExtension = file.name.substring(
								file.name.lastIndexOf('.') + 1
							)
							_self.dataObj.policy = response.data.policy
							_self.dataObj.signature = response.data.signature
							_self.dataObj.ossaccessKeyId = response.data.accessid
							_self.dataObj.key =
								response.data.dir + _self.getFileName() + '.' + fileExtension
							_self.dataObj.dir = response.data.dir
							_self.dataObj.host = response.data.host
							console.log(response)
							resolve(true)
						})
						.catch((err) => {

							reject(false)
						})
				})

			},
			// 目前不带证件照片识别
			handleUploadSuccess(res, file, type) {
				this.showFileList = true
				this.fileList = [{
					name: file.name,
					url: this.ossUrl + this.dataObj.key
				}]
				// return
				if (type == 'avatar') {
					this.uploadAvatar()
				} else if (type == 'yingye') {
					this.info.qiyepic = this.dataObj.key
					// this.uploadYingye()
				} else if (type == 'fr_cardpicz' || type == 'fr_cardpicf') {
					if (type == "fr_cardpicz") this.info.fr_cardpicz = this.dataObj.key
					if (type == "fr_cardpicf") this.info.fr_cardpicf = this.dataObj.key
					// this.uploadFaren(type)
				} else if (type == 'cardpicz' || type == 'cardpicf') {
					if (type == "cardpicz") this.info.cardpicz = this.dataObj.key
					if (type == "cardpicf") this.info.cardpicf = this.dataObj.key
					// this.uploadFarens(type)
				}
			},
			// 原来带证件扫描识别的代码
			// handleUploadSuccess(res, file, type) {
			// 	this.showFileList = true
			// 	this.fileList = [{
			// 		name: file.name,
			// 		url: this.ossUrl + this.dataObj.key
			// 	}]
			// 	// return
			// 	if (type == 'avatar') {
			// 		this.uploadAvatar()
			// 	} else if (type == 'yingye') {
			// 		this.qiyepic = this.dataObj.key
			// 		this.uploadYingye()
			// 	} else if (type == 'fr_cardpicz' || type == 'fr_cardpicf') {
			// 		if (type == "fr_cardpicz") this.fr_cardpicz = this.dataObj.key
			// 		if (type == "fr_cardpicf") this.fr_cardpicf = this.dataObj.key
			// 		this.uploadFaren(type)
			// 	} else if (type == 'cardpicz' || type == 'cardpicf') {
			// 		if (type == "cardpicz") this.cardpicz = this.dataObj.key
			// 		if (type == "cardpicf") this.cardpicf = this.dataObj.key
			// 		this.uploadFarens(type)
			// 	}
			// },
			uploadFarens(type) {
				var ishttps = 'https:' == document.location.protocol ? true : false;
				var path = 'http://dm-51.data.aliyun.com/rest/160601/ocr/ocr_idcard.json'
				if (ishttps) {
					path = 'https://dm-51.data.aliyun.com/rest/160601/ocr/ocr_idcard.json'
				}
				axios.defaults.headers.common["Authorization"] = "APPCODE " + '3e52a6065bce4c5083c21e54c10cab24'
				axios.defaults.headers.common["Content-Type"] = "application/json; charset=UTF-8"
				axios.post(path, {
						image: this.url + (type == 'cardpicz' ? this.cardpicz : this.cardpicf),
						configure: {
							side: type == 'cardpicz' ? 'face' : 'back',
						}
					})
					.then(res => {
						if (res) {
							if (type == 'cardpicz') {
								this.info.cardpicz = this.cardpicz
								this.info.cardnum = res.data.num
								this.info.cnname = res.data.name
								this.gerenreadonly = true
							}
							if (type == 'cardpicf') this.info.cardpicf = this.cardpicf
						}
					})
					.catch(err => {
						this.$message.error('请检查身份证信息')
					})
			},
			uploadFaren(type) {
				var ishttps = 'https:' == document.location.protocol ? true : false;
				var path = 'http://dm-51.data.aliyun.com/rest/160601/ocr/ocr_idcard.json'
				if (ishttps) {
					path = 'https://dm-51.data.aliyun.com/rest/160601/ocr/ocr_idcard.json'
				}
				axios.defaults.headers.common["Authorization"] = "APPCODE " + '3e52a6065bce4c5083c21e54c10cab24'
				axios.defaults.headers.common["Content-Type"] = "application/json; charset=UTF-8"
				axios.post(path, {
						image: this.url + (type == 'fr_cardpicz' ? this.fr_cardpicz : this.fr_cardpicf),
						configure: {
							side: type == 'fr_cardpicz' ? 'face' : 'back',
						}
					})
					.then(res => {
						if (res) {
							if (type == 'fr_cardpicz') {
								if (res.data.name == this.info.fr_name) {
									this.info.fr_cardpicz = this.fr_cardpicz
									this.info.fr_cardnum = res.data.num
									this.farenreadonly = true
								}
							}
							if (type == 'fr_cardpicf') this.info.fr_cardpicf = this.fr_cardpicf
							if (type == 'cardpicz') this.info.cardpicz = this.cardpicz
							if (type == 'cardpicf') this.info.cardpicf = this.cardpicf
						}
					})
					.catch(err => {
						this.$message.error('请检查身份证信息')
					})
			},
			uploadYingye() {
				var ishttps = 'https:' == document.location.protocol ? true : false;
				var path = 'http://bizlicense.market.alicloudapi.com/rest/160601/ocr/ocr_business_license.json'
				if (ishttps) {
					path = 'https://bizlicense.market.alicloudapi.com/rest/160601/ocr/ocr_business_license.json'
				}
				axios.defaults.headers.common["Authorization"] = "APPCODE " + '3e52a6065bce4c5083c21e54c10cab24'
				axios.defaults.headers.common["Content-Type"] = "application/json; charset=UTF-8"
				axios.post(path, {
						image: this.url + this.qiyepic
					})
					.then(res => {
						console.log(res)
						if (res.status == 200) {
							this.info.qiyemingcheng = res.data.name
							this.info.xinyongdaima = res.data.reg_num
							this.info.qiyepic = this.qiyepic
							this.info.fr_name = res.data.person
							this.qiyereadonly = true
						}
					})
					.catch(err => {
						this.$message.error('请上传真实营业执照')
					})
			},
			getFileName() {
				let one = 'uploads/image/'
				var date = new Date();
				var year = date.getFullYear();
				var month = date.getMonth() + 1;
				var day = date.getDate();
				if (month < 10) {
					month = "0" + month;
				}
				if (day < 10) {
					day = "0" + day;
				}
				let two = year.toString() + month.toString() + day.toString()
				let time = ((new Date().getTime() * 0.001).toFixed(0))
				let ones = Math.ceil(Math.random() * 9)
				let oness = Math.ceil(Math.random() * 9)
				let onesss = Math.ceil(Math.random() * 9)
				// let temp = one + two + '/' + time + ones + oness + onesss
				let temp = time + ones + oness + onesss
				return temp
			},
			uploadAvatar() {
				ajax.saveheader({
					member_id: this.userInfo.id,
					avatar: this.dataObj.key
				}).then(res => {
					this.userInfo.avatar = res.data.avatar
					localStorage.setItem('userInfo', JSON.stringify(this.userInfo))
				})
			},
			// 我参与的标的
			getcanyuBiao() {
				ajax.mycybiaodilist({
					member_id: this.userInfo.id,
					page: this.page
				}).then(res => {
					this.canyuList = res.data.data
					this.per_page = res.data.per_page
					this.total = res.data.total
				})
			},
			getQiye() {
				ajax.myscqiyelist({
					member_id: this.userInfo.id,
					page: this.page
				}).then(res => {
					this.qiyeList = res.data.data
					this.per_page = res.data.per_page
					this.total = res.data.total
				})
			},
			getbiaodi() {
				ajax.myscbiaodilist({
					member_id: this.userInfo.id,
					page: this.page
				}).then(res => {
					this.isCollectList = res.data.data
					this.per_page = res.data.per_page
					this.total = res.data.total
				})
			},
			jingdebaiodi() {
				ajax.myjdbiaodilist({
					member_id: this.userInfo.id,
					page: this.page
				}).then(res => {
					this.jingdeList = res.data.data
					this.per_page = res.data.per_page
					this.total = res.data.total
				})
			},
			baomingbiaodi() {
				ajax.mybmbiaodilist({
					member_id: this.userInfo.id,
					page: this.page
				}).then(res => {
					this.baomingList = res.data.data
					this.per_page = res.data.per_page
					this.total = res.data.total
				})
			},
			quxiaoshouchang(item) {
				ajax.scbiaodi({
					member_id: this.userInfo.id,
					bd_id: item.id,
					isquxiao: 0
				}).then(res => {
					this.$message.success(res.msg)
					this.getbiaodi()
				})
			},
			quxiaoqiye(item) {
				ajax.scqiye({
					member_id: this.userInfo.id,
					qiye_id: item.qiye_id,
					isquxiao: 0
				}).then(res => {
					if (res.code == 1) {
						this.$message.success('取消成功！')
						this.getQiye()
					} else {
						this.$message.error(res.msg)
					}
				})
			},
			qiyeListClick(item) {
				this.$router.push({ //核心语句
					path: '/auctionfirmDetail', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: item.qiye_id
					}
				})
			},
			biaodiDetail(item) {
				this.$router.push({ //核心语句
					path: '/auctionDetail/' + item.id, //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: item.id,
						ispaimaih: false
					}
				})
			},
			handleSizeChange(e) {
				this.page = e
			},
			handleCurrentChange(e) {
				this.page = e
			}
		}
	}
</script>

<style lang="scss" scoped="scoped">
	/* 选中后的字体颜色 */
	.el-radio__input.is-checked+.el-radio__label {
		color: #A80012 !important;
	}

	/* 选中后小圆点的颜色 */
	.el-radio__input.is-checked .el-radio__inner {
		background: #A80012 !important;
		border-color: #A80012 !important;
	}

	.UserInfo {
		background-color: #f7f7f7;
	}

	.UserInfoCenter {
		width: 1200px;
		padding-top: 20px;
		padding-bottom: 50px;
		box-sizing: border-box;
		margin: 0 auto;
		min-height: 1000px;
		display: flex;
		-js-display: flex;
	}

	.UserInfoCenter_left {
		width: 196px;
	}

	.UserInfoCenter_left_one {
		padding-left: 36px;
		box-sizing: border-box;
		width: 100%;
		height: 50px;
		background: #FFFFFF;
		line-height: 50px;
		font-size: 16px;
		font-weight: 400;
		border-bottom: 1px solid #EEEEEE;
	}

	.UserInfoCenter_right {
		padding-bottom: 50px;
		box-sizing: border-box;
	}

	.UserInfoCenter_left_two {
		padding-left: 36px;
		box-sizing: border-box;
		width: 100%;
		height: 50px;
		background: #FFFFFF;
		font-size: 14px;
		font-weight: 400;
		display: flex;
		-js-display: flex;
		align-items: center;
		border-left: 4px solid #FFFFFF;
		cursor: pointer;
	}

	.UserInfoCenter_left_two img {
		display: none;
	}

	.UserInfoCenter_left_two_img {
		display: block !important;
	}

	.UserInfoCenter_left_twos {
		border-left: 4px solid #A80012;
	}

	.UserInfoCenter_left_two img {
		margin-right: 10px;
		width: 16px;
		height: 16px;
	}

	.UserInfoCenter_right {
		width: 984px;
		min-height: 500px;
		background: #FFFFFF;
		margin-left: 20px;
		padding-top: 46px;
		box-sizing: border-box;
	}

	.UserInfoCenter_right_one {
		width: 880px;
		height: 100px;
		padding-bottom: 34px;
		box-sizing: border-box;
		border-bottom: 1px solid #EEEEEE;
		margin: 0 auto;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.UserInfoCenter_right_one_i {
		width: 56px;
		height: 56px;
	}

	.UserInfoCenter_right_one_div {
		height: 56px;
		display: flex;
		-js-display: flex;
		flex-direction: column;
		justify-content: space-between;
		margin-left: 30px;
	}

	.UserInfoCenter_right_one_div_one {
		display: flex;
		-js-display: flex;
		align-items: center;
		font-size: 18px;
		font-weight: 400;
		color: #333333;
	}

	.UserInfoCenter_right_one_div_one_a {
		color: #5A5A5A;
		font-size: 14px;
	}

	.UserInfoCenter_right_one_div_one_ab {
		width: 75px;
		height: 16px;
		background: #F1F2F3;
		border-radius: 2px;
		margin-right: 5px;
		font-size: 10px;
		font-weight: 400;
		color: #5A5A5A;
		text-align: center;
		line-height: 16px;
	}

	.UserInfoCenter_right_one_div_one_abs {
		width: 75px;
		height: 16px;
		background: #FFFFFF;
		border: 1px solid #A80012;
		border-radius: 2px;
		text-align: center;
		line-height: 16px;
		font-size: 10px;
		font-weight: 400;
		color: #A80012;
		margin-right: 10px;
	}

	.UserInfoCenter_right_one_div_two {
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.UserInfoCenter_right_one_div_two img {
		width: 20px;
		height: 14px;
		margin-right: 10px;
	}

	.UserInfoCenter_right_one_div_two_div {
		font-size: 12px;
		font-weight: 400;
		color: #5A5A5A;
		width: 800px;
	}

	.UserInfoCenter_right_one_div_two_div span {
		color: #A80012;
	}

	.UserInfoCenter_right_two {
		width: 898px;
		font-size: 16px;
		font-weight: bold;
		color: #333333;
		margin: 39px auto 20px;
	}

	.UserInfoCenter_right_three {
		width: 898px;
		height: 180px;
		background: #FAFAFA;
		border: 1px solid #ECECEC;
		margin: 22px auto;
	}

	.UserInfoCenter_right_three_o {
		width: 898px;
		height: 52px;
		background: #FFFFFF;
		border-bottom: 1px solid #ECECEC;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
		padding: 0 18px;
		box-sizing: border-box;
		font-size: 14px;
		font-weight: 400;
		color: #5A5A5A;
	}

	.UserInfoCenter_right_three_o_roght {
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.UserInfoCenter_right_three_o_roght div {
		width: 120px;
		height: 26px;
		background: #A80012;
		border-radius: 4px;
		text-align: center;
		line-height: 26px;
		font-size: 14px;
		font-weight: bold;
		color: #FFFFFF;
	}

	.UserInfoCenter_right_three_t {
		display: flex;
		-js-display: flex;
		padding: 20px 18px;
		box-sizing: border-box;
	}

	.UserInfoCenter_right_threes {
		width: 898px;
		height: 128px;
		background: #FFFFFF;
		border: 1px solid #ECECEC;
		margin: 0 auto;
		margin-bottom: 22px;
	}

	.UserInfoCenter_right_three_t_one {
		display: flex;
		-js-display: flex;
		align-items: center;
		height: 88px;
		border-right: 1px solid #E6E6E6;
		padding-right: 28px;
		box-sizing: border-box;
	}

	.UserInfoCenter_right_three_t_i {
		width: 88px;
		height: 88px;
		background-color: #A80012;
	}

	.UserInfoCenter_right_three_t_r {
		display: flex;
		-js-display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		height: 88px;
		width: 155px;
		margin-left: 19px;
	}

	.UserInfoCenter_right_three_t_two {
		width: 127px;
		height: 88px;
		display: flex;
		-js-display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		font-size: 14px;
		font-weight: 400;
		color: #5A5A5A;
		border-right: 1px solid #E6E6E6;
	}

	.UserInfoCenter_right_three_t_two>div:nth-child(2) {
		color: #A80012;
		margin-top: 5px;
	}

	.UserInfoCenter_right_three_t_three {
		height: 88px;
		display: flex;
		-js-display: flex;
		flex-direction: column;
		justify-content: space-between;
		align-items: center;
		margin-left: 30px;
	}

	.UserInfoCenter_right_three_t_three_one {
		width: 96px;
		height: 32px;
		background: #FAFAFA;
		border: 1px solid #A80012;
		border-radius: 4px;
		text-align: center;
		line-height: 32px;
		font-size: 14px;
		font-weight: 400;
		color: #A80012;
	}

	.UserInfoCenter_right_three_t_three_ones {
		width: 96px;
		height: 32px;
		background: #FAFAFA;
		border: 1px solid #B3B3B3;
		border-radius: 4px;
		text-align: center;
		line-height: 32px;
		font-size: 14px;
		font-weight: 400;
		color: #999999;
	}

	.UserInfoCenter_right_three_t_three>div {
		width: 96px;
		height: 32px;
		background: #FAFAFA;
		/* border: 1px solid #A80012; */
		border-radius: 4px;
		text-align: center;
		line-height: 32px;
		font-size: 14px;
		font-weight: 400;
		/* color: #A80012; */
	}

	.UserInfoCenter_right_three_t_three_div {
		border: 1px solid #A80012;
		color: #A80012;
	}


	.UserInfoCenter_right_slectOne_one {
		height: 111px;
		width: 880px;
		display: flex;
		-js-display: flex;
		align-items: center;
		margin: 0 auto;
		border-bottom: 3px dotted #EEEEEE;
	}

	.UserInfoCenter_right_slectOne_one_1 {
		display: flex;
		-js-display: flex;
		justify-content: center;
		align-items: center;
		font-size: 16px;
		font-weight: 400;
		color: #333333;
		width: 189px;
		height: 111px;
	}

	.UserInfoCenter_right_slectOne_one_1 img {
		width: 30px;
		height: 30px;
		margin-right: 20px;
	}

	.UserInfoCenter_right_slectOne_one_2 {
		width: 360px;
		font-size: 12px;
		font-weight: 400;
		color: #666666;
		margin-left: 25px;
	}

	.UserInfoCenter_right_slectOne_one_3 {
		width: 80px;
		height: 32px;
		background: #FFFFFF;
		border: 1px solid #A80012;
		border-radius: 4px;
		text-align: center;
		line-height: 32px;
		font-size: 14px;
		font-weight: 400;
		color: #A80012;
		margin-left: 200px;
		cursor: pointer;
	}

	.UserInfoCenter_right_fours {
		padding-top: 30px;
		width: 700px;
		margin: 0 auto;
		box-sizing: border-box;
	}

	.UserInfoCenter_right_fours_title {
		display: flex;
		-js-display: flex;
		justify-content: center;
		align-items: center;
	}


	.UserInfoCenter_right_fours_one {
		width: 500px;
		margin: 0 auto;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.UserInfoCenter_right_fours_ones {
		display: flex;
		-js-display: flex;
		flex-direction: column;
		align-items: center;
		font-size: 14px;
		font-weight: 400;
		color: #999999;
	}

	.UserInfoCenter_right_fours_oness {
		width: 24px;
		height: 24px !important;
		background: #D6D6D6;
		border-radius: 50%;
		text-align: center;
		line-height: 24px;
		font-size: 14px;
		font-weight: bold;
		color: #FFFFFF;
		margin-bottom: 16px;
	}

	.UserInfoCenter_right_fours_onessred {
		width: 24px;
		height: 24px !important;
		background: #A80012;
		border-radius: 50%;
		text-align: center;
		line-height: 24px;
		font-size: 14px;
		font-weight: bold;
		color: #FFFFFF;
		margin-bottom: 16px;
	}

	.UserInfoCenter_right_fours_onesrr {
		color: #A80012 !important;
	}

	.UserInfoCenter_right_fours_onesI {
		position: relative;
		top: -15px;
	}

	.UserInfoCenter_right_fours_two {
		margin-top: 53px;
	}

	.UserInfoCenter_right_fours_two_s {
		width: 450px;
		display: flex;
		-js-display: flex;
		align-items: baseline;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		margin-bottom: 16px;
	}

	.UserInfoCenter_right_fours_two_ss {
		width: 550px;
		// height: 500px;
		font-weight: 400;
		color: #333333;
		margin-bottom: 16px;
		display: flex;
		-js-display: flex;
		align-items: baseline;
	}

	.UserInfoCenter_right_fours_two_sssss>div {
		display: flex;
		-js-display: flex;
		align-items: baseline;
	}

	.UserInfoCenter_right_fours_two_s span {
		color: #A80012;
	}

	.UserInfoCenter_right_fours_two_sssf span {
		color: #A80012;
	}

	.UserInfoCenter_right_fours_two_sss img {
		width: 192px;
		height: 128px;
	}

	.UserInfoCenter_right_fours_two_sss_one {
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.UserInfoCenter_right_fours_two_ss_w {
		width: 438px;
		height: 38px;
		font-size: 14px;
		font-weight: 400;
		color: #777777;
		margin-top: 26px;
	}

	.UserInfoCenter_right_fours_two_ss_w span {
		color: #A80012;
	}

	.UserInfoCenter_right_fours_two_ss_ws {
		width: 300px;
		height: 40px;
		background: #A80012;
		border-radius: 4px;
		font-size: 16px;
		font-weight: 400;
		color: #FFFFFF;
		line-height: 40px;
		text-align: center;
		margin-top: 74px;
	}

	.UserInfoCenter_right_fours_two_sss_onesss {
		display: flex;
		-js-display: flex;
		align-items: center;
		margin-top: 80px;
		padding-left: 100px;
		box-sizing: border-box;
	}

	.UserInfoCenter_right_fours_two_sss_onesss_one {
		width: 400px;
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
	}

	.UserInfoCenter_right_fours_two_sss_onesss_one_div {
		margin-bottom: 22px;
		font-size: 14px;
		font-weight: 400;
		color: #666666;
		display: flex;
		-js-display: flex;
		align-items: center;
		width: 25%;
	}

	.UserInfoCenter_right_fours_two_sss_onesss_one_div_d {
		width: 14px;
		height: 14px;
		background: #F7F7F7;
		border: 1px solid #C2C2C2;
		border-radius: 2px;
		margin-right: 5px;
	}

	.UserInfoCenter_right_fours_two_ss_wsss {
		width: 150px;
		height: 40px;
		background: #A80012;
		border-radius: 4px;
		text-align: center;
		line-height: 40px;
		font-size: 16px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #FFFFFF;
		cursor: pointer;
	}

	.UserInfoCenter_right_fours_two_ss_wss {
		width: 150px;
		height: 40px;
		background: #E6E6E6;
		border-radius: 4px;
		text-align: center;
		line-height: 40px;
		font-size: 16px;
		font-weight: 400;
		color: #5A5A5A;
		cursor: pointer;
	}

	.UserInfoCenter_right_fours_two_ss_shengshi {
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auctionpage {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
	}

	/deep/.el-pagination.is-background .el-pager li:not(.disabled).active {
		background-color: #A80012;
		color: white;
	}


	.About_title {
		min-height: 1000px;
		width: 1200px;
		margin: 0 auto;
		padding-top: 50px;
		box-sizing: border-box;
	}

	.About_title_one {
		font-size: 22px;
		font-weight: bold;
		color: #333333;
		padding: 0 80px;
		box-sizing: border-box;
	}

	.info {
		width: 400px;
		margin: 30px 0 0;
		padding: 0 140px;
		box-sizing: border-box;
	}

	.UserInfoCenter_right_fours_two_s {
		width: 450px;
		display: flex;
		-js-display: flex;
		align-items: baseline;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		margin-bottom: 16px;
		position: relative;
	}

	.UserInfoCenter_right_fours_two_s span {
		color: #A80012;
	}

	.infobtn {
		width: 450px;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 40px;
	}

	.infobtn>div:nth-child(1) {
		width: 100px;
		height: 35px;
		background-color: #A80012;
		text-align: center;
		line-height: 35px;
		color: white;
		font-weight: 500;
		font-size: 14px;
		border-radius: 5px;
		flex-shrink: 0;
		cursor: pointer;
	}

	.infobtn>div:nth-child(2) {
		width: 100px;
		height: 35px;
		background-color: #999999;
		text-align: center;
		line-height: 35px;
		color: white;
		font-weight: 500;
		font-size: 14px;
		border-radius: 5px;
		flex-shrink: 0;
		margin-left: 30px;
		cursor: pointer;
	}

	.UserInfoCenter_right_fours_two_s_view {
		height: 36px;
		width: 100px;
		text-align: center;
		line-height: 36px;
		position: absolute;
		right: 30px;
		font-size: 12px;
		font-weight: 300;
		color: #A80012;
		cursor: pointer;
	}

	.chujialists {
		width: 100%;
		height: 450px;
		overflow-y: scroll;
	}

	.chujialist {
		width: 100%;
		height: 100px;
		border-top: 1px solid #e5e5e5;
		border-left: 1px solid #e5e5e5;
	}

	.chujialist tr td {
		width: 333px;
		background-color: #fff;
		word-break: break-all;
		border-right: 1px solid #e5e5e5;
		border-bottom: 1px solid #e5e5e5;
		padding: 8px 0 8px 20px;
		color: #333333;
	}

	.password_addd {
		width: 520px;
		margin: 79px auto 0;
	}

	.demo-input-suffix {
		display: flex;
		align-items: center;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		margin-bottom: 16px;
	}

	.jifen_title {
		width: 800px;
		height: 120px;
		position: relative;
		margin: 36px auto 0;
	}

	.jifen_title_div {
		width: 800px;
		height: 120px;
		position: absolute;
		top: 0;
		left: 0;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 56px;
		box-sizing: border-box;
	}

	.jifen_title_div_left {
		display: flex;
		flex-direction: column;
		align-items: center;
		font-size: 30px;
		font-weight: bold;
		color: #FFFFFF;
	}

	.jifen_title_div_left>div:nth-child(2) {
		font-size: 16px;
		font-weight: 400;
		color: #FFFFFF;
		margin-top: 0px;
	}

	.jifen_title_div_left_riht {
		width: 144px;
		height: 36px;
		background: #FFFFFF;
		border-radius: 18px;
		text-align: center;
		line-height: 36px;
		font-size: 16px;
		font-weight: bold;
		color: #F55555;
	}

	.jifen_bottom {
		width: 800px;
		min-height: 100px;
		background: #FCFCFC;
		border: 1px solid #F7F7F7;
		border-radius: 4px;
		margin: 0 auto;
		padding-bottom: 20px;
		box-sizing: border-box;
	}

	.jifen_bottom_view {
		height: 96px;
		width: 719px;
		border-bottom: 2px dashed #999999;
		margin: 0 auto;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20px;
		box-sizing: border-box;
	}

	.jifen_bottom_view_left {
		font-size: 16px;
		font-weight: 400;
		color: #333333;
	}

	.jifen_bottom_view_left>div:nth-child(2) {
		font-size: 14px;
		font-weight: 400;
		color: #999999;
		margin-top: 8px;
	}

	.jifen_bottom_view_right {
		font-size: 24px;
		font-weight: 400;
		color: #A80012;
	}

	.userInfoEnter {
		width: 892px;
		margin: 41px auto 0;

	}

	.userInfoEnter_title {
		display: flex;
		align-items: center;
		font-size: 15px;
		font-weight: 400;
		color: #333333;

	}

	.userInfoEnter_title_select {
		padding-bottom: 5px;
		box-sizing: border-box;
		color: #A80012;
		border-bottom: 2px solid #A80012;
	}

	.userInfoEnter_title>div {
		margin-right: 50px;
		cursor: pointer;
		padding-bottom: 5px;
		box-sizing: border-box;
	}

	.userInfoEnter_titles {
		width: 892px;
		height: 42px;
		background: #F7F7F7;
		display: flex;
		align-items: center;
		margin-top: 24px;
		margin-bottom: 26px;
	}

	.userInfoEnter_titles>div:nth-child(1) {
		width: 420px;
		text-align: center;
	}

	.userInfoEnter_titles>div:nth-child(2) {
		width: 160px;
		text-align: center;
	}

	.userInfoEnter_titles>div:nth-child(3) {
		width: 120px;
		text-align: center;
	}

	.userInfoEnter_titles>div:nth-child(4) {
		width: 194px;
		text-align: center;
	}

	.userInfoEnter_div {
		width: 892px;
		height: 164px;
		background: #FFFFFF;
		border: 1px solid #E6E6E6;
		margin-bottom: 18px;
	}

	.userInfoEnter_div_title {
		width: 892px;
		height: 38px;
		background: #F7F7F7;
		display: flex;
		align-items: center;
		padding-left: 28px;
		box-sizing: border-box;
		font-size: 12px;
		font-weight: 400;
		color: #777777;
	}

	.userInfoEnter_div_title>div:nth-child(1) {
		margin-right: 50px;
	}

	.userInfoEnter_div_bottom {
		width: 892px;
		height: 126px;
		display: flex;
		align-items: center;

	}

	.userInfoEnter_div_bottom_o {
		height: 126px;
		width: 420px;
		display: flex;
		align-items: center;
		border-right: 1px solid #E6E6E6;
		padding-left: 28px;
		box-sizing: border-box;
	}

	.userInfoEnter_div_bottom_o>img {
		width: 80px;
		height: 80px;
	}

	.userInfoEnter_div_bottom_o>div {
		font-size: 12px;
		font-family: Microsoft YaHei;
		font-weight: 400;
		color: #333333;
		width: 235px;
		margin-left: 20px;
	}

	.userInfoEnter_div_bottom_t {
		height: 126px;
		width: 160px;
		border-right: 1px solid #E6E6E6;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		font-weight: bold;
		color: #333333;
	}

	.userInfoEnter_div_bottom_th {
		height: 126px;
		width: 120px;
		border-right: 1px solid #E6E6E6;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		font-weight: bold;
		color: #333333;
	}

	.userInfoEnter_div_bottom_f {
		width: 194px;
		height: 126px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		font-weight: 400;
		color: #999999;
	}

	.userInfoEnter_div_bottom_f>div:hover {
		color: #A80012;
	}

	.userInfoEnter_div_bottom_f_o {
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		margin-top: 5px;
		cursor: pointer;
	}
	::v-deep{
		.el-upload-list,.el-upload__tip{
			max-width: 298px;
		}
		
	}
</style>
