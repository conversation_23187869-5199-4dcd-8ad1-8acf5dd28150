<template>
	<div id="payPwd">
		<input ref="pwd" type="tel" maxlength="6" v-model="msg" class="pwd" unselectable="on" />
		<ul class="pwd-wrap" @click="focus">
			<li :class="msg.length == 0?'psd-blink':''"><i v-if="msg.length > 0"></i></li>
			<li :class="msg.length == 1?'psd-blink':''"><i v-if="msg.length > 1"></i></li>
			<li :class="msg.length == 2?'psd-blink':''"><i v-if="msg.length > 2"></i></li>
			<li :class="msg.length == 3?'psd-blink':''"><i v-if="msg.length > 3"></i></li>
			<li :class="msg.length == 4?'psd-blink':''"><i v-if="msg.length > 4"></i></li>
			<li :class="msg.length == 5?'psd-blink':''"><i v-if="msg.length > 5"></i></li>
		</ul>
	</div>
</template>
<script>
	export default {
		components: {},
		data() {
			return {
				msg: '',
			}
		},
		created() {},
		computed: {},
		watch: {
			msg(curVal) {
				if (/[^\d]/g.test(curVal)) {
					this.msg = this.msg.replace(/[^\d]/g, '');
				}
			},
		},
		methods: {
			focus() {
				this.$refs.pwd.focus();
			},
		},
		mounted() {}
	}
</script>
<style scoped>
	#payPwd input {
		width: 0.1px;
		height: 0.1px;
		color: transparent;
		position: relative;
		top: 23px;
		background: #000000;
		left: 46px;
		border: none;
		font-size: 18px;
		opacity: 0;
		z-index: -1;
	}

	/* //光标 */
	.psd-blink {
		display: inline-block;
		background: url(../assets/blink.gif) no-repeat center;
	}

	.pwd-wrap {
		width: 235px;
		height: 40px;
		padding-bottom: 1px;
		margin: 0 auto;
		background: #fff;
		border: 1px solid #ddd;
		display: flex;
		display: -webkit-box;
		display: -webkit-flex;
		cursor: pointer;
		position: absolute;
		left: 0;
		right: 0;
		/* top: 13%; */
		z-index: 10;
	}

	.pwd-wrap li {
		list-style-type: none;
		text-align: center;
		line-height: 40px;
		-webkit-box-flex: 1;
		flex: 1;
		-webkit-flex: 1;
		border-right: 1px solid #ddd;
	}

	.pwd-wrap li:last-child {
		border-right: 0;
	}

	.pwd-wrap li i {
		height: 10px;
		width: 10px;
		border-radius: 50%;
		background: #000;
		display: inline-block;
	}
</style>
