<template>
  <!-- <client-only> -->
  <vue-seamless-scroll
    :data="list"
    :class-option="optionSingleHeight"
    class="seamless-warp"
  >
  <div class="news_list">
    <div class="news_item" v-for="item,i in list" :key="i" @click="toNoticeDetail(item)">
      <div class="news_red"></div>
      <div class="news_cont">
        <div class="news_title">{{item[title]}}</div>
        <div class="news_time">{{item[date]}}</div>
      </div>
    </div>
  </div>
  </vue-seamless-scroll>
  <!-- </client-only> -->
</template>
<script>
import vueSeamlessScroll from "vue-seamless-scroll";
export default {
  props: ["list",'title','date'],
  components: {
    vueSeamlessScroll,
  },
  data() {
    return {
      listData: [{}],
      time: 0.5,
    };
  },
  computed: {
    optionSingleHeight() {
      return {
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 40,
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 0,
        step: this.time,
        limitMoveNum: 4,
      };
    },
  },
  mounted() {
    // setTimeout(() => {
    //   this.time = 1;
    // }, 3000);
  },
  methods:{
    toNoticeDetail(item) {
      if(this.title == 'title'){
        this.$router.push({ //核心语句
          path: '/announcement/detail', //跳转的路径
          query: { //路由传参时push和query搭配使用 ，作用时传递参数
            id: item.id
          }
        })
      }else if(this.title == 'pmh_name'){
        this.$router.push({ //核心语句
          path: '/noticeDetail', //跳转的路径
          query: { //路由传参时push和query搭配使用 ，作用时传递参数
            id: item.id
          }
        })
      }
    },
    goDetailNew(cateid, id) {
      this.$router.push({
        path: '/news',
        query: { //路由传参时push和query搭配使用 ，作用时传递参数
          cateid: cateid,
          id: id
        }
      })
    },
    navTo(item){
      this.$router.push({ path: "/plan/statistics" ,query:{
        fengongsi:item.fengongsi,
        ju:item.ju,
        id:item.id,
        level:item.level
      }});
    }
  }
};
</script>
<style lang="scss" scoped>
.seamless-warp {
  // margin: 10px 0;
  // height: 287px;
  overflow: hidden;
}


.news_list{
  width: 100%;
  margin-top: 5px;
  .news_item{
    position: relative;
    width: 100%;
    margin-bottom: 10px;
    padding-left: 13px;
    box-sizing: border-box;
    cursor: pointer;
    .news_red{
      position: absolute;
      left: 0;
      width: 5px;
      height: 5px;
      margin-top: 8px;
      margin-right: 8px;
      background: #E24555;
      border-radius: 50%;
    }
    .news_cont{
      width: 100%;
      .news_title{
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 13px;
        color: #343434;
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .news_time{
        margin-top: 0px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 12px;
        color: #777777;
        line-height: 16px;
      }
    }
  }
}
</style>
