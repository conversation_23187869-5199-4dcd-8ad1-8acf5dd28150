<template>
	<div class="min_wrapper_1500" style="background-color: #F7F7F7;">
		<Headers />
		<div style="background-color: #f7f7f7;padding-bottom: 80px;box-sizing: border-box;">
            <div class="newsTitle">
                当前位置：首页 | {{type[cateid]}}
            </div>
            <div class="auctionfirm_two">
                <div class="auctionfirm_two_div"
                    v-for="(item,i) in list" @click="goInfo(item)" :key="i">
                        <div class="auctionfirm_class">
                            【{{item.cate_name}}】
                        </div>
                        <div class="auctionfirm_two_div_rig_o">{{item.title}}</div>
                        <div class="auctionfirm_date">
                            {{item.addtime}}
                        </div>
                </div>
			</div>
			<div class="auctionpage">
				<el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
					:current-page.sync="page" :page-size="per_page" layout="prev, pager, next, jumper" :total="total">
				</el-pagination>
			</div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import ajax from '../../store/Ajax'
	export default {
		name: 'Auction',
		components: {
			Headers,
			FootersBottom,
			homeRight
		},
		data() {
			return {
				cateid: 29,
				url: '',
				page: 1,
				list: [],
				total: 0,
				per_page: 0,
                type:{
                    29:'采购信息',
                    30:'销售信息'
                }
			}
		},
		created() {
			this.url = this.$Pc.ossUrl
			console.log(this.$route)
            if(this.$route.query.id){
                this.cateid = this.$route.query.id
                // console.log(this.cateid,'this.cateid')
            }
			this.getPmhList()
		},
		watch: {
			page() {
				this.getPmhList()
			}
		},
		methods: {
			goInfo(item) {
				this.$router.push({ //核心语句
					path: '/announcement/detail', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: item.id,
					}
				})
			},
			getPmhList() {
				ajax.zhaobiaolist({
                    cateid:this.cateid,
                    limit:10,
					page: this.page
				}).then(res => {
					this.list = res.data.data
					this.total = res.data.total
					this.per_page = res.data.per_page
				})
			},
			handleSizeChange(e) {
				this.page = e
			},
			handleCurrentChange(e) {
				this.page = e
			}
		}
	}
</script>

<style type="text/css" scoped="scoped">
	.auctionpage {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
	}

	/deep/.el-pagination.is-background .el-pager li:not(.disabled).active {
		background-color: #D6363B;
		color: white;
	}

	/* 第二样式 */
	.auctionfirm_two {
        width: 1200px;
		min-height: 500px;
        margin: 0px auto 50px;
        padding: 0 30px;
        background: #fff;
        box-sizing: border-box;
	}

	.auctionfirm_two_div {
		display: flex;
		-js-display: flex;
		align-items: center;
		width: 100%;
		height: 76px;
		/* margin-bottom: 20px; */
        border-bottom: 1px dashed #eee;
        cursor: pointer;
	}
    .auctionfirm_two_div:last-child{
        border: 0;
    }
    .auctionfirm_class{
        font-size: 18px;
		color: #333333;
        font-weight: bold;
    }

	.auctionfirm_two_div_rig_o {
        flex: 1;
        margin-right: 10px;
		font-size: 18px;
		color: #333333;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
	}

	.auctionfirm_date {
		/* width: 350px; */
		font-size: 14px;
		font-weight: 400;
		color: #777777;
	}

    .newsTitle {
        display: flex;
        align-items: center;
        width: 1200px;
        height: 60px;
        margin: 0px auto 0;
        /* border-bottom: 1px solid #eee; */
    }
</style>
