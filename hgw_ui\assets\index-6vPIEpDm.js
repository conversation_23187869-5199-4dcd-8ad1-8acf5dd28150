import{d as e,r as a,Q as s,E as r,G as o,I as l,b as t,$ as m,o as i,c,a as n,ax as d,H as p,J as u}from"./vue-vendor-D6tHD5lA.js";import{u as g,a as h}from"./vue-router-D0b9rEnV.js";import{_ as b,S as f}from"./index-Bsdr07Jh.js";import{E as v,d as w,e as _,f as j,g as y,b as V}from"./element-plus-BiAL0NdQ.js";import{g as k,n as S,s as U,r as R}from"./utils-common-PdkFOSu3.js";import{a as x}from"./app-stores-CLUCXxRF.js";import"./app-assets-OmEvQhWx.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./crypto-vendor-CkZKNqwc.js";import"./element-icons-Cfq32zG_.js";import"./vendor-lodash-D0OfQ6x6.js";const I={class:"captcha-container"},M=["src"],q=b(e({__name:"FreedomPasswordLogin",emits:["loginSuccess"],setup(e,{emit:c}){const n=c,d=x(),p=a(!1),u=a(),g=s({username:"",password:"",captcha:"",rememberMe:!1}),h=a(""),b=a(""),f={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:2,message:"用户名长度不能少于2位",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度不能少于6位",trigger:"blur"}],captcha:[{required:!0,message:"请输入验证码",trigger:"blur"},{len:4,message:"验证码长度为4位",trigger:"blur"}]},q=async()=>{if(u.value)try{await u.value.validate(),p.value=!0;const e=await S.login({username:g.username,password:g.password,captcha:g.captcha,checkKey:b.value,loginSource:1});if(200===e.code){const a=e.result;d.freedomLogin(a),g.rememberMe?(localStorage.setItem("freedomRememberedUsername",g.username),U("freedomRememberedPassword",g.password)):(localStorage.removeItem("freedomRememberedUsername"),R("freedomRememberedPassword")),v.success("登录成功"),n("loginSuccess")}else C(),v.error(e.message||"登录失败，请检查用户名和密码")}catch(e){if(C(),e.response){switch(e.response.status){case 401:v.error("用户名、密码或验证码错误");break;case 403:v.error("账户被禁用，请联系管理员");break;case 500:v.error("服务器内部错误，请稍后重试");break;default:v.error("登录失败，请检查网络连接")}}else v.error("登录失败，请检查用户名、密码和验证码")}finally{p.value=!1}},z=async()=>{try{const e=(new Date).getTime(),a=e+Math.random().toString(36).slice(-4),s=await S.getCaptcha(a);if(200!==s.code&&!s.success)throw new Error(s.msg||"获取验证码失败");{const e=s.result||s.data;h.value=e,b.value=a}}catch(e){v.error("获取验证码失败，请稍后重试")}},C=()=>{g.captcha="",z()},E=()=>{},P=localStorage.getItem("freedomRememberedUsername"),L=k("freedomRememberedPassword");return P&&L&&(g.username=P,g.password=L,g.rememberMe=!0),z(),(e,a)=>{const s=_,c=j,n=y,d=V,b=w;return i(),r(b,{ref_key:"passwordFormRef",ref:u,model:g,rules:f,"validate-on-rule-change":!1,class:"login-form"},{default:o(()=>[l(c,{prop:"username"},{default:o(()=>[l(s,{modelValue:g.username,"onUpdate:modelValue":a[0]||(a[0]=e=>g.username=e),placeholder:"请输入用户名",size:"large",class:"login-input"},null,8,["modelValue"])]),_:1}),l(c,{prop:"password",class:"password"},{default:o(()=>[l(s,{modelValue:g.password,"onUpdate:modelValue":a[1]||(a[1]=e=>g.password=e),type:"password",placeholder:"请输入登录密码",size:"large",class:"login-input","show-password":""},null,8,["modelValue"])]),_:1}),l(c,{prop:"captcha",class:"captcha"},{default:o(()=>[t("div",I,[l(s,{modelValue:g.captcha,"onUpdate:modelValue":a[2]||(a[2]=e=>g.captcha=e),placeholder:"请输入验证码",size:"large",class:"captcha-input",maxlength:"4"},null,8,["modelValue"]),t("div",{class:"captcha-image-wrapper",onClick:C},[t("img",{src:h.value,alt:"验证码",class:"captcha-image",onError:E},null,40,M),a[4]||(a[4]=t("div",{class:"captcha-refresh-tip"},"点击刷新",-1))])])]),_:1}),l(c,{class:"form-options"},{default:o(()=>[l(n,{modelValue:g.rememberMe,"onUpdate:modelValue":a[3]||(a[3]=e=>g.rememberMe=e),class:"remember-checkbox"},{default:o(()=>a[5]||(a[5]=[m(" 记住密码 ")])),_:1,__:[5]},8,["modelValue"])]),_:1}),l(c,null,{default:o(()=>[l(d,{type:"primary",size:"large",class:"login-button",loading:p.value,onClick:q},{default:o(()=>a[6]||(a[6]=[m(" 登录 ")])),_:1,__:[6]},8,["loading"])]),_:1})]),_:1},8,["model"])}}}),[["__scopeId","data-v-937fcd62"]]),z={class:"login-container"},C={class:"login-right"},E={class:"login-form-container"},P={class:"form-logo"},L={class:"back-link"},F=b(e({__name:"index",setup(e){const a=g(),s=h(),o=c(()=>q),m=()=>{const e=s.query.returnUrl;e?a.push(e):a.push("/freedom/freedomHome")},b=()=>{a.push("/login")};return(e,a)=>(i(),n("div",z,[a[3]||(a[3]=d('<div class="login-left" data-v-674bbe25><div class="logo-3d-wrapper" data-v-674bbe25><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/1_1754963030315.png" alt="" class="logo-3d-image-1" data-v-674bbe25><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/2_1754963119472.png" alt="" class="logo-3d-image-2" data-v-674bbe25><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/3_1754963135630.png" alt="" class="logo-3d-image-3" data-v-674bbe25></div></div>',1)),t("div",C,[t("div",E,[t("div",P,[l(f,{iconName:"login-logo",className:"logo-icon"})]),a[1]||(a[1]=t("h2",{class:"welcome-title"},"回收商登录",-1)),a[2]||(a[2]=t("p",{class:"login-subtitle"},"请使用回收商账号登录",-1)),(i(),r(p(o.value),{onLoginSuccess:m},null,32)),t("div",L,[a[0]||(a[0]=t("span",null,"返回",-1)),t("a",{href:"#",onClick:u(b,["prevent"])},"主站登录")])])])]))}}),[["__scopeId","data-v-674bbe25"]]);export{F as default};
