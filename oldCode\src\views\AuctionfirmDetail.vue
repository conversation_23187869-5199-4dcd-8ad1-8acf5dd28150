<template>
	<div class="min_wrapper_1500" style="background-color: #F7F7F7;">
		<Headers />
		<div class="auctionFirmDetail_one">
		</div>
		<div class="auctionFirmDetail_two">
			<div class="auctionFirmDetail_two_view">
				<div class="auctionFirmDetail_two_view_left">
					<div class="auctionFirmDetail_two_view_left_one">
						<div class="auctionFirmDetail_two_view_left_ones">
							<img style="width: 100%;height: 100%;" :src="url + pmqiyeDetails.qiyelogo" @error="(e)=>e.target.src = pmqiyeDetails.qiyelogo ? 'https://oss.yunpaiwang.com/'+pmqiyeDetails.qiyelogo : ''" >
						</div>
						<div class="auctionFirmDetail_two_view_left_oness">
							<div>{{pmqiyeDetails.qiyemingcheng}} </div>
							<div>联系方式：{{pmqiyeDetails.qiyephone}}</div>
							<div>所在地区：{{province}}{{city}}</div>
						</div>
					</div>
					<div v-if="!isSlect" class="auctionFirmDetail_two_view_left_two" @click="isCollect(1)">+收藏</div>
					<div v-if="isSlect" class="auctionFirmDetail_two_view_left_two" @click="isCollect(0)">取消收藏</div>
				</div>
				<div class="auctionFirmDetail_two_view_right" v-html="pmqiyeDetails.jianjie">

				</div>
			</div>
		</div>
		<div style="width: 100%;height: 20px;background-color: rgb(247,247,247);"></div>
		<div class="auctionFirmDetail_three">
			<div class="auctionFirmDetail_threec">
				<div @click="selectIndex = 0"
					:class="selectIndex ==0?'auctionFirmDetail_threecs':'auctionFirmDetail_threecss'">
					累计竞价会(<span>{{pmqiyeDetails.pmh_num}}</span>)</div>
				<div @click="selectIndex = 1"
					:class="selectIndex ==1?'auctionFirmDetail_threecs':'auctionFirmDetail_threecss'">
					竞价会公告(<span>{{pmqiyeDetails.pmh_num}}</span>)</div>
				<div @click="selectIndex = 2"
					:class="selectIndex ==2?'auctionFirmDetail_threecs':'auctionFirmDetail_threecss'">
					累计标的(<span>{{pmqiyeDetails.bd_num}}</span>)</div>
				<div @click="selectIndex = 3"
					:class="selectIndex ==3?'auctionFirmDetail_threecs':'auctionFirmDetail_threecss'">
					成交标的(<span>{{pmqiyeDetails.cjbd_num}}</span>)</div>
			</div>
		</div>
		<div style="background-color: #f7f7f7;padding-bottom: 80px;box-sizing: border-box;">
			<div class="auction" v-if="selectIndex == 0">
				<div class="auction_three">
					<auction-card v-for="(item,i) in qiyePaimaihuiList" :key="i" :data="item" :isdetail="false"></auction-card>
				</div>
			</div>
			<div style="width: 1200px;margin: 0 auto;">
				<div class="biaodi_three" v-if="selectIndex == 2 || selectIndex == 3">
					<target-card v-for="item,i in biaodiList" :data="item" :key="i" class="biaodi_item"></target-card>
				</div>
				<div class="auctionfirm_two" v-if="selectIndex == 1">
					<div :class="i%2==0?'auctionfirm_two_div auctionfirm_two_divs':'auctionfirm_two_div'"
						v-for="(item,i) in qiyePaimaihuiList" @click="goInfo(item)">
						<div style="width: 180px;height: 180px;background-color: #F6F6F6; overflow: hidden;"
							class="auctionfirm_two_divsss">
							<img style="max-height: 3980px;" :src="url +  item.pmh_pic" @error="(e)=>e.target.src = item.pmh_pic ? 'https://oss.yunpaiwang.com/'+item.pmh_pic : ''">
						</div>
						<div class="auctionfirm_two_div_rig">
							<div class="auctionfirm_two_div_rig_o">{{item.pmh_name}} </div>
							<div class="auctionfirm_two_div_rig_t">
								受委托，我公司定于{{item.start_time_name}}在灰谷网公开举行竞价会，物资内容以委托方所指定现场实物为准。
							</div>
							<div class="auctionfirm_two_div_rig_f">
								<div>{{item.parentname}}</div>
								<div>{{item.start_time_name}}</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="auctionpage">
				<el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
					:current-page.sync="page" :page-size="per_page" layout="prev, pager, next, jumper" :total="total">
				</el-pagination>
			</div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import area from '../assets/js/area.js'
	import ajax from '../store/Ajax'
	import TargetCard from '../components/targetCard.vue'
	import AuctionCard from '../components/auctionCard.vue'
	export default {
		name: 'Auction',
		components: {
			Headers,
			FootersBottom,
			homeRight,
			TargetCard,
			AuctionCard
		},
		data() {
			return {
				selectIndex: 0,
				pmqiyeDetails: {},
				url: '',
				page: 1,
				qiyePaimaihuiList: [],
				biaodiList: [],
				gongaoList: [],
				total: 0,
				per_page: 0,
				userInfo: null,
				isSlect: 0,
				province: '',
				city: ''
			}
		},
		filters: {
			formatDate: function(value) {
				value = value * 1000
				let date = new Date(value);
				// console.log(date)
				let y = date.getFullYear();
				// console.log(y)
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return MM + '月' + d + '日 ' + h + ':' + m + ':' + s;
			}
		},
		created() {
			this.url = this.$Pc.ossUrl
			if (localStorage.getItem('userInfo')) {
				this.userInfo = JSON.parse(localStorage.getItem('userInfo'))
			}
			console.log(this.$route)
			// 拍卖详情
			ajax.pmqiyeDetail({
				id: this.$route.query.id * 1
			}).then(res => {
				this.pmqiyeDetails = res.data
				this.province = area.area.province_list[res.data.province]
				this.city = area.area.city_list[res.data.city]
			})
			if (this.userInfo) {
				// 企业是否收藏
				ajax.zyqyscIsClick({
					qiye_id: this.$route.query.id * 1,
					member_id: this.userInfo.id
				}).then(res => {
					this.isSlect = res.code
				})
			}
			this.getPmhList()
		},
		watch: {
			selectIndex() {
				this.page = 1
				if (this.selectIndex == 0) this.getPmhList()
				if (this.selectIndex == 1) this.getPmhList()
				if (this.selectIndex == 2) this.getBiaoList()
				if (this.selectIndex == 3) this.getBiaoList(5)
			},
			page() {
				if (this.selectIndex == 0) this.getPmhList()
				if (this.selectIndex == 1) this.getPmhList()
				if (this.selectIndex == 2) this.getBiaoList()
				if (this.selectIndex == 3) this.getBiaoList(5)
			}
		},
		methods: {
			goLogin() {
				this.$router.push({ //核心语句
					path: '/Login', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						isReject: false
					}
				})
			},
			isCollect(idx) {
				if (!this.userInfo) {
					this.$message.error('请先登录')
					this.goLogin()
				}
				ajax.scqiye({
					member_id: this.userInfo.id,
					qiye_id: this.$route.query.id * 1,
					isquxiao: idx
				}).then(res => {
					if (res.code == 1) {
						this.$message.success(res.data)
						if (res.data == '取消收藏成功') this.isSlect = 0
						else this.isSlect = 1
					}else{
						this.$message.error(res.msg)
						setTimeout(()=>{
							this.goLogin()
						},600)
					}
				})
			},
			goInfo(item) {
				let routeData = this.$router.resolve({ //核心语句
					path: '/noticeDetail', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: item.id,
					}
				})
				window.open(routeData.href, '_blank');
			},
			getPmhList() {
				ajax.pmhlist({
					parentid: this.$route.query.id * 1,
					page: this.page
				}).then(res => {
					this.qiyePaimaihuiList = res.data.data
					this.total = res.data.total
					this.per_page = res.data.per_page
				})
			},
			getBiaoList(idx) {
				let data = {
					parentid: this.$route.query.id * 1,
					page: this.page
				}
				if (idx) data.status = idx
				ajax.bdlist(data).then(res => {
					this.biaodiList = res.data.data
					this.total = res.data.total
					this.per_page = res.data.per_page
				})
			},
			gerGonggao() {
				let data = {
					parentid: this.$route.query.id * 1,
					page: this.page
				}
				ajax.pmhgglist(data).then(res => {
					this.gongaoList = res.data.data
					this.total = res.data.total
					this.per_page = res.data.per_page
				})
			},
			handleSizeChange(e) {
				this.page = e
			},
			handleCurrentChange(e) {
				this.page = e
			}
		}
	}
</script>

<style type="text/css" scoped="scoped">
	.auction {
		width: 1200px;
		min-height: 1000px;
		padding-bottom: 50px;
		box-sizing: border-box;
		margin: 0 auto;
	}

	.auctionFirmDetail_three {
		height: 66px;
		background: #FFFFFF;
		border-bottom: 2px solid #A80012;
	}

	.auctionFirmDetail_threec {
		width: 1200px;
		margin: 0 auto;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auctionFirmDetail_threec>div {
		width: 212px;
		height: 66px;
		text-align: center;
		line-height: 66px;
		font-size: 18px;
		font-weight: 400;
		color: #333333;
		cursor: pointer;
	}

	.auctionFirmDetail_threec>div span {
		color: #A80012;
	}

	.auctionFirmDetail_threec .auctionFirmDetail_threecs {
		width: 212px;
		height: 66px;
		background: #A80012;
		font-size: 18px;
		font-weight: 400;
		color: #FFFFFF;
	}

	.auctionFirmDetail_threec .auctionFirmDetail_threecs span {
		color: #FFFFFF;
	}

	.auctionFirmDetail_threecss:hover {
		width: 212px;
		height: 66px;
		background: #A80012 !important;
		font-size: 18px !important;
		font-weight: 400 !important;
		color: #FFFFFF !important;
	}

	.auctionFirmDetail_threecss:hover span {
		color: #FFFFFF !important;
	}

	.auctionFirmDetail_one {
		max-width: 100%;
		min-width: 1200px;
		background-image: url(https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/about/qiyebanner.jpg);
		background-size: 100% 100%;
		height: 300px;
	}

	.auctionFirmDetail_two {
		min-height: 252px;
		background-color: #FFFFFF;
	}

	.auctionFirmDetail_two_view {
		width: 1200px;
		margin: 0 auto;
		min-height: 252px;
		display: flex;
		-js-display: flex;

	}

	.auctionFirmDetail_two_view_left {
		padding-top: 54px;
		box-sizing: border-box;
	}

	.auctionFirmDetail_two_view_left_one {
		display: flex;
		-js-display: flex;
	}

	.auctionFirmDetail_two_view_left_ones {
		width: 80px;
		height: 80px;
		background: #FFFFFF;
		border: 1px solid #EEEEEE;
		border-radius: 4px;
		background-color: red;
	}

	.auctionFirmDetail_two_view_left_oness {
		height: 80px;
		font-size: 14px;
		color: #999;
		margin-left: 13px;
	}

	.auctionFirmDetail_two_view_left_oness>div:nth-child(1) {
		font-size: 18px;
		font-weight: bold;
		color: #333333;
		margin-bottom: 18px;
	}

	.auctionFirmDetail_two_view_left_two {
		width: 120px;
		height: 32px;
		background: #A80012;
		border-radius: 2px;
		text-align: center;
		line-height: 32px;
		font-size: 14px;
		font-weight: 400;
		color: #FFFFFF;
		margin-left: 105px;
		margin-top: 36px;
		cursor: pointer;
	}

	.auctionFirmDetail_two_view_right {
		margin-left: 149px;
		width: 650px;
		min-height: 156px;
		font-size: 16px;
		font-weight: 400;
		color: #333333;
		line-height: 28px;
		padding-top: 54px;
		box-sizing: border-box;
	}

	.auction_title {
		height: 252px;
		background: #FFFFFF;
		border: 1px solid #EEEEEE;
		margin-top: 20px;
	}


	.auction_three {
		margin-top: 30px;
	}
	
	.auctionpage {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
	}

	/deep/.el-pagination.is-background .el-pager li:not(.disabled).active {
		background-color: #A80012;
		color: white;
	}

	.biaodi_three {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 20px;
	}
	.biaodi_item{
		margin-right: 20px;
	}
	.biaodi_item:nth-child(4n){
		margin-right: 0;
	}

	/* 第二样式 */
	.auctionfirm_two {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 32px;
	}

	.auctionfirm_two_div {
		width: 592px;
		height: 220px;
		background: #FFFFFF;
		margin-bottom: 20px;
		padding: 20px;
		box-sizing: border-box;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auctionfirm_two_divsss {
		display: flex;
		-js-display: flex;
		align-items: center;
	}
	.auctionfirm_two_divsss>img {
		width: 180px;
		height: auto;
	}

	.auctionfirm_two_divs {
		margin-right: 16px;
	}

	.auctionfirm_two_div img {
		width: 180px;
		min-height: auto;
		max-height: 180px;
	}

	.auctionfirm_two_div_rig {
		width: 350px;
		height: 180px;
		margin-left: 20px;
		position: relative;
	}

	.auctionfirm_two_div_rig_o {
		font-size: 18px;
		font-weight: bold;
		color: #333333;
	}

	.auctionfirm_two_div_rig_t {
		font-size: 14px;
		color: #777;
		margin-top: 20px;
		width: 350px;
		display: -webkit-box;
		-js-display: -webkit-box;
		overflow: hidden;
		white-space: normal !important;
		text-overflow: ellipsis;
		word-wrap: break-word;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
	}

	.auctionfirm_two_div_rig_f {
		width: 350px;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 14px;
		font-weight: 400;
		color: #777777;
		position: absolute;
		left: 0;
		bottom: 5px;
	}
</style>
