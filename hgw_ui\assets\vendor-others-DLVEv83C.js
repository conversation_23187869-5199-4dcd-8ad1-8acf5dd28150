import{z as e,i as t,aw as r,u as n,r as o,af as i,t as a,M as s,_ as f,R as u,g as c}from"./vue-vendor-D6tHD5lA.js";function l(e,t={},r){for(const n in e){const o=e[n],i=r?`${r}:${n}`:n;"object"==typeof o&&null!==o?l(o,t,i):"function"==typeof o&&(t[i]=o)}return t}const p={run:e=>e()},d=void 0!==console.createTask?console.createTask:()=>p;function h(e,t){const r=t.shift(),n=d(r);return e.reduce((e,r)=>e.then(()=>n.run(()=>r(...t))),Promise.resolve())}function g(e,t){const r=t.shift(),n=d(r);return Promise.all(e.map(e=>n.run(()=>e(...t))))}function m(e,t){for(const r of[...e])r(t)}class y{constructor(){this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}hook(e,t,r={}){if(!e||"function"!=typeof t)return()=>{};const n=e;let o;for(;this._deprecatedHooks[e];)o=this._deprecatedHooks[e],e=o.to;if(o&&!r.allowDeprecated){let e=o.message;e||(e=`${n} hook has been deprecated`+(o.to?`, please use ${o.to}`:"")),this._deprecatedMessages||(this._deprecatedMessages=new Set),this._deprecatedMessages.has(e)||this._deprecatedMessages.add(e)}if(!t.name)try{Object.defineProperty(t,"name",{get:()=>"_"+e.replace(/\W+/g,"_")+"_hook_cb",configurable:!0})}catch{}return this._hooks[e]=this._hooks[e]||[],this._hooks[e].push(t),()=>{t&&(this.removeHook(e,t),t=void 0)}}hookOnce(e,t){let r,n=(...e)=>("function"==typeof r&&r(),r=void 0,n=void 0,t(...e));return r=this.hook(e,n),r}removeHook(e,t){if(this._hooks[e]){const r=this._hooks[e].indexOf(t);-1!==r&&this._hooks[e].splice(r,1),0===this._hooks[e].length&&delete this._hooks[e]}}deprecateHook(e,t){this._deprecatedHooks[e]="string"==typeof t?{to:t}:t;const r=this._hooks[e]||[];delete this._hooks[e];for(const n of r)this.hook(e,n)}deprecateHooks(e){Object.assign(this._deprecatedHooks,e);for(const t in e)this.deprecateHook(t,e[t])}addHooks(e){const t=l(e),r=Object.keys(t).map(e=>this.hook(e,t[e]));return()=>{for(const e of r.splice(0,r.length))e()}}removeHooks(e){const t=l(e);for(const r in t)this.removeHook(r,t[r])}removeAllHooks(){for(const e in this._hooks)delete this._hooks[e]}callHook(e,...t){return t.unshift(e),this.callHookWith(h,e,...t)}callHookParallel(e,...t){return t.unshift(e),this.callHookWith(g,e,...t)}callHookWith(e,t,...r){const n=this._before||this._after?{name:t,args:r,context:{}}:void 0;this._before&&m(this._before,n);const o=e(t in this._hooks?[...this._hooks[t]]:[],r);return o instanceof Promise?o.finally(()=>{this._after&&n&&m(this._after,n)}):(this._after&&n&&m(this._after,n),o)}beforeEach(e){return this._before=this._before||[],this._before.push(e),()=>{if(void 0!==this._before){const t=this._before.indexOf(e);-1!==t&&this._before.splice(t,1)}}}afterEach(e){return this._after=this._after||[],this._after.push(e),()=>{if(void 0!==this._after){const t=this._after.indexOf(e);-1!==t&&this._after.splice(t,1)}}}}const v=new Set(["title","titleTemplate","script","style","noscript"]),b=new Set(["base","meta","link","style","script","noscript"]),w=new Set(["title","titleTemplate","templateParams","base","htmlAttrs","bodyAttrs","meta","link","style","script","noscript"]),x=new Set(["base","title","titleTemplate","bodyAttrs","htmlAttrs","templateParams"]),k=new Set(["tagPosition","tagPriority","tagDuplicateStrategy","children","innerHTML","textContent","processTemplateParams"]),O="undefined"!=typeof window;function _(e){let t=9;for(let r=0;r<e.length;)t=Math.imul(t^e.charCodeAt(r++),9**9);return(65536+(t^t>>>9)).toString(16).substring(1,8).toLowerCase()}function A(e){if(e._h)return e._h;if(e._d)return _(e._d);let t=`${e.tag}:${e.textContent||e.innerHTML||""}:`;for(const r in e.props)t+=`${r}:${String(e.props[r])},`;return _(t)}function M(e,t,r,n){const o=n||P("object"!=typeof t||"function"==typeof t||t instanceof Promise?{["script"===e||"noscript"===e||"style"===e?"innerHTML":"textContent"]:t}:{...t},"templateParams"===e||"titleTemplate"===e);if(o instanceof Promise)return o.then(n=>M(e,t,r,n));const i={tag:e,props:o};for(const a of k){const e=void 0!==i.props[a]?i.props[a]:r[a];void 0!==e&&(("innerHTML"!==a&&"textContent"!==a&&"children"!==a||v.has(i.tag))&&(i["children"===a?"innerHTML":a]=e),delete i.props[a])}return i.props.body&&(i.tagPosition="bodyClose",delete i.props.body),"script"===i.tag&&"object"==typeof i.innerHTML&&(i.innerHTML=JSON.stringify(i.innerHTML),i.props.type=i.props.type||"application/json"),Array.isArray(i.props.content)?i.props.content.map(e=>({...i,props:{...i.props,content:e}})):i}function H(e,t){var r;const n="class"===e?" ":";";return t&&"object"==typeof t&&!Array.isArray(t)&&(t=Object.entries(t).filter(([,e])=>e).map(([t,r])=>"style"===e?`${t}:${r}`:t)),null==(r=String(Array.isArray(t)?t.join(n):t))?void 0:r.split(n).filter(e=>Boolean(e.trim())).join(n)}function j(e,t,r,n){for(let o=n;o<r.length;o+=1){const n=r[o];if("class"!==n&&"style"!==n){if(e[n]instanceof Promise)return e[n].then(i=>(e[n]=i,j(e,t,r,o)));if(!t&&!k.has(n)){const t=String(e[n]),r=n.startsWith("data-");"true"===t||""===t?e[n]=!r||"true":e[n]||(r&&"false"===t?e[n]="false":delete e[n])}}else e[n]=H(n,e[n])}}function P(e,t=!1){const r=j(e,t,Object.keys(e),0);return r instanceof Promise?r.then(()=>e):e}function q(e,t,r){for(let n=r;n<t.length;n+=1){const r=t[n];if(r instanceof Promise)return r.then(r=>(t[n]=r,q(e,t,n)));Array.isArray(r)?e.push(...r):e.push(r)}}function E(e){const t=[],r=e.resolvedInput;for(const a in r){if(!Object.prototype.hasOwnProperty.call(r,a))continue;const n=r[a];if(void 0!==n&&w.has(a))if(Array.isArray(n))for(const r of n)t.push(M(a,r,e));else t.push(M(a,n,e))}if(0===t.length)return[];const n=[];return o=q(n,t,0),i=()=>n.map((t,r)=>(t._e=e._i,e.mode&&(t._m=e.mode),t._p=(e._i<<10)+r,t)),o instanceof Promise?o.then(i):i(o);var o,i}const S=new Set(["onload","onerror","onabort","onprogress","onloadstart"]),F={base:-10,title:10},T={critical:-80,high:-10,low:20};function R(e){const t=e.tagPriority;if("number"==typeof t)return t;let r=100;return"meta"===e.tag?"content-security-policy"===e.props["http-equiv"]?r=-30:e.props.charset?r=-20:"viewport"===e.props.name&&(r=-15):"link"===e.tag&&"preconnect"===e.props.rel?r=20:e.tag in F&&(r=F[e.tag]),t&&t in T?r+T[t]:r}const $=[{prefix:"before:",offset:-1},{prefix:"after:",offset:1}],C=["name","property","http-equiv"];function L(e){const{props:t,tag:r}=e;if(x.has(r))return r;if("link"===r&&"canonical"===t.rel)return"canonical";if(t.charset)return"charset";if(t.id)return`${r}:id:${t.id}`;for(const n of C)if(void 0!==t[n])return`${r}:${n}:${t[n]}`;return!1}const D="%separator";const W=new RegExp(`${D}(?:\\s*${D})*`,"g");function N(e,t,r,n=!1){if("string"!=typeof e||!e.includes("%"))return e;let o=e;try{o=decodeURI(e)}catch{}const i=o.match(/%\w+(?:\.\w+)?/g);if(!i)return e;const a=e.includes(D);return e=e.replace(/%\w+(?:\.\w+)?/g,e=>{if(e===D||!i.includes(e))return e;const r=function(e,t,r=!1){var n;let o;if("s"===t||"pageTitle"===t)o=e.pageTitle;else if(t.includes(".")){const r=t.indexOf(".");o=null==(n=e[t.substring(0,r)])?void 0:n[t.substring(r+1)]}else o=e[t];if(void 0!==o)return r?(o||"").replace(/"/g,'\\"'):o||""}(t,e.slice(1),n);return void 0!==r?r:e}).trim(),a&&(e.endsWith(D)&&(e=e.slice(0,-10)),e.startsWith(D)&&(e=e.slice(10)),e=e.replace(W,r).trim()),e}function B(e,t){return null==e?t||null:"function"==typeof e?e(t):e}function V(e,t={}){const r=t.delayFn||(e=>setTimeout(e,10));return e._domDebouncedUpdatePromise=e._domDebouncedUpdatePromise||new Promise(n=>r(()=>async function(e,t={}){const r=t.document||e.resolvedOptions.document;if(!r||!e.dirty)return;const n={shouldRender:!0,tags:[]};return await e.hooks.callHook("dom:beforeRender",n),n.shouldRender?(e._domUpdatePromise||(e._domUpdatePromise=new Promise(async t=>{var n;const o=(await e.resolveTags()).map(e=>({tag:e,id:b.has(e.tag)?A(e):e.tag,shouldRender:!0}));let i=e._dom;if(!i){i={elMap:{htmlAttrs:r.documentElement,bodyAttrs:r.body}};const e=new Set;for(const t of["body","head"]){const o=null==(n=r[t])?void 0:n.children;for(const t of o){const r=t.tagName.toLowerCase();if(!b.has(r))continue;const n={tag:r,props:await P(t.getAttributeNames().reduce((e,r)=>({...e,[r]:t.getAttribute(r)}),{})),innerHTML:t.innerHTML},o=L(n);let a=o,s=1;for(;a&&e.has(a);)a=`${o}:${s++}`;a&&(n._d=a,e.add(a)),i.elMap[t.getAttribute("data-hid")||A(n)]=t}}}function a(e,t,r){const n=`${e}:${t}`;i.sideEffects[n]=r,delete i.pendingSideEffects[n]}function s({id:e,$el:t,tag:n}){const o=n.tag.endsWith("Attrs");if(i.elMap[e]=t,o||(n.textContent&&n.textContent!==t.textContent&&(t.textContent=n.textContent),n.innerHTML&&n.innerHTML!==t.innerHTML&&(t.innerHTML=n.innerHTML),a(e,"el",()=>{var t;null==(t=i.elMap[e])||t.remove(),delete i.elMap[e]})),n._eventHandlers)for(const i in n._eventHandlers)Object.prototype.hasOwnProperty.call(n._eventHandlers,i)&&""!==t.getAttribute(`data-${i}`)&&(("bodyAttrs"===n.tag?r.defaultView:t).addEventListener(i.substring(2),n._eventHandlers[i].bind(t)),t.setAttribute(`data-${i}`,""));for(const r in n.props){if(!Object.prototype.hasOwnProperty.call(n.props,r))continue;const i=n.props[r],s=`attr:${r}`;if("class"===r){if(!i)continue;for(const r of i.split(" "))o&&a(e,`${s}:${r}`,()=>t.classList.remove(r)),!t.classList.contains(r)&&t.classList.add(r)}else if("style"===r){if(!i)continue;for(const r of i.split(";")){const n=r.indexOf(":"),o=r.substring(0,n).trim(),i=r.substring(n+1).trim();a(e,`${s}:${o}`,()=>{t.style.removeProperty(o)}),t.style.setProperty(o,i)}}else t.getAttribute(r)!==i&&t.setAttribute(r,!0===i?"":String(i)),o&&a(e,s,()=>t.removeAttribute(r))}}i.pendingSideEffects={...i.sideEffects},i.sideEffects={};const f=[],u={bodyClose:void 0,bodyOpen:void 0,head:void 0};for(const e of o){const{tag:t,shouldRender:n,id:o}=e;n&&("title"!==t.tag?(e.$el=e.$el||i.elMap[o],e.$el?s(e):b.has(t.tag)&&f.push(e)):r.title=t.textContent)}for(const e of f){const t=e.tag.tagPosition||"head";e.$el=r.createElement(e.tag.tag),s(e),u[t]=u[t]||r.createDocumentFragment(),u[t].appendChild(e.$el)}for(const c of o)await e.hooks.callHook("dom:renderTag",c,r,a);u.head&&r.head.appendChild(u.head),u.bodyOpen&&r.body.insertBefore(u.bodyOpen,r.body.firstChild),u.bodyClose&&r.body.appendChild(u.bodyClose);for(const e in i.pendingSideEffects)i.pendingSideEffects[e]();e._dom=i,await e.hooks.callHook("dom:rendered",{renders:o}),t()}).finally(()=>{e._domUpdatePromise=void 0,e.dirty=!1})),e._domUpdatePromise):void 0}(e,t).then(()=>{delete e._domDebouncedUpdatePromise,n()})))}function I(e){return t=>{var r,n;const o=(null==(n=null==(r=t.resolvedOptions.document)?void 0:r.head.querySelector('script[id="unhead:payload"]'))?void 0:n.innerHTML)||!1;return o&&t.push(JSON.parse(o)),{mode:"client",hooks:{"entries:updated":t=>{V(t,e)}}}}}const U=new Set(["templateParams","htmlAttrs","bodyAttrs"]),z={hooks:{"tag:normalise":({tag:e})=>{e.props.hid&&(e.key=e.props.hid,delete e.props.hid),e.props.vmid&&(e.key=e.props.vmid,delete e.props.vmid),e.props.key&&(e.key=e.props.key,delete e.props.key);const t=L(e);!t||t.startsWith("meta:og:")||t.startsWith("meta:twitter:")||delete e.key;const r=t||!!e.key&&`${e.tag}:${e.key}`;r&&(e._d=r)},"tags:resolve":e=>{const t=Object.create(null);for(const n of e.tags){const e=(n.key?`${n.tag}:${n.key}`:n._d)||A(n),r=t[e];if(r){let o=null==n?void 0:n.tagDuplicateStrategy;if(!o&&U.has(n.tag)&&(o="merge"),"merge"===o){const o=r.props;o.style&&n.props.style&&(";"!==o.style[o.style.length-1]&&(o.style+=";"),n.props.style=`${o.style} ${n.props.style}`),o.class&&n.props.class?n.props.class=`${o.class} ${n.props.class}`:o.class&&(n.props.class=o.class),t[e].props={...o,...n.props};continue}if(n._e===r._e){r._duped=r._duped||[],n._d=`${r._d}:${r._duped.length+1}`,r._duped.push(n);continue}if(R(n)>R(r))continue}n.innerHTML||n.textContent||0!==Object.keys(n.props).length||!b.has(n.tag)?t[e]=n:delete t[e]}const r=[];for(const n in t){const e=t[n],o=e._duped;r.push(e),o&&(delete e._duped,r.push(...o))}e.tags=r,e.tags=e.tags.filter(e=>!("meta"===e.tag&&(e.props.name||e.props.property)&&!e.props.content))}}},J=new Set(["script","link","bodyAttrs"]),Y=e=>({hooks:{"tags:resolve":t=>{for(const r of t.tags){if(!J.has(r.tag))continue;const t=r.props;for(const n in t){if("o"!==n[0]||"n"!==n[1])continue;if(!Object.prototype.hasOwnProperty.call(t,n))continue;const o=t[n];"function"==typeof o&&(e.ssr&&S.has(n)?t[n]=`this.dataset.${n}fired = true`:delete t[n],r._eventHandlers=r._eventHandlers||{},r._eventHandlers[n]=o)}e.ssr&&r._eventHandlers&&(r.props.src||r.props.href)&&(r.key=r.key||_(r.props.src||r.props.href))}},"dom:renderTag":({$el:e,tag:t})=>{var r,n;const o=null==e?void 0:e.dataset;if(o)for(const i in o){if(!i.endsWith("fired"))continue;const o=i.slice(0,-5);S.has(o)&&(null==(n=null==(r=t._eventHandlers)?void 0:r[o])||n.call(e,new Event(o.substring(2))))}}}}),Z=new Set(["link","style","script","noscript"]),X={hooks:{"tag:normalise":({tag:e})=>{e.key&&Z.has(e.tag)&&(e.props["data-hid"]=e._h=_(e.key))}}},G={mode:"server",hooks:{"tags:beforeResolve":e=>{const t={};let r=!1;for(const n of e.tags)"server"!==n._m||"titleTemplate"!==n.tag&&"templateParams"!==n.tag&&"title"!==n.tag||(t[n.tag]="title"===n.tag||"titleTemplate"===n.tag?n.textContent:n.props,r=!0);r&&e.tags.push({tag:"script",innerHTML:JSON.stringify(t),props:{id:"unhead:payload",type:"application/json"}})}}},K={hooks:{"tags:resolve":e=>{var t;for(const r of e.tags)if("string"==typeof r.tagPriority)for(const{prefix:n,offset:o}of $){if(!r.tagPriority.startsWith(n))continue;const i=r.tagPriority.substring(n.length),a=null==(t=e.tags.find(e=>e._d===i))?void 0:t._p;if(void 0!==a){r._p=a+o;break}}e.tags.sort((e,t)=>{const r=R(e),n=R(t);return r<n?-1:r>n?1:e._p-t._p})}}},Q={meta:"content",link:"href",htmlAttrs:"lang"},ee=["innerHTML","textContent"],te=e=>({hooks:{"tags:resolve":t=>{var r;const{tags:n}=t;let o;for(let e=0;e<n.length;e+=1){"templateParams"===n[e].tag&&(o=t.tags.splice(e,1)[0].props,e-=1)}const i=o||{},a=i.separator||"|";delete i.separator,i.pageTitle=N(i.pageTitle||(null==(r=n.find(e=>"title"===e.tag))?void 0:r.textContent)||"",i,a);for(const e of n){if(!1===e.processTemplateParams)continue;const t=Q[e.tag];if(t&&"string"==typeof e.props[t])e.props[t]=N(e.props[t],i,a);else if(e.processTemplateParams||"titleTemplate"===e.tag||"title"===e.tag)for(const r of ee)"string"==typeof e[r]&&(e[r]=N(e[r],i,a,"script"===e.tag&&e.props.type.endsWith("json")))}e._templateParams=i,e._separator=a},"tags:afterResolve":({tags:t})=>{let r;for(let e=0;e<t.length;e+=1){const n=t[e];"title"===n.tag&&!1!==n.processTemplateParams&&(r=n)}(null==r?void 0:r.textContent)&&(r.textContent=N(r.textContent,e._templateParams,e._separator))}}}),re={hooks:{"tags:resolve":e=>{const{tags:t}=e;let r,n;for(let o=0;o<t.length;o+=1){const e=t[o];"title"===e.tag?r=e:"titleTemplate"===e.tag&&(n=e)}if(n&&r){const t=B(n.textContent,r.textContent);null!==t?r.textContent=t||r.textContent:e.tags.splice(e.tags.indexOf(r),1)}else if(n){const e=B(n.textContent);null!==e&&(n.textContent=e,n.tag="title",n=void 0)}n&&e.tags.splice(e.tags.indexOf(n),1)}}},ne={hooks:{"tags:afterResolve":e=>{for(const t of e.tags)"string"==typeof t.innerHTML&&(!t.innerHTML||"application/ld+json"!==t.props.type&&"application/json"!==t.props.type?t.innerHTML=t.innerHTML.replace(new RegExp(`</${t.tag}`,"g"),`<\\/${t.tag}`):t.innerHTML=t.innerHTML.replace(/</g,"\\u003C"))}}};let oe;function ie(e={}){const t=function(e={}){const t=new y;t.addHooks(e.hooks||{}),e.document=e.document||(O?document:void 0);const r=!e.document,n=()=>{s.dirty=!0,t.callHook("entries:updated",s)};let o=0,i=[];const a=[],s={plugins:a,dirty:!1,resolvedOptions:e,hooks:t,headEntries:()=>i,use(e){const n="function"==typeof e?e(s):e;n.key&&a.some(e=>e.key===n.key)||(a.push(n),ae(n.mode,r)&&t.addHooks(n.hooks||{}))},push(e,t){null==t||delete t.head;const a={_i:o++,input:e,...t};return ae(a.mode,r)&&(i.push(a),n()),{dispose(){i=i.filter(e=>e._i!==a._i),n()},patch(e){for(const t of i)t._i===a._i&&(t.input=a.input=e);n()}}},async resolveTags(){const e={tags:[],entries:[...i]};await t.callHook("entries:resolve",e);for(const r of e.entries){const n=r.resolvedInput||r.input;if(r.resolvedInput=await(r.transform?r.transform(n):n),r.resolvedInput)for(const o of await E(r)){const n={tag:o,entry:r,resolvedOptions:s.resolvedOptions};await t.callHook("tag:normalise",n),e.tags.push(n.tag)}}return await t.callHook("tags:beforeResolve",e),await t.callHook("tags:resolve",e),await t.callHook("tags:afterResolve",e),e.tags},ssr:r};return[z,G,Y,X,K,te,re,ne,...(null==e?void 0:e.plugins)||[]].forEach(e=>s.use(e)),s.hooks.callHook("init",s),s}(e);return t.use(I()),oe=t}function ae(e,t){return!e||"server"===e&&t||"client"===e&&!t}const se="3"===r[0];function fe(e){if(e instanceof Promise||e instanceof Date||e instanceof RegExp)return e;const t="function"==typeof(r=e)?r():n(r);var r;if(!e||!t)return t;if(Array.isArray(t))return t.map(e=>fe(e));if("object"==typeof t){const e={};for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&("titleTemplate"===r||"o"===r[0]&&"n"===r[1]?e[r]=n(t[r]):e[r]=fe(t[r]));return e}return t}const ue={hooks:{"entries:resolve":e=>{for(const t of e.entries)t.resolvedInput=fe(t.input)}}},ce="usehead";function le(t={}){t.domDelayFn=t.domDelayFn||(t=>e(()=>setTimeout(()=>t(),0)));const r=ie(t);return r.use(ue),r.install=function(e){return{install(t){se&&(t.config.globalProperties.$unhead=e,t.config.globalProperties.$head=e,t.provide(ce,e))}}.install}(r),r}const pe="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},de="__unhead_injection_handler__";function he(){if(de in pe)return pe[de]();return t(ce)||oe}function ge(e,t={}){const r=t.head||he();if(r)return r.ssr?r.push(e,t):function(e,t,r={}){const n=o(!1),l=o({});i(()=>{l.value=n.value?{}:fe(t)});const p=e.push(l.value,r);a(l,e=>{p.patch(e)});c()&&(s(()=>{p.dispose()}),f(()=>{n.value=!0}),u(()=>{n.value=!1}));return p}(r,e,t)}function me(e){const t=e;return t.headTags=e.resolveTags,t.addEntry=e.push,t.addHeadObjs=e.push,t.addReactiveEntry=(e,t)=>{const r=ge(e,t);return void 0!==r?r.dispose:()=>{}},t.removeHeadObjs=()=>{},t.updateDOM=()=>{e.hooks.callHook("entries:updated",e)},t.unhead=e,t}var ye="top",ve="bottom",be="right",we="left",xe="auto",ke=[ye,ve,be,we],Oe="start",_e="end",Ae="viewport",Me="popper",He=ke.reduce(function(e,t){return e.concat([t+"-"+Oe,t+"-"+_e])},[]),je=[].concat(ke,[xe]).reduce(function(e,t){return e.concat([t,t+"-"+Oe,t+"-"+_e])},[]),Pe=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function qe(e){return e?(e.nodeName||"").toLowerCase():null}function Ee(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Se(e){return e instanceof Ee(e).Element||e instanceof Element}function Fe(e){return e instanceof Ee(e).HTMLElement||e instanceof HTMLElement}function Te(e){return"undefined"!=typeof ShadowRoot&&(e instanceof Ee(e).ShadowRoot||e instanceof ShadowRoot)}var Re={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var r=t.styles[e]||{},n=t.attributes[e]||{},o=t.elements[e];!Fe(o)||!qe(o)||(Object.assign(o.style,r),Object.keys(n).forEach(function(e){var t=n[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,r={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,r.popper),t.styles=r,t.elements.arrow&&Object.assign(t.elements.arrow.style,r.arrow),function(){Object.keys(t.elements).forEach(function(e){var n=t.elements[e],o=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:r[e]).reduce(function(e,t){return e[t]="",e},{});!Fe(n)||!qe(n)||(Object.assign(n.style,i),Object.keys(o).forEach(function(e){n.removeAttribute(e)}))})}},requires:["computeStyles"]};function $e(e){return e.split("-")[0]}var Ce=Math.max,Le=Math.min,De=Math.round;function We(e,t){void 0===t&&(t=!1);var r=e.getBoundingClientRect(),n=1,o=1;if(Fe(e)&&t){var i=e.offsetHeight,a=e.offsetWidth;a>0&&(n=De(r.width)/a||1),i>0&&(o=De(r.height)/i||1)}return{width:r.width/n,height:r.height/o,top:r.top/o,right:r.right/n,bottom:r.bottom/o,left:r.left/n,x:r.left/n,y:r.top/o}}function Ne(e){var t=We(e),r=e.offsetWidth,n=e.offsetHeight;return Math.abs(t.width-r)<=1&&(r=t.width),Math.abs(t.height-n)<=1&&(n=t.height),{x:e.offsetLeft,y:e.offsetTop,width:r,height:n}}function Be(e,t){var r=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(r&&Te(r)){var n=t;do{if(n&&e.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function Ve(e){return Ee(e).getComputedStyle(e)}function Ie(e){return["table","td","th"].indexOf(qe(e))>=0}function Ue(e){return((Se(e)?e.ownerDocument:e.document)||window.document).documentElement}function ze(e){return"html"===qe(e)?e:e.assignedSlot||e.parentNode||(Te(e)?e.host:null)||Ue(e)}function Je(e){return Fe(e)&&"fixed"!==Ve(e).position?e.offsetParent:null}function Ye(e){for(var t=Ee(e),r=Je(e);r&&Ie(r)&&"static"===Ve(r).position;)r=Je(r);return r&&("html"===qe(r)||"body"===qe(r)&&"static"===Ve(r).position)?t:r||function(e){var t=-1!==navigator.userAgent.toLowerCase().indexOf("firefox");if(-1!==navigator.userAgent.indexOf("Trident")&&Fe(e)&&"fixed"===Ve(e).position)return null;var r=ze(e);for(Te(r)&&(r=r.host);Fe(r)&&["html","body"].indexOf(qe(r))<0;){var n=Ve(r);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||t&&"filter"===n.willChange||t&&n.filter&&"none"!==n.filter)return r;r=r.parentNode}return null}(e)||t}function Ze(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function Xe(e,t,r){return Ce(e,Le(t,r))}function Ge(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Ke(e,t){return t.reduce(function(t,r){return t[r]=e,t},{})}var Qe={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,r=e.state,n=e.name,o=e.options,i=r.elements.arrow,a=r.modifiersData.popperOffsets,s=$e(r.placement),f=Ze(s),u=[we,be].indexOf(s)>=0?"height":"width";if(i&&a){var c=function(e,t){return Ge("number"!=typeof(e="function"==typeof e?e(Object.assign({},t.rects,{placement:t.placement})):e)?e:Ke(e,ke))}(o.padding,r),l=Ne(i),p="y"===f?ye:we,d="y"===f?ve:be,h=r.rects.reference[u]+r.rects.reference[f]-a[f]-r.rects.popper[u],g=a[f]-r.rects.reference[f],m=Ye(i),y=m?"y"===f?m.clientHeight||0:m.clientWidth||0:0,v=h/2-g/2,b=c[p],w=y-l[u]-c[d],x=y/2-l[u]/2+v,k=Xe(b,x,w),O=f;r.modifiersData[n]=((t={})[O]=k,t.centerOffset=k-x,t)}},effect:function(e){var t=e.state,r=e.options.element,n=void 0===r?"[data-popper-arrow]":r;null!=n&&("string"==typeof n&&!(n=t.elements.popper.querySelector(n))||!Be(t.elements.popper,n)||(t.elements.arrow=n))},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function et(e){return e.split("-")[1]}var tt={top:"auto",right:"auto",bottom:"auto",left:"auto"};function rt(e){var t,r=e.popper,n=e.popperRect,o=e.placement,i=e.variation,a=e.offsets,s=e.position,f=e.gpuAcceleration,u=e.adaptive,c=e.roundOffsets,l=e.isFixed,p=a.x,d=void 0===p?0:p,h=a.y,g=void 0===h?0:h,m="function"==typeof c?c({x:d,y:g}):{x:d,y:g};d=m.x,g=m.y;var y=a.hasOwnProperty("x"),v=a.hasOwnProperty("y"),b=we,w=ye,x=window;if(u){var k=Ye(r),O="clientHeight",_="clientWidth";if(k===Ee(r)&&("static"!==Ve(k=Ue(r)).position&&"absolute"===s&&(O="scrollHeight",_="scrollWidth")),o===ye||(o===we||o===be)&&i===_e)w=ve,g-=(l&&k===x&&x.visualViewport?x.visualViewport.height:k[O])-n.height,g*=f?1:-1;if(o===we||(o===ye||o===ve)&&i===_e)b=be,d-=(l&&k===x&&x.visualViewport?x.visualViewport.width:k[_])-n.width,d*=f?1:-1}var A,M=Object.assign({position:s},u&&tt),H=!0===c?function(e){var t=e.x,r=e.y,n=window.devicePixelRatio||1;return{x:De(t*n)/n||0,y:De(r*n)/n||0}}({x:d,y:g}):{x:d,y:g};return d=H.x,g=H.y,f?Object.assign({},M,((A={})[w]=v?"0":"",A[b]=y?"0":"",A.transform=(x.devicePixelRatio||1)<=1?"translate("+d+"px, "+g+"px)":"translate3d("+d+"px, "+g+"px, 0)",A)):Object.assign({},M,((t={})[w]=v?g+"px":"",t[b]=y?d+"px":"",t.transform="",t))}var nt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,r=e.options,n=r.gpuAcceleration,o=void 0===n||n,i=r.adaptive,a=void 0===i||i,s=r.roundOffsets,f=void 0===s||s,u={placement:$e(t.placement),variation:et(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:o,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,rt(Object.assign({},u,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:f})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,rt(Object.assign({},u,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:f})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},ot={passive:!0};var it={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,r=e.instance,n=e.options,o=n.scroll,i=void 0===o||o,a=n.resize,s=void 0===a||a,f=Ee(t.elements.popper),u=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&u.forEach(function(e){e.addEventListener("scroll",r.update,ot)}),s&&f.addEventListener("resize",r.update,ot),function(){i&&u.forEach(function(e){e.removeEventListener("scroll",r.update,ot)}),s&&f.removeEventListener("resize",r.update,ot)}},data:{}},at={left:"right",right:"left",bottom:"top",top:"bottom"};function st(e){return e.replace(/left|right|bottom|top/g,function(e){return at[e]})}var ft={start:"end",end:"start"};function ut(e){return e.replace(/start|end/g,function(e){return ft[e]})}function ct(e){var t=Ee(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function lt(e){return We(Ue(e)).left+ct(e).scrollLeft}function pt(e){var t=Ve(e),r=t.overflow,n=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(r+o+n)}function dt(e){return["html","body","#document"].indexOf(qe(e))>=0?e.ownerDocument.body:Fe(e)&&pt(e)?e:dt(ze(e))}function ht(e,t){var r;void 0===t&&(t=[]);var n=dt(e),o=n===(null==(r=e.ownerDocument)?void 0:r.body),i=Ee(n),a=o?[i].concat(i.visualViewport||[],pt(n)?n:[]):n,s=t.concat(a);return o?s:s.concat(ht(ze(a)))}function gt(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function mt(e,t){return t===Ae?gt(function(e){var t=Ee(e),r=Ue(e),n=t.visualViewport,o=r.clientWidth,i=r.clientHeight,a=0,s=0;return n&&(o=n.width,i=n.height,/^((?!chrome|android).)*safari/i.test(navigator.userAgent)||(a=n.offsetLeft,s=n.offsetTop)),{width:o,height:i,x:a+lt(e),y:s}}(e)):Se(t)?function(e){var t=We(e);return t.top=t.top+e.clientTop,t.left=t.left+e.clientLeft,t.bottom=t.top+e.clientHeight,t.right=t.left+e.clientWidth,t.width=e.clientWidth,t.height=e.clientHeight,t.x=t.left,t.y=t.top,t}(t):gt(function(e){var t,r=Ue(e),n=ct(e),o=null==(t=e.ownerDocument)?void 0:t.body,i=Ce(r.scrollWidth,r.clientWidth,o?o.scrollWidth:0,o?o.clientWidth:0),a=Ce(r.scrollHeight,r.clientHeight,o?o.scrollHeight:0,o?o.clientHeight:0),s=-n.scrollLeft+lt(e),f=-n.scrollTop;return"rtl"===Ve(o||r).direction&&(s+=Ce(r.clientWidth,o?o.clientWidth:0)-i),{width:i,height:a,x:s,y:f}}(Ue(e)))}function yt(e,t,r){var n="clippingParents"===t?function(e){var t=ht(ze(e)),r=["absolute","fixed"].indexOf(Ve(e).position)>=0&&Fe(e)?Ye(e):e;return Se(r)?t.filter(function(e){return Se(e)&&Be(e,r)&&"body"!==qe(e)}):[]}(e):[].concat(t),o=[].concat(n,[r]),i=o[0],a=o.reduce(function(t,r){var n=mt(e,r);return t.top=Ce(n.top,t.top),t.right=Le(n.right,t.right),t.bottom=Le(n.bottom,t.bottom),t.left=Ce(n.left,t.left),t},mt(e,i));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function vt(e){var t,r=e.reference,n=e.element,o=e.placement,i=o?$e(o):null,a=o?et(o):null,s=r.x+r.width/2-n.width/2,f=r.y+r.height/2-n.height/2;switch(i){case ye:t={x:s,y:r.y-n.height};break;case ve:t={x:s,y:r.y+r.height};break;case be:t={x:r.x+r.width,y:f};break;case we:t={x:r.x-n.width,y:f};break;default:t={x:r.x,y:r.y}}var u=i?Ze(i):null;if(null!=u){var c="y"===u?"height":"width";switch(a){case Oe:t[u]=t[u]-(r[c]/2-n[c]/2);break;case _e:t[u]=t[u]+(r[c]/2-n[c]/2)}}return t}function bt(e,t){void 0===t&&(t={});var r=t,n=r.placement,o=void 0===n?e.placement:n,i=r.boundary,a=void 0===i?"clippingParents":i,s=r.rootBoundary,f=void 0===s?Ae:s,u=r.elementContext,c=void 0===u?Me:u,l=r.altBoundary,p=void 0!==l&&l,d=r.padding,h=void 0===d?0:d,g=Ge("number"!=typeof h?h:Ke(h,ke)),m=c===Me?"reference":Me,y=e.rects.popper,v=e.elements[p?m:c],b=yt(Se(v)?v:v.contextElement||Ue(e.elements.popper),a,f),w=We(e.elements.reference),x=vt({reference:w,element:y,placement:o}),k=gt(Object.assign({},y,x)),O=c===Me?k:w,_={top:b.top-O.top+g.top,bottom:O.bottom-b.bottom+g.bottom,left:b.left-O.left+g.left,right:O.right-b.right+g.right},A=e.modifiersData.offset;if(c===Me&&A){var M=A[o];Object.keys(_).forEach(function(e){var t=[be,ve].indexOf(e)>=0?1:-1,r=[ye,ve].indexOf(e)>=0?"y":"x";_[e]+=M[r]*t})}return _}var wt={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,r=e.options,n=e.name;if(!t.modifiersData[n]._skip){for(var o=r.mainAxis,i=void 0===o||o,a=r.altAxis,s=void 0===a||a,f=r.fallbackPlacements,u=r.padding,c=r.boundary,l=r.rootBoundary,p=r.altBoundary,d=r.flipVariations,h=void 0===d||d,g=r.allowedAutoPlacements,m=t.options.placement,y=$e(m),v=f||(y===m||!h?[st(m)]:function(e){if($e(e)===xe)return[];var t=st(e);return[ut(e),t,ut(t)]}(m)),b=[m].concat(v).reduce(function(e,r){return e.concat($e(r)===xe?function(e,t){void 0===t&&(t={});var r=t,n=r.placement,o=r.boundary,i=r.rootBoundary,a=r.padding,s=r.flipVariations,f=r.allowedAutoPlacements,u=void 0===f?je:f,c=et(n),l=c?s?He:He.filter(function(e){return et(e)===c}):ke,p=l.filter(function(e){return u.indexOf(e)>=0});0===p.length&&(p=l);var d=p.reduce(function(t,r){return t[r]=bt(e,{placement:r,boundary:o,rootBoundary:i,padding:a})[$e(r)],t},{});return Object.keys(d).sort(function(e,t){return d[e]-d[t]})}(t,{placement:r,boundary:c,rootBoundary:l,padding:u,flipVariations:h,allowedAutoPlacements:g}):r)},[]),w=t.rects.reference,x=t.rects.popper,k=new Map,O=!0,_=b[0],A=0;A<b.length;A++){var M=b[A],H=$e(M),j=et(M)===Oe,P=[ye,ve].indexOf(H)>=0,q=P?"width":"height",E=bt(t,{placement:M,boundary:c,rootBoundary:l,altBoundary:p,padding:u}),S=P?j?be:we:j?ve:ye;w[q]>x[q]&&(S=st(S));var F=st(S),T=[];if(i&&T.push(E[H]<=0),s&&T.push(E[S]<=0,E[F]<=0),T.every(function(e){return e})){_=M,O=!1;break}k.set(M,T)}if(O)for(var R=function(e){var t=b.find(function(t){var r=k.get(t);if(r)return r.slice(0,e).every(function(e){return e})});if(t)return _=t,"break"},$=h?3:1;$>0;$--){if("break"===R($))break}t.placement!==_&&(t.modifiersData[n]._skip=!0,t.placement=_,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function xt(e,t,r){return void 0===r&&(r={x:0,y:0}),{top:e.top-t.height-r.y,right:e.right-t.width+r.x,bottom:e.bottom-t.height+r.y,left:e.left-t.width-r.x}}function kt(e){return[ye,be,ve,we].some(function(t){return e[t]>=0})}var Ot={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,r=e.name,n=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=bt(t,{elementContext:"reference"}),s=bt(t,{altBoundary:!0}),f=xt(a,n),u=xt(s,o,i),c=kt(f),l=kt(u);t.modifiersData[r]={referenceClippingOffsets:f,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:l},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":l})}};var _t={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,r=e.options,n=e.name,o=r.offset,i=void 0===o?[0,0]:o,a=je.reduce(function(e,r){return e[r]=function(e,t,r){var n=$e(e),o=[we,ye].indexOf(n)>=0?-1:1,i="function"==typeof r?r(Object.assign({},t,{placement:e})):r,a=i[0],s=i[1];return a=a||0,s=(s||0)*o,[we,be].indexOf(n)>=0?{x:s,y:a}:{x:a,y:s}}(r,t.rects,i),e},{}),s=a[t.placement],f=s.x,u=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=f,t.modifiersData.popperOffsets.y+=u),t.modifiersData[n]=a}};var At={name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,r=e.name;t.modifiersData[r]=vt({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}};var Mt={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,r=e.options,n=e.name,o=r.mainAxis,i=void 0===o||o,a=r.altAxis,s=void 0!==a&&a,f=r.boundary,u=r.rootBoundary,c=r.altBoundary,l=r.padding,p=r.tether,d=void 0===p||p,h=r.tetherOffset,g=void 0===h?0:h,m=bt(t,{boundary:f,rootBoundary:u,padding:l,altBoundary:c}),y=$e(t.placement),v=et(t.placement),b=!v,w=Ze(y),x=function(e){return"x"===e?"y":"x"}(w),k=t.modifiersData.popperOffsets,O=t.rects.reference,_=t.rects.popper,A="function"==typeof g?g(Object.assign({},t.rects,{placement:t.placement})):g,M="number"==typeof A?{mainAxis:A,altAxis:A}:Object.assign({mainAxis:0,altAxis:0},A),H=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,j={x:0,y:0};if(k){if(i){var P,q="y"===w?ye:we,E="y"===w?ve:be,S="y"===w?"height":"width",F=k[w],T=F+m[q],R=F-m[E],$=d?-_[S]/2:0,C=v===Oe?O[S]:_[S],L=v===Oe?-_[S]:-O[S],D=t.elements.arrow,W=d&&D?Ne(D):{width:0,height:0},N=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},B=N[q],V=N[E],I=Xe(0,O[S],W[S]),U=b?O[S]/2-$-I-B-M.mainAxis:C-I-B-M.mainAxis,z=b?-O[S]/2+$+I+V+M.mainAxis:L+I+V+M.mainAxis,J=t.elements.arrow&&Ye(t.elements.arrow),Y=J?"y"===w?J.clientTop||0:J.clientLeft||0:0,Z=null!=(P=null==H?void 0:H[w])?P:0,X=F+z-Z,G=Xe(d?Le(T,F+U-Z-Y):T,F,d?Ce(R,X):R);k[w]=G,j[w]=G-F}if(s){var K,Q="x"===w?ye:we,ee="x"===w?ve:be,te=k[x],re="y"===x?"height":"width",ne=te+m[Q],oe=te-m[ee],ie=-1!==[ye,we].indexOf(y),ae=null!=(K=null==H?void 0:H[x])?K:0,se=ie?ne:te-O[re]-_[re]-ae+M.altAxis,fe=ie?te+O[re]+_[re]-ae-M.altAxis:oe,ue=d&&ie?function(e,t,r){var n=Xe(e,t,r);return n>r?r:n}(se,te,fe):Xe(d?se:ne,te,d?fe:oe);k[x]=ue,j[x]=ue-te}t.modifiersData[n]=j}},requiresIfExists:["offset"]};function Ht(e,t,r){void 0===r&&(r=!1);var n=Fe(t),o=Fe(t)&&function(e){var t=e.getBoundingClientRect(),r=De(t.width)/e.offsetWidth||1,n=De(t.height)/e.offsetHeight||1;return 1!==r||1!==n}(t),i=Ue(t),a=We(e,o),s={scrollLeft:0,scrollTop:0},f={x:0,y:0};return(n||!n&&!r)&&(("body"!==qe(t)||pt(i))&&(s=function(e){return e!==Ee(e)&&Fe(e)?function(e){return{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}}(e):ct(e)}(t)),Fe(t)?((f=We(t,!0)).x+=t.clientLeft,f.y+=t.clientTop):i&&(f.x=lt(i))),{x:a.left+s.scrollLeft-f.x,y:a.top+s.scrollTop-f.y,width:a.width,height:a.height}}function jt(e){var t=new Map,r=new Set,n=[];function o(e){r.add(e.name),[].concat(e.requires||[],e.requiresIfExists||[]).forEach(function(e){if(!r.has(e)){var n=t.get(e);n&&o(n)}}),n.push(e)}return e.forEach(function(e){t.set(e.name,e)}),e.forEach(function(e){r.has(e.name)||o(e)}),n}function Pt(e){var t;return function(){return t||(t=new Promise(function(r){Promise.resolve().then(function(){t=void 0,r(e())})})),t}}var qt={placement:"bottom",modifiers:[],strategy:"absolute"};function Et(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}function St(e){void 0===e&&(e={});var t=e,r=t.defaultModifiers,n=void 0===r?[]:r,o=t.defaultOptions,i=void 0===o?qt:o;return function(e,t,r){void 0===r&&(r=i);var o={placement:"bottom",orderedModifiers:[],options:Object.assign({},qt,i),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},a=[],s=!1,f={state:o,setOptions:function(r){var s="function"==typeof r?r(o.options):r;u(),o.options=Object.assign({},i,o.options,s),o.scrollParents={reference:Se(e)?ht(e):e.contextElement?ht(e.contextElement):[],popper:ht(t)};var c=function(e){var t=jt(e);return Pe.reduce(function(e,r){return e.concat(t.filter(function(e){return e.phase===r}))},[])}(function(e){var t=e.reduce(function(e,t){var r=e[t.name];return e[t.name]=r?Object.assign({},r,t,{options:Object.assign({},r.options,t.options),data:Object.assign({},r.data,t.data)}):t,e},{});return Object.keys(t).map(function(e){return t[e]})}([].concat(n,o.options.modifiers)));return o.orderedModifiers=c.filter(function(e){return e.enabled}),o.orderedModifiers.forEach(function(e){var t=e.name,r=e.options,n=void 0===r?{}:r,i=e.effect;if("function"==typeof i){var s=i({state:o,name:t,instance:f,options:n}),u=function(){};a.push(s||u)}}),f.update()},forceUpdate:function(){if(!s){var e=o.elements,t=e.reference,r=e.popper;if(Et(t,r)){o.rects={reference:Ht(t,Ye(r),"fixed"===o.options.strategy),popper:Ne(r)},o.reset=!1,o.placement=o.options.placement,o.orderedModifiers.forEach(function(e){return o.modifiersData[e.name]=Object.assign({},e.data)});for(var n=0;n<o.orderedModifiers.length;n++)if(!0!==o.reset){var i=o.orderedModifiers[n],a=i.fn,u=i.options,c=void 0===u?{}:u,l=i.name;"function"==typeof a&&(o=a({state:o,options:c,name:l,instance:f})||o)}else o.reset=!1,n=-1}}},update:Pt(function(){return new Promise(function(e){f.forceUpdate(),e(o)})}),destroy:function(){u(),s=!0}};if(!Et(e,t))return f;function u(){a.forEach(function(e){return e()}),a=[]}return f.setOptions(r).then(function(e){!s&&r.onFirstUpdate&&r.onFirstUpdate(e)}),f}}St(),St({defaultModifiers:[it,At,nt,Re]});var Ft=St({defaultModifiers:[it,At,nt,Re,_t,wt,Mt,Qe,Ot]});function Tt(e,t){(function(e){return"string"==typeof e&&-1!==e.indexOf(".")&&1===parseFloat(e)})(e)&&(e="100%");var r=function(e){return"string"==typeof e&&-1!==e.indexOf("%")}(e);return e=360===t?e:Math.min(t,Math.max(0,parseFloat(e))),r&&(e=parseInt(String(e*t),10)/100),Math.abs(e-t)<1e-6?1:e=360===t?(e<0?e%t+t:e%t)/parseFloat(String(t)):e%t/parseFloat(String(t))}function Rt(e){return Math.min(1,Math.max(0,e))}function $t(e){return e=parseFloat(e),(isNaN(e)||e<0||e>1)&&(e=1),e}function Ct(e){return e<=1?"".concat(100*Number(e),"%"):e}function Lt(e){return 1===e.length?"0"+e:String(e)}function Dt(e,t,r){e=Tt(e,255),t=Tt(t,255),r=Tt(r,255);var n=Math.max(e,t,r),o=Math.min(e,t,r),i=0,a=0,s=(n+o)/2;if(n===o)a=0,i=0;else{var f=n-o;switch(a=s>.5?f/(2-n-o):f/(n+o),n){case e:i=(t-r)/f+(t<r?6:0);break;case t:i=(r-e)/f+2;break;case r:i=(e-t)/f+4}i/=6}return{h:i,s:a,l:s}}function Wt(e,t,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?e+6*r*(t-e):r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function Nt(e,t,r){e=Tt(e,255),t=Tt(t,255),r=Tt(r,255);var n=Math.max(e,t,r),o=Math.min(e,t,r),i=0,a=n,s=n-o,f=0===n?0:s/n;if(n===o)i=0;else{switch(n){case e:i=(t-r)/s+(t<r?6:0);break;case t:i=(r-e)/s+2;break;case r:i=(e-t)/s+4}i/=6}return{h:i,s:f,v:a}}function Bt(e,t,r,n){var o=[Lt(Math.round(e).toString(16)),Lt(Math.round(t).toString(16)),Lt(Math.round(r).toString(16))];return n&&o[0].startsWith(o[0].charAt(1))&&o[1].startsWith(o[1].charAt(1))&&o[2].startsWith(o[2].charAt(1))?o[0].charAt(0)+o[1].charAt(0)+o[2].charAt(0):o.join("")}function Vt(e){return It(e)/255}function It(e){return parseInt(e,16)}var Ut={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function zt(e){var t,r,n,o={r:0,g:0,b:0},i=1,a=null,s=null,f=null,u=!1,c=!1;return"string"==typeof e&&(e=function(e){if(e=e.trim().toLowerCase(),0===e.length)return!1;var t=!1;if(Ut[e])e=Ut[e],t=!0;else if("transparent"===e)return{r:0,g:0,b:0,a:0,format:"name"};var r=Xt.rgb.exec(e);if(r)return{r:r[1],g:r[2],b:r[3]};if(r=Xt.rgba.exec(e),r)return{r:r[1],g:r[2],b:r[3],a:r[4]};if(r=Xt.hsl.exec(e),r)return{h:r[1],s:r[2],l:r[3]};if(r=Xt.hsla.exec(e),r)return{h:r[1],s:r[2],l:r[3],a:r[4]};if(r=Xt.hsv.exec(e),r)return{h:r[1],s:r[2],v:r[3]};if(r=Xt.hsva.exec(e),r)return{h:r[1],s:r[2],v:r[3],a:r[4]};if(r=Xt.hex8.exec(e),r)return{r:It(r[1]),g:It(r[2]),b:It(r[3]),a:Vt(r[4]),format:t?"name":"hex8"};if(r=Xt.hex6.exec(e),r)return{r:It(r[1]),g:It(r[2]),b:It(r[3]),format:t?"name":"hex"};if(r=Xt.hex4.exec(e),r)return{r:It(r[1]+r[1]),g:It(r[2]+r[2]),b:It(r[3]+r[3]),a:Vt(r[4]+r[4]),format:t?"name":"hex8"};if(r=Xt.hex3.exec(e),r)return{r:It(r[1]+r[1]),g:It(r[2]+r[2]),b:It(r[3]+r[3]),format:t?"name":"hex"};return!1}(e)),"object"==typeof e&&(Gt(e.r)&&Gt(e.g)&&Gt(e.b)?(t=e.r,r=e.g,n=e.b,o={r:255*Tt(t,255),g:255*Tt(r,255),b:255*Tt(n,255)},u=!0,c="%"===String(e.r).substr(-1)?"prgb":"rgb"):Gt(e.h)&&Gt(e.s)&&Gt(e.v)?(a=Ct(e.s),s=Ct(e.v),o=function(e,t,r){e=6*Tt(e,360),t=Tt(t,100),r=Tt(r,100);var n=Math.floor(e),o=e-n,i=r*(1-t),a=r*(1-o*t),s=r*(1-(1-o)*t),f=n%6;return{r:255*[r,a,i,i,s,r][f],g:255*[s,r,r,a,i,i][f],b:255*[i,i,s,r,r,a][f]}}(e.h,a,s),u=!0,c="hsv"):Gt(e.h)&&Gt(e.s)&&Gt(e.l)&&(a=Ct(e.s),f=Ct(e.l),o=function(e,t,r){var n,o,i;if(e=Tt(e,360),t=Tt(t,100),r=Tt(r,100),0===t)o=r,i=r,n=r;else{var a=r<.5?r*(1+t):r+t-r*t,s=2*r-a;n=Wt(s,a,e+1/3),o=Wt(s,a,e),i=Wt(s,a,e-1/3)}return{r:255*n,g:255*o,b:255*i}}(e.h,a,f),u=!0,c="hsl"),Object.prototype.hasOwnProperty.call(e,"a")&&(i=e.a)),i=$t(i),{ok:u,format:e.format||c,r:Math.min(255,Math.max(o.r,0)),g:Math.min(255,Math.max(o.g,0)),b:Math.min(255,Math.max(o.b,0)),a:i}}var Jt="(?:".concat("[-\\+]?\\d*\\.\\d+%?",")|(?:").concat("[-\\+]?\\d+%?",")"),Yt="[\\s|\\(]+(".concat(Jt,")[,|\\s]+(").concat(Jt,")[,|\\s]+(").concat(Jt,")\\s*\\)?"),Zt="[\\s|\\(]+(".concat(Jt,")[,|\\s]+(").concat(Jt,")[,|\\s]+(").concat(Jt,")[,|\\s]+(").concat(Jt,")\\s*\\)?"),Xt={CSS_UNIT:new RegExp(Jt),rgb:new RegExp("rgb"+Yt),rgba:new RegExp("rgba"+Zt),hsl:new RegExp("hsl"+Yt),hsla:new RegExp("hsla"+Zt),hsv:new RegExp("hsv"+Yt),hsva:new RegExp("hsva"+Zt),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/};function Gt(e){return Boolean(Xt.CSS_UNIT.exec(String(e)))}var Kt=function(){function e(t,r){var n;if(void 0===t&&(t=""),void 0===r&&(r={}),t instanceof e)return t;"number"==typeof t&&(t=function(e){return{r:e>>16,g:(65280&e)>>8,b:255&e}}(t)),this.originalInput=t;var o=zt(t);this.originalInput=t,this.r=o.r,this.g=o.g,this.b=o.b,this.a=o.a,this.roundA=Math.round(100*this.a)/100,this.format=null!==(n=r.format)&&void 0!==n?n:o.format,this.gradientType=r.gradientType,this.r<1&&(this.r=Math.round(this.r)),this.g<1&&(this.g=Math.round(this.g)),this.b<1&&(this.b=Math.round(this.b)),this.isValid=o.ok}return e.prototype.isDark=function(){return this.getBrightness()<128},e.prototype.isLight=function(){return!this.isDark()},e.prototype.getBrightness=function(){var e=this.toRgb();return(299*e.r+587*e.g+114*e.b)/1e3},e.prototype.getLuminance=function(){var e=this.toRgb(),t=e.r/255,r=e.g/255,n=e.b/255;return.2126*(t<=.03928?t/12.92:Math.pow((t+.055)/1.055,2.4))+.7152*(r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4))+.0722*(n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4))},e.prototype.getAlpha=function(){return this.a},e.prototype.setAlpha=function(e){return this.a=$t(e),this.roundA=Math.round(100*this.a)/100,this},e.prototype.isMonochrome=function(){return 0===this.toHsl().s},e.prototype.toHsv=function(){var e=Nt(this.r,this.g,this.b);return{h:360*e.h,s:e.s,v:e.v,a:this.a}},e.prototype.toHsvString=function(){var e=Nt(this.r,this.g,this.b),t=Math.round(360*e.h),r=Math.round(100*e.s),n=Math.round(100*e.v);return 1===this.a?"hsv(".concat(t,", ").concat(r,"%, ").concat(n,"%)"):"hsva(".concat(t,", ").concat(r,"%, ").concat(n,"%, ").concat(this.roundA,")")},e.prototype.toHsl=function(){var e=Dt(this.r,this.g,this.b);return{h:360*e.h,s:e.s,l:e.l,a:this.a}},e.prototype.toHslString=function(){var e=Dt(this.r,this.g,this.b),t=Math.round(360*e.h),r=Math.round(100*e.s),n=Math.round(100*e.l);return 1===this.a?"hsl(".concat(t,", ").concat(r,"%, ").concat(n,"%)"):"hsla(".concat(t,", ").concat(r,"%, ").concat(n,"%, ").concat(this.roundA,")")},e.prototype.toHex=function(e){return void 0===e&&(e=!1),Bt(this.r,this.g,this.b,e)},e.prototype.toHexString=function(e){return void 0===e&&(e=!1),"#"+this.toHex(e)},e.prototype.toHex8=function(e){return void 0===e&&(e=!1),function(e,t,r,n,o){var i,a=[Lt(Math.round(e).toString(16)),Lt(Math.round(t).toString(16)),Lt(Math.round(r).toString(16)),Lt((i=n,Math.round(255*parseFloat(i)).toString(16)))];return o&&a[0].startsWith(a[0].charAt(1))&&a[1].startsWith(a[1].charAt(1))&&a[2].startsWith(a[2].charAt(1))&&a[3].startsWith(a[3].charAt(1))?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0)+a[3].charAt(0):a.join("")}(this.r,this.g,this.b,this.a,e)},e.prototype.toHex8String=function(e){return void 0===e&&(e=!1),"#"+this.toHex8(e)},e.prototype.toHexShortString=function(e){return void 0===e&&(e=!1),1===this.a?this.toHexString(e):this.toHex8String(e)},e.prototype.toRgb=function(){return{r:Math.round(this.r),g:Math.round(this.g),b:Math.round(this.b),a:this.a}},e.prototype.toRgbString=function(){var e=Math.round(this.r),t=Math.round(this.g),r=Math.round(this.b);return 1===this.a?"rgb(".concat(e,", ").concat(t,", ").concat(r,")"):"rgba(".concat(e,", ").concat(t,", ").concat(r,", ").concat(this.roundA,")")},e.prototype.toPercentageRgb=function(){var e=function(e){return"".concat(Math.round(100*Tt(e,255)),"%")};return{r:e(this.r),g:e(this.g),b:e(this.b),a:this.a}},e.prototype.toPercentageRgbString=function(){var e=function(e){return Math.round(100*Tt(e,255))};return 1===this.a?"rgb(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%)"):"rgba(".concat(e(this.r),"%, ").concat(e(this.g),"%, ").concat(e(this.b),"%, ").concat(this.roundA,")")},e.prototype.toName=function(){if(0===this.a)return"transparent";if(this.a<1)return!1;for(var e="#"+Bt(this.r,this.g,this.b,!1),t=0,r=Object.entries(Ut);t<r.length;t++){var n=r[t],o=n[0];if(e===n[1])return o}return!1},e.prototype.toString=function(e){var t=Boolean(e);e=null!=e?e:this.format;var r=!1,n=this.a<1&&this.a>=0;return t||!n||!e.startsWith("hex")&&"name"!==e?("rgb"===e&&(r=this.toRgbString()),"prgb"===e&&(r=this.toPercentageRgbString()),"hex"!==e&&"hex6"!==e||(r=this.toHexString()),"hex3"===e&&(r=this.toHexString(!0)),"hex4"===e&&(r=this.toHex8String(!0)),"hex8"===e&&(r=this.toHex8String()),"name"===e&&(r=this.toName()),"hsl"===e&&(r=this.toHslString()),"hsv"===e&&(r=this.toHsvString()),r||this.toHexString()):"name"===e&&0===this.a?this.toName():this.toRgbString()},e.prototype.toNumber=function(){return(Math.round(this.r)<<16)+(Math.round(this.g)<<8)+Math.round(this.b)},e.prototype.clone=function(){return new e(this.toString())},e.prototype.lighten=function(t){void 0===t&&(t=10);var r=this.toHsl();return r.l+=t/100,r.l=Rt(r.l),new e(r)},e.prototype.brighten=function(t){void 0===t&&(t=10);var r=this.toRgb();return r.r=Math.max(0,Math.min(255,r.r-Math.round(-t/100*255))),r.g=Math.max(0,Math.min(255,r.g-Math.round(-t/100*255))),r.b=Math.max(0,Math.min(255,r.b-Math.round(-t/100*255))),new e(r)},e.prototype.darken=function(t){void 0===t&&(t=10);var r=this.toHsl();return r.l-=t/100,r.l=Rt(r.l),new e(r)},e.prototype.tint=function(e){return void 0===e&&(e=10),this.mix("white",e)},e.prototype.shade=function(e){return void 0===e&&(e=10),this.mix("black",e)},e.prototype.desaturate=function(t){void 0===t&&(t=10);var r=this.toHsl();return r.s-=t/100,r.s=Rt(r.s),new e(r)},e.prototype.saturate=function(t){void 0===t&&(t=10);var r=this.toHsl();return r.s+=t/100,r.s=Rt(r.s),new e(r)},e.prototype.greyscale=function(){return this.desaturate(100)},e.prototype.spin=function(t){var r=this.toHsl(),n=(r.h+t)%360;return r.h=n<0?360+n:n,new e(r)},e.prototype.mix=function(t,r){void 0===r&&(r=50);var n=this.toRgb(),o=new e(t).toRgb(),i=r/100;return new e({r:(o.r-n.r)*i+n.r,g:(o.g-n.g)*i+n.g,b:(o.b-n.b)*i+n.b,a:(o.a-n.a)*i+n.a})},e.prototype.analogous=function(t,r){void 0===t&&(t=6),void 0===r&&(r=30);var n=this.toHsl(),o=360/r,i=[this];for(n.h=(n.h-(o*t>>1)+720)%360;--t;)n.h=(n.h+o)%360,i.push(new e(n));return i},e.prototype.complement=function(){var t=this.toHsl();return t.h=(t.h+180)%360,new e(t)},e.prototype.monochromatic=function(t){void 0===t&&(t=6);for(var r=this.toHsv(),n=r.h,o=r.s,i=r.v,a=[],s=1/t;t--;)a.push(new e({h:n,s:o,v:i})),i=(i+s)%1;return a},e.prototype.splitcomplement=function(){var t=this.toHsl(),r=t.h;return[this,new e({h:(r+72)%360,s:t.s,l:t.l}),new e({h:(r+216)%360,s:t.s,l:t.l})]},e.prototype.onBackground=function(t){var r=this.toRgb(),n=new e(t).toRgb(),o=r.a+n.a*(1-r.a);return new e({r:(r.r*r.a+n.r*n.a*(1-r.a))/o,g:(r.g*r.a+n.g*n.a*(1-r.a))/o,b:(r.b*r.a+n.b*n.a*(1-r.a))/o,a:o})},e.prototype.triad=function(){return this.polyad(3)},e.prototype.tetrad=function(){return this.polyad(4)},e.prototype.polyad=function(t){for(var r=this.toHsl(),n=r.h,o=[this],i=360/t,a=1;a<t;a++)o.push(new e({h:(n+a*i)%360,s:r.s,l:r.l}));return o},e.prototype.equals=function(t){return this.toRgbString()===new e(t).toRgbString()},e}();function Qt(){return Qt=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},Qt.apply(this,arguments)}function er(e){return(er=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function tr(e,t){return(tr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function rr(e,t,r){return(rr=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}()?Reflect.construct.bind():function(e,t,r){var n=[null];n.push.apply(n,t);var o=new(Function.bind.apply(e,n));return r&&tr(o,r.prototype),o}).apply(null,arguments)}function nr(e){var t="function"==typeof Map?new Map:void 0;return nr=function(e){if(null===e||(r=e,-1===Function.toString.call(r).indexOf("[native code]")))return e;var r;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return rr(e,arguments,er(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),tr(n,e)},nr(e)}var or=/%[sdj%]/g;function ir(e){if(!e||!e.length)return null;var t={};return e.forEach(function(e){var r=e.field;t[r]=t[r]||[],t[r].push(e)}),t}function ar(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var o=0,i=r.length;return"function"==typeof e?e.apply(null,r):"string"==typeof e?e.replace(or,function(e){if("%%"===e)return"%";if(o>=i)return e;switch(e){case"%s":return String(r[o++]);case"%d":return Number(r[o++]);case"%j":try{return JSON.stringify(r[o++])}catch(t){return"[Circular]"}break;default:return e}}):e}function sr(e,t){return null==e||(!("array"!==t||!Array.isArray(e)||e.length)||!(!function(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"date"===e||"pattern"===e}(t)||"string"!=typeof e||e))}function fr(e,t,r){var n=0,o=e.length;!function i(a){if(a&&a.length)r(a);else{var s=n;n+=1,s<o?t(e[s],i):r([])}}([])}var ur=function(e){var t,r;function n(t,r){var n;return(n=e.call(this,"Async Validation Error")||this).errors=t,n.fields=r,n}return r=e,(t=n).prototype=Object.create(r.prototype),t.prototype.constructor=t,tr(t,r),n}(nr(Error));function cr(e,t,r,n,o){if(t.first){var i=new Promise(function(t,i){var a=function(e){var t=[];return Object.keys(e).forEach(function(r){t.push.apply(t,e[r]||[])}),t}(e);fr(a,r,function(e){return n(e),e.length?i(new ur(e,ir(e))):t(o)})});return i.catch(function(e){return e}),i}var a=!0===t.firstFields?Object.keys(e):t.firstFields||[],s=Object.keys(e),f=s.length,u=0,c=[],l=new Promise(function(t,i){var l=function(e){if(c.push.apply(c,e),++u===f)return n(c),c.length?i(new ur(c,ir(c))):t(o)};s.length||(n(c),t(o)),s.forEach(function(t){var n=e[t];-1!==a.indexOf(t)?fr(n,r,l):function(e,t,r){var n=[],o=0,i=e.length;function a(e){n.push.apply(n,e||[]),++o===i&&r(n)}e.forEach(function(e){t(e,a)})}(n,r,l)})});return l.catch(function(e){return e}),l}function lr(e,t){return function(r){var n,o;return n=e.fullFields?function(e,t){for(var r=e,n=0;n<t.length;n++){if(null==r)return r;r=r[t[n]]}return r}(t,e.fullFields):t[r.field||e.fullField],(o=r)&&void 0!==o.message?(r.field=r.field||e.fullField,r.fieldValue=n,r):{message:"function"==typeof r?r():r,fieldValue:n,field:r.field||e.fullField}}}function pr(e,t){if(t)for(var r in t)if(t.hasOwnProperty(r)){var n=t[r];"object"==typeof n&&"object"==typeof e[r]?e[r]=Qt({},e[r],n):e[r]=n}return e}var dr,hr=function(e,t,r,n,o,i){!e.required||r.hasOwnProperty(e.field)&&!sr(t,i||e.type)||n.push(ar(o.messages.required,e.fullField))},gr=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,mr=/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i,yr={integer:function(e){return yr.number(e)&&parseInt(e,10)===e},float:function(e){return yr.number(e)&&!yr.integer(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(t){return!1}},date:function(e){return"function"==typeof e.getTime&&"function"==typeof e.getMonth&&"function"==typeof e.getYear&&!isNaN(e.getTime())},number:function(e){return!isNaN(e)&&"number"==typeof e},object:function(e){return"object"==typeof e&&!yr.array(e)},method:function(e){return"function"==typeof e},email:function(e){return"string"==typeof e&&e.length<=320&&!!e.match(gr)},url:function(e){return"string"==typeof e&&e.length<=2048&&!!e.match(function(){if(dr)return dr;var e="[a-fA-F\\d:]",t=function(t){return t&&t.includeBoundaries?"(?:(?<=\\s|^)(?="+e+")|(?<="+e+")(?=\\s|$))":""},r="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",n="[a-fA-F\\d]{1,4}",o=("\n(?:\n(?:"+n+":){7}(?:"+n+"|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n(?:"+n+":){6}(?:"+r+"|:"+n+"|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::1.2.3.4\n(?:"+n+":){5}(?::"+r+"|(?::"+n+"){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:1.2.3.4\n(?:"+n+":){4}(?:(?::"+n+"){0,1}:"+r+"|(?::"+n+"){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:1.2.3.4\n(?:"+n+":){3}(?:(?::"+n+"){0,2}:"+r+"|(?::"+n+"){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:1.2.3.4\n(?:"+n+":){2}(?:(?::"+n+"){0,3}:"+r+"|(?::"+n+"){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:1.2.3.4\n(?:"+n+":){1}(?:(?::"+n+"){0,4}:"+r+"|(?::"+n+"){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:1.2.3.4\n(?::(?:(?::"+n+"){0,5}:"+r+"|(?::"+n+"){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::1.2.3.4\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\n").replace(/\s*\/\/.*$/gm,"").replace(/\n/g,"").trim(),i=new RegExp("(?:^"+r+"$)|(?:^"+o+"$)"),a=new RegExp("^"+r+"$"),s=new RegExp("^"+o+"$"),f=function(e){return e&&e.exact?i:new RegExp("(?:"+t(e)+r+t(e)+")|(?:"+t(e)+o+t(e)+")","g")};f.v4=function(e){return e&&e.exact?a:new RegExp(""+t(e)+r+t(e),"g")},f.v6=function(e){return e&&e.exact?s:new RegExp(""+t(e)+o+t(e),"g")};var u=f.v4().source,c=f.v6().source;return dr=new RegExp("(?:^(?:(?:(?:[a-z]+:)?//)|www\\.)(?:\\S+(?::\\S*)?@)?(?:localhost|"+u+"|"+c+'|(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))(?::\\d{2,5})?(?:[/?#][^\\s"]*)?$)',"i")}())},hex:function(e){return"string"==typeof e&&!!e.match(mr)}},vr="enum",br={required:hr,whitespace:function(e,t,r,n,o){(/^\s+$/.test(t)||""===t)&&n.push(ar(o.messages.whitespace,e.fullField))},type:function(e,t,r,n,o){if(e.required&&void 0===t)hr(e,t,r,n,o);else{var i=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(i)>-1?yr[i](t)||n.push(ar(o.messages.types[i],e.fullField,e.type)):i&&typeof t!==e.type&&n.push(ar(o.messages.types[i],e.fullField,e.type))}},range:function(e,t,r,n,o){var i="number"==typeof e.len,a="number"==typeof e.min,s="number"==typeof e.max,f=t,u=null,c="number"==typeof t,l="string"==typeof t,p=Array.isArray(t);if(c?u="number":l?u="string":p&&(u="array"),!u)return!1;p&&(f=t.length),l&&(f=t.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"_").length),i?f!==e.len&&n.push(ar(o.messages[u].len,e.fullField,e.len)):a&&!s&&f<e.min?n.push(ar(o.messages[u].min,e.fullField,e.min)):s&&!a&&f>e.max?n.push(ar(o.messages[u].max,e.fullField,e.max)):a&&s&&(f<e.min||f>e.max)&&n.push(ar(o.messages[u].range,e.fullField,e.min,e.max))},enum:function(e,t,r,n,o){e[vr]=Array.isArray(e[vr])?e[vr]:[],-1===e[vr].indexOf(t)&&n.push(ar(o.messages[vr],e.fullField,e[vr].join(", ")))},pattern:function(e,t,r,n,o){if(e.pattern)if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||n.push(ar(o.messages.pattern.mismatch,e.fullField,t,e.pattern));else if("string"==typeof e.pattern){new RegExp(e.pattern).test(t)||n.push(ar(o.messages.pattern.mismatch,e.fullField,t,e.pattern))}}},wr=function(e,t,r,n,o){var i=e.type,a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t,i)&&!e.required)return r();br.required(e,t,n,a,o,i),sr(t,i)||br.type(e,t,n,a,o)}r(a)},xr={string:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t,"string")&&!e.required)return r();br.required(e,t,n,i,o,"string"),sr(t,"string")||(br.type(e,t,n,i,o),br.range(e,t,n,i,o),br.pattern(e,t,n,i,o),!0===e.whitespace&&br.whitespace(e,t,n,i,o))}r(i)},method:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t)&&!e.required)return r();br.required(e,t,n,i,o),void 0!==t&&br.type(e,t,n,i,o)}r(i)},number:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(""===t&&(t=void 0),sr(t)&&!e.required)return r();br.required(e,t,n,i,o),void 0!==t&&(br.type(e,t,n,i,o),br.range(e,t,n,i,o))}r(i)},boolean:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t)&&!e.required)return r();br.required(e,t,n,i,o),void 0!==t&&br.type(e,t,n,i,o)}r(i)},regexp:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t)&&!e.required)return r();br.required(e,t,n,i,o),sr(t)||br.type(e,t,n,i,o)}r(i)},integer:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t)&&!e.required)return r();br.required(e,t,n,i,o),void 0!==t&&(br.type(e,t,n,i,o),br.range(e,t,n,i,o))}r(i)},float:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t)&&!e.required)return r();br.required(e,t,n,i,o),void 0!==t&&(br.type(e,t,n,i,o),br.range(e,t,n,i,o))}r(i)},array:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(null==t&&!e.required)return r();br.required(e,t,n,i,o,"array"),null!=t&&(br.type(e,t,n,i,o),br.range(e,t,n,i,o))}r(i)},object:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t)&&!e.required)return r();br.required(e,t,n,i,o),void 0!==t&&br.type(e,t,n,i,o)}r(i)},enum:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t)&&!e.required)return r();br.required(e,t,n,i,o),void 0!==t&&br.enum(e,t,n,i,o)}r(i)},pattern:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t,"string")&&!e.required)return r();br.required(e,t,n,i,o),sr(t,"string")||br.pattern(e,t,n,i,o)}r(i)},date:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t,"date")&&!e.required)return r();var a;if(br.required(e,t,n,i,o),!sr(t,"date"))a=t instanceof Date?t:new Date(t),br.type(e,a,n,i,o),a&&br.range(e,a.getTime(),n,i,o)}r(i)},url:wr,hex:wr,email:wr,required:function(e,t,r,n,o){var i=[],a=Array.isArray(t)?"array":typeof t;br.required(e,t,n,i,o,a),r(i)},any:function(e,t,r,n,o){var i=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(sr(t)&&!e.required)return r();br.required(e,t,n,i,o)}r(i)}};function kr(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var Or=kr(),_r=function(){function e(e){this.rules=null,this._messages=Or,this.define(e)}var t=e.prototype;return t.define=function(e){var t=this;if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!=typeof e||Array.isArray(e))throw new Error("Rules must be an object");this.rules={},Object.keys(e).forEach(function(r){var n=e[r];t.rules[r]=Array.isArray(n)?n:[n]})},t.messages=function(e){return e&&(this._messages=pr(kr(),e)),this._messages},t.validate=function(t,r,n){var o=this;void 0===r&&(r={}),void 0===n&&(n=function(){});var i=t,a=r,s=n;if("function"==typeof a&&(s=a,a={}),!this.rules||0===Object.keys(this.rules).length)return s&&s(null,i),Promise.resolve(i);if(a.messages){var f=this.messages();f===Or&&(f=kr()),pr(f,a.messages),a.messages=f}else a.messages=this.messages();var u={};(a.keys||Object.keys(this.rules)).forEach(function(e){var r=o.rules[e],n=i[e];r.forEach(function(r){var a=r;"function"==typeof a.transform&&(i===t&&(i=Qt({},i)),n=i[e]=a.transform(n)),(a="function"==typeof a?{validator:a}:Qt({},a)).validator=o.getValidationMethod(a),a.validator&&(a.field=e,a.fullField=a.fullField||e,a.type=o.getType(a),u[e]=u[e]||[],u[e].push({rule:a,value:n,source:i,field:e}))})});var c={};return cr(u,a,function(t,r){var n,o=t.rule,s=!("object"!==o.type&&"array"!==o.type||"object"!=typeof o.fields&&"object"!=typeof o.defaultField);function f(e,t){return Qt({},t,{fullField:o.fullField+"."+e,fullFields:o.fullFields?[].concat(o.fullFields,[e]):[e]})}function u(n){void 0===n&&(n=[]);var u=Array.isArray(n)?n:[n];!a.suppressWarning&&u.length&&e.warning("async-validator:",u),u.length&&void 0!==o.message&&(u=[].concat(o.message));var l=u.map(lr(o,i));if(a.first&&l.length)return c[o.field]=1,r(l);if(s){if(o.required&&!t.value)return void 0!==o.message?l=[].concat(o.message).map(lr(o,i)):a.error&&(l=[a.error(o,ar(a.messages.required,o.field))]),r(l);var p={};o.defaultField&&Object.keys(t.value).map(function(e){p[e]=o.defaultField}),p=Qt({},p,t.rule.fields);var d={};Object.keys(p).forEach(function(e){var t=p[e],r=Array.isArray(t)?t:[t];d[e]=r.map(f.bind(null,e))});var h=new e(d);h.messages(a.messages),t.rule.options&&(t.rule.options.messages=a.messages,t.rule.options.error=a.error),h.validate(t.value,t.rule.options||a,function(e){var t=[];l&&l.length&&t.push.apply(t,l),e&&e.length&&t.push.apply(t,e),r(t.length?t:null)})}else r(l)}if(s=s&&(o.required||!o.required&&t.value),o.field=t.field,o.asyncValidator)n=o.asyncValidator(o,t.value,u,t.source,a);else if(o.validator){try{n=o.validator(o,t.value,u,t.source,a)}catch(l){console.error,a.suppressValidatorError||setTimeout(function(){throw l},0),u(l.message)}!0===n?u():!1===n?u("function"==typeof o.message?o.message(o.fullField||o.field):o.message||(o.fullField||o.field)+" fails"):n instanceof Array?u(n):n instanceof Error&&u(n.message)}n&&n.then&&n.then(function(){return u()},function(e){return u(e)})},function(e){!function(e){var t=[],r={};function n(e){var r;Array.isArray(e)?t=(r=t).concat.apply(r,e):t.push(e)}for(var o=0;o<e.length;o++)n(e[o]);t.length?(r=ir(t),s(t,r)):s(null,i)}(e)},i)},t.getType=function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!=typeof e.validator&&e.type&&!xr.hasOwnProperty(e.type))throw new Error(ar("Unknown rule type %s",e.type));return e.type||"string"},t.getValidationMethod=function(e){if("function"==typeof e.validator)return e.validator;var t=Object.keys(e),r=t.indexOf("message");return-1!==r&&t.splice(r,1),1===t.length&&"required"===t[0]?xr.required:xr[this.getType(e)]||void 0},e}();_r.register=function(e,t){if("function"!=typeof t)throw new Error("Cannot register a validator by type, validator is not a function");xr[e]=t},_r.warning=function(){},_r.messages=Or,_r.validators=xr;export{je as E,_r as S,Kt as T,le as c,me as p,ge as u,Ft as y};
