<template>
  <div class="hearder min_wrapper_1500">
    <header-top @setIsReject='setIsReject'></header-top>
    <div class="header_content">
      <div class="header_logo" @click="toIndex">
        <img
          class="logo_img"
          src="@/assets/header/hglogo.jpg"
        />
        <!-- <div class="header_text">灰谷网</div> -->
      </div>
      <div class="header_nav_list">
        <div class="header_nav">
          <div
            @click="gohome"
            :class="[
              'header_nav_cont',
              $route.path == '/' ? 'active' : '',
            ]"
          >
            首页
          </div>
          <div
            @click="nextPage"
            :class="[
              'header_nav_cont',
              $route.path == '/auction' ? 'active' : '',
            ]"
          >
            <div class="txt">竞价大厅</div> 
          </div>
          <div
            @click="goBiaodi"
            :class="[
              'header_nav_cont',
              $route.path == '/biaodi' ? 'active' : '',
            ]"
          >
            <div class="txt">标的信息</div> 
          </div>
          <div
            @click="toAnnouncement(29)"
            :class="[
              'header_nav_cont',
              $route.path == '/announcement' && $route.query.id == 29
                ? 'active'
                : '',
            ]"
          >
            <div class="txt">采购信息</div> 
          </div>
          <div
            @click="toAnnouncement(30)"
            :class="[
              'header_nav_cont',
              $route.path == '/announcement' && $route.query.id == 30
                ? 'active'
                : '',
            ]"
          >
            <div class="txt">销售信息</div> 
          </div>
          <!-- <div
            @click="goHangye"
            :class="[
              'header_nav_cont',
              $route.path == '/news' && $route.query.cateid == 2
                ? 'active'
                : '',
            ]"
          >
            <div class="txt">行业资讯</div> 
          </div> -->
          <div
            @click="nextAbout"
            :class="[
              'header_nav_cont',
              $route.path == '/about' ? 'active' : '',
            ]"
          >
            <div class="txt">关于我们</div>
          </div>
        </div>
      </div>
      <div class="header_search">
        <div class="header_search_input">
          <!-- <el-input v-model="keyword" :placeholder="sousuo"></el-input> -->
          <input
            v-model="keyword"
            type="text"
            :placeholder="sousuo"
          />
        </div>
        <div @click="searchClick" class="header_search_btn">
          搜索
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ajax from "@/store/Ajax";
import HeaderTop from './HeaderTop.vue';
export default {
  name: "Home",
  props: ["inputShu"],
  components:{
    HeaderTop
  },
  data() {
    return {
      keyword: "",
      userInfo: null,
      sousuo: "请输入拍卖会名称",
      pcUrl:this.$Pc.pcUrl
    };
  },
  created() {
    // console.log(this.$route,'$route')
    if (localStorage.getItem("userInfo")) {
      this.userInfo = JSON.parse(localStorage.getItem("userInfo"));
      console.log(this.userInfo,'uer')
    }

    if (this.$route.path == "/biaodi") {
      this.sousuo = "请输入标的关键词";
    }

    if (this.$route.path == "/auction") {
      this.sousuo = "请输入竞价会关键词";
    }

    if (this.$route.path == "/auctionfirm") {
      this.sousuo = "请输入企业名称";
    }

    // this.sousuo = this.inputShu;
  },
  methods: {
    goHangye() {
      this.$router.push({
        path: '/news',
        query: { //路由传参时push和query搭配使用 ，作用时传递参数
          cateid: 2
        }
      })
    },
    setIsReject(val){
      this.$emit("setIsReject", val);
    },
    toIndex() {
      let routeData = this.$router.resolve("/");
      window.open(routeData.href, "_blank");
    },
    nextPage() {
      let routeData = this.$router.resolve("/auction");
      window.open(routeData.href, "_blank");
    },
    gohome() {
      let routeData = this.$router.resolve("/");
      window.open(routeData.href, "_blank");
    },
    goBiaodi() {
      let routeData = this.$router.resolve("/biaodi");
      window.open(routeData.href, "_blank");
    },
    toAnnouncement(id){
      this.$router.push({
        path: '/announcement',
        query: { //路由传参时push和query搭配使用 ，作用时传递参数
          id
        }
      })
      // let routeData = this.$router.resolve("/announcement");
      // window.open(routeData.href, "_blank");
    },
    nextQuotation() {
      let routeData = this.$router.resolve("/Quotation");
      window.open(routeData.href, "_blank");
    },
    nextAbout() {
      this.$router.push("/about");
    },
    searchClick() {
      if (
        this.$route.path == "/biaodi" ||
        this.$route.path == "/auction" ||
        this.$route.path == "/auctionfirm" ||
        this.$route.path == "/integralNew"
      ) {
        this.$emit("getKeyWord", this.keyword);
      } else {
        this.$router.push({
          //核心语句
          path: "/auction", //跳转的路径
          query: {
            //路由传参时push和query搭配使用 ，作用时传递参数
            password: this.keyword,
          },
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
body {
  margin: 0;
}
.headers {
  background-color: #fff;
}
.header_content {
  display: flex;
  -js-display: flex;
  align-items: center;
  justify-content: space-between;
  width: 1200px;
  height: 100px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  margin: 0 auto;
  .header_logo{
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    // width: 200px;
    .logo_img{
      display: block;
      // width: 52px;
      margin-right: 17px;
    }
    .header_text{
      margin-top: -5px;
      font-size: 32px;
      color: #3a3a3a;
      font-family: 'STHupo';
    }
  }
}
.header_nav_list{
  display: flex;
  -js-display: flex;
  align-items: center;
  height: 100px;
}
.header_title_right_new{
  display: flex;
  -js-display: flex;
  align-items: center;
}
.header_nav {
  display: flex;
  align-items: center;
  height: 100px;
}
.header_nav_cont{
  margin-right: 50px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 20px;
  color: #666666;
  line-height: 28px;
  text-align: left;
  font-style: normal;
  cursor: pointer;
  transition: all .2s;
  &:last-child{
    margin-right: 0;
  }
}
.header_nav_cont.active,.header_nav_cont:hover{
  color: #A80012;
  font-weight: bold;
}
.header_search{
  display: flex;
  align-items: center;
  justify-content: center;
  width: 295px;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #A80012;
  color: #000;
  .header_search_input{
    flex: 1;
  }
  input{
    width: 100%;
    height: 40px;
    border: 0;
    outline: none;
    background: inherit;
    padding: 0 15px;
    box-sizing: border-box;
  }
}
.header_search_btn{
  display: flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 100%;
  background: #A80012;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #FFFFFF;
  line-height: 20px;
  text-align: left;
  font-style: normal;
  cursor: pointer;
}
</style>
