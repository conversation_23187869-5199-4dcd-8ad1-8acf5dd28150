<template>
	<div class="FootersBottom min_wrapper_1500">
		<!-- <div class="footer_top">
			<div class="footer_top_content">
				<div class="footer_top_nav_list">
					<div class="footer_top_nav_title">联系我们</div>
					<div style="display: flex;align-items: center;">
						<img style="display:block" src="@/assets/images/index/rexian.png" width="24" height="24" />
						<div style="font-size: 16px;color: #fff;margin-left: 13px;">全国服务热线：400-999-0233</div>
					</div>
					<div class="footer_top_nav_item">邮箱：<EMAIL></div>
					<div class="footer_top_nav_item">地址：河南自贸试验区郑州片区（郑东）商务外环路20号11楼1109室</div>
				</div>
				<div class="footer_top_code">
					<div class="erweima" style="margin-right:30px">
						<img class="erweima_1" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/gzhao.jpg" />
						<span class="erweima_2" style="color: #fff;">关注公众号</span>
					</div>
					<div class="erweima" style="margin-right:30px">
						<img class="erweima_1" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/images/appxiazai.png" />
						<span class="erweima_2" style="color: #fff;">扫码下载APP</span>
					</div>
					<div class="erweima">
						<img class="erweima_1" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/xiaochengxu.jpg" />
						<span class="erweima_2" style="color: #fff;">打开小程序</span>
					</div>
				</div>
			</div>
		</div> -->
		<div class="FootersBottom_div_bottom">
			<div class="FootersBottom_div_bottom_left_one">
				<a href="/">网站首页</a>|
				<a @click="nextAuctionfirm">&nbsp;拍卖公司</a>|
				<a @click="goDetailNew('5',126)">&nbsp;注册流程</a>|
				<a @click="goDetailNew('3',156)">&nbsp;竞价流程</a>|
				<a @click="goDetailNew('1',129)">&nbsp;保证金缴纳</a>|
				<a @click="goDetailNew('1',130)">&nbsp;服务费标准</a>|
				<a @click="goHangyes">&nbsp;常见问题</a>|
				<a @click="goDetailNew('1',131)">&nbsp;免责声明</a>
			</div>
			<div style="display:flex;align-items: center;" class="FootersBottom_div_bottom_left_one">
				<a href="https://www.hghello.com/maigongxuke.pdf" target="_blank">河南麦工拍卖有限公司</a> &nbsp;&nbsp;|&nbsp;&nbsp;河南麦工网&nbsp;&nbsp;|&nbsp;&nbsp;备案号：
				<a href="http://beian.miit.gov.cn" target="_blank"
					style="font-size: 12px;margin-right:0">豫ICP备2024088249号-3</a>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				url: this.$Pc.ossUrl,
				selectIndex: 0,
				lists: [1, 2, 3, 4, 5, 6, 7, 8]
			
			}
		},
		methods: {
			nextAuctionfirm() {
				let routeData = this.$router.resolve("/auctionfirm");
				window.open(routeData.href, "_blank");
			},
			goDetailNew(cateid,id) {
				this.$router.push({
					path: '/news',
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						cateid: cateid,
						id: id
					}
				})
			},
			nextAbout() {
				return
				this.$router.push('/about')
			},
			goHangyes() {
				console.log('11')
				this.$router.push({
					path: '/news',
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						cateid: 1
					}
				})
			},
		},
		created() {

		}
	
	}
</script>

<style lang="scss">
	.FootersBottom {
		display: flex;
		-js-display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		// height: 390px;
		background-color: #323332;
	}
	.footer_top{
		width: 100%;
		padding-bottom: 36px;
		border-bottom: 1px solid rgba(255,255,255,0.16);
		.footer_top_content{
			display: flex;
			justify-content: space-between;
			width: 1200px;
			margin: 48px auto 0;
			.footer_top_nav_list{
				// display: flex;
				color: #fff;
				.footer_top_nav_title{
					margin-bottom: 20px;
					font-size: 24px;
				}
				.footer_top_nav_item{
					margin-top: 15px;
					font-size: 16px;
				}
			}
			.footer_top_code{
				display: flex;
			}
		}
	}

	.fbk {
		width: auto;
		margin-right: 60px;
		&:last-child{
			margin-right: 0;
		}
	}

	.title_b {
		margin-bottom: 16px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 500;
		font-size: 16px;
		color: #FFFFFF;
		line-height: 18px;
	}

	.f_list {
		margin-top: 6px;
		font-size: 12px;
	}

	.f_list li {
		margin-bottom: 16px;
	}

	.f_list li a {
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 13px;
		color: #D8D8D8;
		line-height: 18px;
	}

	// .f_list li a:hover {
	// 	color: #F60;
	// }

	.f_list li span {
		float: right;
		width: 60px;
		text-align: center
	}

	.FootersBottom a {
		color: white;
	}

	.FootersBottom a:link,
	a:visited,
	a:active {
		text-decoration: none;
	}

	a:hover {
		text-decoration: none;
	}

	p,
	ul,
	li {
		list-style: none;
		margin: 0px;
		padding: 0px;
	}

	.f_nr {
		margin-top: 6px;
		padding: 0 10px;
		color: #fff;
		font-size: 12px;
	}

	.erweima {
		min-width: 100px;
		// height: 130px;
	}

	.erweima_1 {
		display: block;
		width: 130px;
		height: 130px;
		border-radius: 10px;
	}

	.erweima_2 {
		display: block;
		margin-top: 15px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #FFFFFF;
		line-height: 20px;
		text-align: center;
	}

	.FootersBottom_div_bottom {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		width: 1200px;
		box-sizing: border-box;
		overflow: hidden;
	}
	.FootersBottom_div_bottom_left{
		margin-top: 32px;
	}

	.FootersBottom_div_bottom_left_one {
		margin: 20px 0;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #FFFFFF;
		line-height: 17px;
	}

	.FootersBottom_div_bottom_left_one a {
		margin-top: 2px;
		margin-right: 8px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #FFFFFF;
		line-height: 17px;
		cursor: pointer;
	}

	// .FootersBottom_div_bottom_left_one a:hover {
	// 	color: #F60;
	// }
</style>
