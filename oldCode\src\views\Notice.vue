<template>
	<div class="min_wrapper_1500" style="background-color: #F7F7F7;">
		<Headers />
		<div style="background-color: #f7f7f7;padding-top: 20px;padding-bottom: 80px;box-sizing: border-box;">
			<div style="width: 1200px;margin: 0 auto;">
				<div class="auctionfirm_two">
					<div :class="i%2==0?'auctionfirm_two_div auctionfirm_two_divs':'auctionfirm_two_div'"
						v-for="(item,i) in qiyePaimaihuiList" @click="goInfo(item)">
						<div style="width: 180px;height: 180px;background-color: #F6F6F6;"
							class="auctionfirm_two_divsss">
							<img :src="url +  item.pmh_pic" @error="(e)=>e.target.src = item.pmh_pic ? 'https://oss.yunpaiwang.com/'+item.pmh_pic : ''">
						</div>
						<div class="auctionfirm_two_div_rig">
							<div class="auctionfirm_two_div_rig_o">{{item.pmh_name}} </div>
							<div class="auctionfirm_two_div_rig_t">
								受委托，我公司定于{{item.start_time_name}}在灰谷网公开举行竞价会，物资内容以委托方所指定现场实物为准。
							</div>
							<div class="auctionfirm_two_div_rig_f">
								<div>{{item.parentname}}</div>
								<div>{{item.start_time_name}}</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="auctionpage">
				<el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
					:current-page.sync="page" :page-size="per_page" layout="prev, pager, next, jumper" :total="total">
				</el-pagination>
			</div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import ajax from '../store/Ajax'
	export default {
		name: 'Auction',
		components: {
			Headers,
			FootersBottom,
			homeRight
		},
		data() {
			return {
				selectIndex: 0,
				pmqiyeDetails: {},
				url: '',
				page: 1,
				qiyePaimaihuiList: [],
				biaodiList: [],
				gongaoList: [],
				total: 0,
				per_page: 0
			}
		},
		created() {
			this.url = this.$Pc.ossUrl
			console.log(this.$route)
			// 拍卖详情
			ajax.pmqiyeDetail({
				id: this.$route.query.id * 1
			}).then(res => {
				this.pmqiyeDetails = res.data
			})
			this.getPmhList()
		},
		watch: {
			page() {
				this.getPmhList()
			}
		},
		methods: {
			goInfo(item) {
				this.$router.push({ //核心语句
					path: '/noticeDetail', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: item.id,
					}
				})
			},
			getPmhList() {
				ajax.pmhlistInfo({
					page: this.page
				}).then(res => {
					this.qiyePaimaihuiList = res.data.data
					this.total = res.data.total
					this.per_page = res.data.per_page
				})
			},
			handleSizeChange(e) {
				this.page = e
			},
			handleCurrentChange(e) {
				this.page = e
			}
		}
	}
</script>

<style type="text/css" scoped="scoped">
	.auction {
		width: 1200px;
		min-height: 1000px;
		padding-bottom: 50px;
		box-sizing: border-box;
		margin: 0 auto;
	}

	.auctionFirmDetail_three {
		height: 66px;
		background: #FFFFFF;
		border-bottom: 2px solid #D6363B;
	}

	.auctionFirmDetail_threec {
		width: 1200px;
		margin: 0 auto;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auctionFirmDetail_threec>div {
		width: 212px;
		height: 66px;
		text-align: center;
		line-height: 66px;
		font-size: 18px;
		font-weight: 400;
		color: #333333;
		cursor: pointer;
	}

	.auctionFirmDetail_threec>div span {
		color: #D6363B;
	}

	.auctionFirmDetail_threec .auctionFirmDetail_threecs {
		width: 212px;
		height: 66px;
		background: #D6363B;
		font-size: 18px;
		font-weight: 400;
		color: #FFFFFF;
	}

	.auctionFirmDetail_threec .auctionFirmDetail_threecs span {
		color: #FFFFFF;
	}

	.auctionFirmDetail_threecss:hover {
		width: 212px;
		height: 66px;
		background: #D6363B !important;
		font-size: 18px !important;
		font-weight: 400 !important;
		color: #FFFFFF !important;
	}

	.auctionFirmDetail_threecss:hover span {
		color: #FFFFFF !important;
	}

	.auctionFirmDetail_one {
		max-width: 100%;
		min-width: 1200px;
		background-image: url(http://www.yunpaiwang.com/templates/default/images/bg1.png);
		background-size: 100% 100%;
		height: 300px;
	}

	.auctionFirmDetail_two {
		min-height: 252px;
		background-color: #FFFFFF;
	}

	.auctionFirmDetail_two_view {
		width: 1200px;
		margin: 0 auto;
		min-height: 252px;
		display: flex;
		-js-display: flex;

	}

	.auctionFirmDetail_two_view_left {
		padding-top: 54px;
		box-sizing: border-box;
	}

	.auctionFirmDetail_two_view_left_one {
		display: flex;
		-js-display: flex;
	}

	.auctionFirmDetail_two_view_left_ones {
		width: 80px;
		height: 80px;
		background: #FFFFFF;
		border: 1px solid #EEEEEE;
		border-radius: 4px;
		background-color: red;
	}

	.auctionFirmDetail_two_view_left_oness {
		height: 80px;
		font-size: 14px;
		font-weight: bold;
		color: #999;
		margin-left: 13px;
	}

	.auctionFirmDetail_two_view_left_oness>div:nth-child(1) {
		font-size: 18px;
		font-weight: bold;
		color: #333333;
		margin-bottom: 18px;
	}

	.auctionFirmDetail_two_view_left_two {
		width: 120px;
		height: 32px;
		background: #D6363B;
		border-radius: 2px;
		text-align: center;
		line-height: 32px;
		font-size: 14px;
		font-weight: 400;
		color: #FFFFFF;
		margin-left: 105px;
		margin-top: 36px;
		cursor: pointer;
	}

	.auctionFirmDetail_two_view_right {
		margin-left: 149px;
		width: 650px;
		min-height: 156px;
		font-size: 16px;
		font-weight: 400;
		color: #333333;
		line-height: 28px;
		padding-top: 54px;
		box-sizing: border-box;
	}

	.auction_title {
		height: 252px;
		background: #FFFFFF;
		border: 1px solid #EEEEEE;
		margin-top: 20px;
	}


	.auction_three {
		margin-top: 30px;
	}

	.auction_three_div {
		display: flex;
		-js-display: flex;
		align-items: center;
		height: 265px;
		margin-bottom: 15px;
	}

	.auction_three_div>img {
		width: 800px;
		min-height: auto;
		max-height: 265px;
	}

	.auction_three_div_right {
		width: 394px;
		height: 265px;
		padding-top: 20px;
		box-sizing: border-box;
		position: relative;
		background-color: #FFFFFF;
	}

	.auction_three_div_right_one {
		font-size: 18px;
		font-weight: 400;
		color: #333333;
		width: 290px;
		height: 22px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		margin-left: 20px;
	}

	.auction_three_div_right_two {
		padding-left: 20px;
		box-sizing: border-box;
		color: #7A7A7A;
		font-size: 14px;
		margin-top: 10px;
	}

	.auction_three_div_right_three {
		padding-left: 20px;
		box-sizing: border-box;
		color: #00000;
		font-size: 16px;
		margin-top: 40px;
	}

	.auction_three_div_right_three span {
		color: #7A7A7A;
	}

	.auction_three_div_right_four {
		padding-left: 20px;
		box-sizing: border-box;
		font-size: 14px;
		font-weight: 400;
		color: #7A7A7A;
		display: flex;
		-js-display: flex;
		align-items: center;
		margin-top: 61px;
	}

	.auction_three_div_right_four>div {
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auction_three_div_right_img {
		width: 128px;
		height: 103px;
		position: absolute;
		right: -40px;
		top: 30%;
		transform: translateX(-50%);
	}

	.auction_three_div_right_five {
		width: 100%;
		height: 36px;
		position: absolute;
		bottom: 0px;
		display: flex;
		-js-display: flex;
		align-items: center;
		font-size: 0;
	}

	.auction_three_div_right_five_o {
		width: 307px;
		position: relative;
	}

	.auction_three_div_right_five_o_pp {
		position: absolute;
		width: 307px;
		height: 36px;
		/* text-align: center; */
		font-size: 14px;
		font-weight: 400;
		color: #FFFFFF;
		top: 0;
		line-height: 36px;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.auction_three_div_right_five_o img {
		width: 307px;
		height: 36px;
	}

	.auction_three_div_right_five_i {
		width: 101px;
		height: 36px;
		position: relative;
		left: -15px;
		top: 0px;
	}

	.homeCentet_three_left_two_ri_backRightss {
		position: absolute;
		right: -8px;
		top: 16px;
	}

	.homeCentet_three_left_two_ri_backRightss div {
		position: absolute;
		width: 100%;
		height: 100%;
		text-align: center;
		color: white;
		top: 0;
		font-size: 12px;
		line-height: 22px;
	}

	.auctionpage {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
	}

	/deep/.el-pagination.is-background .el-pager li:not(.disabled).active {
		background-color: #D6363B;
		color: white;
	}

	.biaodi_three {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 20px;
	}

	.biaodi_three_div {
		width: 285px;
		height: 374px;
		background: #FFFFFF;
	}

	.biaodi_three_div_img {
		width: 285px;
		height: 200px;
	}


	.homeCentet_four_centet {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 10px;
	}

	.homeCentet_four_centet_view {
		width: 285px;
		height: 374px;
		background: #FFFFFF;
		position: relative;
		margin-bottom: 20px;
		cursor: pointer;
	}

	.homeCentet_four_centet_views {
		margin-left: 20px;
	}

	.homeCentet_four_centet_view>img {
		width: 285px;
		height: 200px;
	}

	.homeCentet_four_centet_view_ti {
		width: 252px;
		height: 37px;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		text-overflow: -o-ellipsis-lastline;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		margin: 11px auto 11px;
	}

	.homeCentet_four_centet_view_two {
		width: 252px;
		height: 30px;
		margin: 0 auto;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.homeCentet_four_centet_view_two_left {
		width: 194px;
		height: 30px;
		position: relative;
	}

	.homeCentet_four_centet_view_two_left img {
		width: 194px;
		height: 30px;
	}

	.homeCentet_four_centet_view_two_left div {
		width: 194px;
		height: 30px;
		position: absolute;
		left: 0;
		top: 0;
		font-size: 12px;
		font-weight: bold;
		color: #FFFFFF;
		text-align: center;
	}

	.homeCentet_four_centet_view_two_left>div span {
		height: 15px;
		font-size: 20px;
		font-weight: bold;
		color: #FEFEFE;
		position: relative;
		top: 2px;
	}

	.homeCentet_four_centet_view_two_right {
		width: 81px !important;
		height: 30px !important;
		position: relative;
		left: -12px;
	}

	.homeCentet_four_centet_view_three {
		width: 252px;
		margin: 17px auto 0;
		font-size: 12px;
		font-weight: 400;
		color: #7A7A7A;
	}

	.homeCentet_four_centet_view_three span {
		color: #000000;
	}

	.homeCentet_four_centet_view_enter {
		width: 100%;
		height: 38px;
		position: absolute;
		bottom: 0;
		left: 0;
		border-top: 1px solid #EEEEEE;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.homeCentet_four_centet_view_enter>div:nth-child(1) {
		display: flex;
		-js-display: flex;
		align-items: center;
		font-size: 12px;
		font-weight: 400;
		color: #7A7A7A;
	}

	.homeCentet_four_centet_view_enter>div:nth-child(1) img {
		width: 11px;
		height: 16px;
		margin-right: 10px;
	}

	.homeCentet_four_centet_view_enters {
		width: 70px;
		height: 19px;
		background: #169173;
		text-align: center;
		line-height: 19px;
		font-size: 12px;
		font-weight: 400;
		color: #FFFFFF;
	}

	.homeCentet_four_centet_view_enterss {
		width: 70px;
		height: 19px;
		background: #B3B3B3;
		text-align: center;
		line-height: 19px;
		font-size: 12px;
		font-weight: 400;
		color: #FFFFFF;
	}

	.homeCentet_four_centet_view_entersss {
		width: 70px;
		height: 19px;
		background: #D6363B;
		text-align: center;
		line-height: 19px;
		font-size: 12px;
		font-weight: 400;
		color: #FFFFFF;
	}

	.homeCentet_four_centets {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 14px;
	}

	.homeCentet_four_centetsQi {
		width: 224px;
		height: 100px;
		margin-bottom: 20px;
	}

	.homeCentet_four_centetsQis {
		margin-left: 20px;
	}

	.homeCentet_four_centets img {
		width: 224px;
		height: 100px;
	}

	/* 第二样式 */
	.auctionfirm_two {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 14px;
	}

	.auctionfirm_two_div {
		width: 592px;
		height: 220px;
		background: #FFFFFF;
		margin-bottom: 20px;
		padding: 20px;
		box-sizing: border-box;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auctionfirm_two_divsss {
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auctionfirm_two_divs {
		margin-right: 16px;
	}

	.auctionfirm_two_div img {
		width: 180px;
		min-height: auto;
		max-height: 180px;
	}

	.auctionfirm_two_div_rig {
		width: 350px;
		height: 180px;
		margin-left: 20px;
		position: relative;
	}

	.auctionfirm_two_div_rig_o {
		font-size: 18px;
		font-weight: bold;
		color: #333333;
	}

	.auctionfirm_two_div_rig_t {
		font-size: 14px;
		color: #777;
		margin-top: 20px;
		width: 350px;
		display: -webkit-box;
		-js-display: -webkit-box;
		overflow: hidden;
		white-space: normal !important;
		text-overflow: ellipsis;
		word-wrap: break-word;
		-webkit-line-clamp: 3;
		-webkit-box-orient: vertical;
	}

	.auctionfirm_two_div_rig_f {
		width: 350px;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 14px;
		font-weight: 400;
		color: #777777;
		position: absolute;
		left: 0;
		bottom: 5px;
	}
</style>
