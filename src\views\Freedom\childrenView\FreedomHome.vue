<template>
  <div class="bidding">
    <!-- 轮播图部分 -->
    <div class="carousel-container">
      <Carousel :items="carouselItems" :autoplay="true" :interval="5000" />
    </div>

    <!-- 资产处置板块 -->
    <div class="section">
      <div class="section-title">
        <div class="title-container">
          <SvgIcon iconName="freedom-assets-disposal" className="title-icon" />
          <span>资产处置</span>
        </div>
        <!-- <div class="more-container">
          <el-button size="small" plain>
            <SvgIcon iconName="freedom-relase" className="title-icon" />
            <span>发布交易</span>
          </el-button>
        </div> -->
      </div>
      <div class="content-container">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
          <el-skeleton :rows="2" animated />
        </div>

        <!-- 数据列表 -->
        <div v-else class="auction-cards">
          <PropertyCard
            v-for="item in accomplishItems"
            :key="item.productId"
            v-bind="item"
            @click="handleCardClick"
          />
        </div>

        <!-- 分页组件 -->
        <div v-if="!loading && total > 0" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            :page-size="pageSize"
            :total="total"
            layout="total, prev, pager, next, jumper"
            @current-change="handlePageChange"
          />
        </div>

        <!-- 暂无数据 -->
        <div
          v-if="!loading && accomplishItems.length === 0"
          class="empty-state"
        >
          <div class="empty-text">暂无数据</div>
        </div>
      </div>
    </div>
  </div>

  <!-- 自由交易登录信息浮动区域 -->
  <div v-if="freedomUserStore.isFreedomLoggedIn" class="freedom-login-float">
    <!-- 用户头像触发器 -->
    <div class="float-avatar" @click="toggleFloatDropdown">
      <!-- 有头像时显示用户头像 -->
      <img 
        v-if="freedomUserStore.freedomUserInfo?.userInfo.avatar" 
        :src="freedomUserStore.freedomUserInfo.userInfo.avatar" 
        alt="用户头像" 
        class="float-user-avatar-img"
      />
      <!-- 没有头像时显示默认图标 -->
      <SvgIcon 
        v-else 
        iconName="user" 
        className="float-avatar-icon" 
      />
    </div>

    <!-- 用户信息弹出框 -->
    <Teleport to="body">
      <div
        v-show="showFloatDropdown"
        class="freedom-user-dropdown"
        @click.stop
      >
        <!-- 用户信息区域 -->
         <div class="float-user-info-section">
           <div class="float-user-info-avatar">
             <!-- 有头像时显示用户头像 -->
             <img 
               v-if="freedomUserStore.freedomUserInfo?.userInfo.avatar" 
               :src="freedomUserStore.freedomUserInfo.userInfo.avatar" 
               alt="用户头像" 
               class="float-dropdown-avatar-img"
             />
             <!-- 没有头像时显示默认图标 -->
             <SvgIcon 
               v-else 
               iconName="user" 
               className="float-info-avatar-icon" 
             />
           </div>
          <div class="float-user-info-content">
            <span class="float-user-info-name">{{ freedomUserStore.freedomUserInfo?.userInfo.username || '自由交易用户' }}</span>
            <span class="float-user-info-phone">{{ freedomUserStore.freedomUserInfo?.userInfo.phone || '' }}</span>
          </div>
        </div>

        <!-- 菜单列表区域 -->
        <div class="float-menu-list-section">
          <!-- 回收商管理 -->
          <div class="float-menu-item" @click="handleRecyclerManage">
            <SvgIcon iconName="login-menu-account" className="float-menu-icon" />
            <span>回收商管理</span>
          </div>
          <!-- 分割线 -->
          <div class="float-menu-divider"></div>
          <!-- 退出登录 -->
          <div class="float-menu-item float-logout-item" @click="handleFreedomLogout">
            <SvgIcon iconName="login-menu-logout" className="float-menu-icon float-logout-icon" />
            <span>退出登录</span>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage, ElMessageBox } from "element-plus";
import PropertyCard from "@/components/PropertyCard.vue";
import Carousel from "@/components/Carousel.vue";
import SvgIcon from "@/components/SvgIcon.vue";
import type { PropertyItem } from "@/types/property";
import { newApi } from "@/utils/api-new";
import { useFreedomUserStore } from "@/stores/freedomUser";

// 路由实例
const router = useRouter();
const route = useRoute();

// 自由交易用户状态管理
const freedomUserStore = useFreedomUserStore();

// 搜索关键词
const searchKeyword = ref<string>("");

// 分页相关变量
const currentPage = ref<number>(1);
const pageSize = ref<number>(8);
const total = ref<number>(0);
const loading = ref<boolean>(false);

// 定义轮播图数据
const carouselItems = ref([
  {
    image: 'https://huigupaimai.oss-cn-beijing.aliyuncs.com/temp/free-banner_1754963528553.jpg',
    title: "",
    price: "",
    description: "",
  },
  {
    image:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250603/17489456557790.png",
    title: "推广banner图展示位",
  },
  {
    image:
      "https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/image/20250613/17497843321079.png",
    title: "推广banner图展示位",
  },
]);

// 自由交易资产处置数据
const accomplishItems = ref<PropertyItem[]>([]);

// 浮动登录信息下拉框状态管理
const showFloatDropdown = ref(false);

/**
 * 获取自由交易区列表数据
 * @param page 页码，默认为当前页
 */
const getTradeZoneList = async (page: number = currentPage.value) => {
  try {
    loading.value = true;
    const response = await newApi.getTradeZonePageList({
      pageNo: page,
      pageSize: pageSize.value,
      queryHgySupplyDemandDto: {
        infoTitle: searchKeyword.value,
      },
    });

    if (response.success || response.code === 200) {
      // 根据实际返回的数据结构进行调整
      const data = response.result;

      // 假设返回的数据结构为 { records: [], total: number }
      // 需要根据实际API返回结构调整
      if (data && data.records) {
        // 将API返回的数据转换为PropertyItem格式
        accomplishItems.value = data.records.map((item: any) => ({
          productId: item.id,
          productName: item.infoTitle,
          productImage: item.attachment,
          currentPrice: item.price,
          priceUnit: item.priceUnit || "元",
          viewCount: item.viewNum || 0,
          enterpriseLogo: item.enterpriseLogo || "",
          enterpriseName: item.enterpriseName || item.company,
          enterpriseType: item.enterpriseType || "企业",
          status: item.type,
          statusName: item.type_dictText,
          productCount: item.quantity || 1,
          productCountUnit: item.unit || "批",
          productWeight: item.productWeight || 0,
          productWeightUnit: item.productWeightUnit || "吨",
          productPower: item.viewNum,
          productPowerUnit: item.productPowerUnit,
        }));

        total.value = data.total || 0;
        currentPage.value = page;
      } else {
        accomplishItems.value = [];
        total.value = 0;
      }
    } else {
      ElMessage.error(response.message || response.msg || "获取数据失败");
    }
  } catch (error) {
    console.error("获取自由交易区列表失败:", error);
    ElMessage.error("获取数据失败，请稍后重试");
  } finally {
    loading.value = false;
  }
};

/**
 * 处理卡片点击事件
 * 点击前需要校验自由交易登录状态
 */
const handleCardClick = async (value: {productId: string,productName: string}) => {
  // console.log(`点击了商品ID: ${value.productId}`);
  
  // 检查自由交易登录状态
  if (!freedomUserStore.checkFreedomLogin()) {
    try {
      // 提示用户需要登录
      await ElMessageBox.confirm(
        '访问资产详情需要登录自由交易账号，是否前往登录？',
        '需要登录',
        {
          confirmButtonText: '去登录',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );
      
      // 用户点击确定，跳转到自由交易登录页面
      // 携带当前页面作为重定向地址
      const currentPath = router.currentRoute.value.fullPath;
      router.push({
        name: 'freedomLogin',
        query: {
          redirect: `/propertyDetail?id=${value.productId}&crumbsTitle=自由交易&type=4`
        }
      });
      return;
    } catch {
      // 用户点击取消，不做任何操作
      return;
    }
  }
  
  // 已登录，直接跳转到资产详情页面
  // 根据商品数据确定type值
  const currentItem = accomplishItems.value.find(item => item.productId === value.productId);
  const type = currentItem?.status || '4'; // 默认为4，实际应该根据业务逻辑确定
  
  // 跳转到资产详情页面，携带产品ID和type
  router.push({
    name: "propertyDetail",
    query: { 
      id: value.productId,
      crumbsTitle: '自由交易',
      type: type
    },
  });
};

// 处理列表项点击事件
const handleListItemClick = (productId: string) => {
  // console.log(`点击了列表项ID: ${productId}`);
  // 这里可以添加导航到详情页或其他操作
};

/**
 * 处理分页变化
 * @param page 新的页码
 */
const handlePageChange = (page: number) => {
  currentPage.value = page;
  getTradeZoneList(page);
};

/**
 * 处理搜索功能
 * @param keyword 搜索关键词
 */
const handleSearch = (keyword: string) => {
  // console.log("自由交易页面搜索:", keyword);

  // 重置到第一页并重新加载数据
  currentPage.value = 1;
  getTradeZoneList(1);

  ElMessage.success(`正在搜索: ${keyword}`);

  // TODO: 实现实际的搜索逻辑
  // 可以在getTradeZoneList函数中添加搜索参数
};

/**
 * 切换浮动下拉框显示状态
 */
const toggleFloatDropdown = () => {
  showFloatDropdown.value = !showFloatDropdown.value;
};

/**
 * 关闭浮动下拉框
 */
const closeFloatDropdown = () => {
  showFloatDropdown.value = false;
};

/**
 * 点击外部关闭下拉框
 */
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement;
  const dropdown = document.querySelector('.freedom-user-dropdown');
  const avatar = document.querySelector('.float-avatar');
  
  // 如果点击的不是下拉框内容和头像，则关闭下拉框
  if (dropdown && avatar && 
      !dropdown.contains(target) && 
      !avatar.contains(target)) {
    closeFloatDropdown();
  }
};

/**
 * 处理回收商管理点击事件
 */
const handleRecyclerManage = () => {
  // 关闭下拉框
  closeFloatDropdown();

  // 跳转到回收商管理页面
  router.push({
    name: 'recyclerManage'
  });
};

/**
 * 处理自由交易退出登录
 */
const handleFreedomLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出自由交易登录吗？',
      '退出登录',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 执行退出登录
    await freedomUserStore.freedomLogout();

    // 关闭下拉框
    closeFloatDropdown();

    ElMessage.success('已退出自由交易登录');
  } catch {
    // 用户取消退出，不做任何操作
  }
};

// 监听路由查询参数变化，处理搜索关键词
watch(
  () => route.query.keyword,
  (newKeyword) => {
    if (newKeyword && typeof newKeyword === "string") {
      searchKeyword.value = newKeyword;
      handleSearch(newKeyword);
    }
  },
  { immediate: true }
);

// 组件挂载时初始化数据
onMounted(() => {
  // 初始化自由交易用户状态
  freedomUserStore.initFreedomUserState();
  
  // 初始化加载第一页数据
  getTradeZoneList(1);

  // 检查搜索关键词
  const keyword = route.query.keyword;
  if (keyword && typeof keyword === "string") {
    searchKeyword.value = keyword;
    handleSearch(keyword);
  }
  
  // 添加点击外部关闭下拉框的事件监听
  document.addEventListener('click', handleClickOutside);
});

// 组件卸载时清理事件监听器
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style lang="scss" scoped>
.bidding {
  margin: 0 auto;
  max-width: 1280px;
  margin-top: 20px;

  .carousel-container {
    margin-bottom: 20px;
  }

  .section {
    min-height: 500px;
    margin-bottom: 20px;
    background-color: #fff;
    border-radius: 10px;
    padding: 13px 20px 20px 20px;

    .section-title {
      margin-bottom: 6px;
      padding-bottom: 6px;
      border-bottom: 1px solid;
      border-image: linear-gradient(to right, #dddddd, #ffffff) 1;
      display: flex;
      align-items: center;
      .title-container {
        flex: 1;
        font-size: 18px;
        font-family: "PingFang Bold";
        color: #333333;
        display: flex;
        align-items: center;
        .title-icon {
          width: 24px;
          height: 26px;
          margin-right: 5px;
        }
      }
      .more-container {
        flex: 0 0 auto;
        :hover {
          background-color: rgba($color: #004c66, $alpha: 0.2);
        }
        .title-icon {
          width: 13px;
          height: 13px;
          margin-right: 4px;
        }
        .el-button {
          width: 102px;
          height: 30px;
          border-radius: 6px;
          border-color: #004c66;
          color: #004c66;
          :hover {
            background-color: transparent;
          }
        }
      }
    }

    .content-container {
      margin-top: 9px;
    }
  }

  .auction-cards {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
  }

  .auction-list {
    display: flex;
    flex-direction: column;

    .item-divider {
      width: 100%;
      height: 1px;
      background-color: #dddddd;
      margin: 20px 0;
    }
  }

  // 加载状态样式
  .loading-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 20px;
  }

  // 分页组件样式
  .pagination-container {
    display: flex;
    justify-content: end;
    margin-top: 30px;
    padding: 20px 0;
  }

  // 暂无数据样式
  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 500px;
    margin-top: 20px;

    .empty-text {
      font-size: 16px;
      color: #999;
    }
  }
}

/* 自由交易浮动登录信息区域样式 */
.freedom-login-float {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
  
  .float-avatar {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #004C66 0%, #006B8A 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 4px 12px rgba(0, 76, 102, 0.3);
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 76, 102, 0.4);
    }
    
    .float-avatar-icon {
      width: 28px;
      height: 28px;
      color: #ffffff;
    }
    
    /* 浮动头像图片样式 */
    .float-user-avatar-img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      object-position: center;
    }
  }
}

/* 自由交易用户信息下拉框样式 */
.freedom-user-dropdown {
  position: fixed;
  bottom: 100px;
  right: 30px;
  width: 280px;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e5e7eb;
  z-index: 1001;
  overflow: hidden;
  animation: fadeInUp 0.3s ease;
  
  /* 用户信息区域 */
  .float-user-info-section {
    padding: 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    display: flex;
    align-items: center;
    gap: 12px;
    
    .float-user-info-avatar {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, #004C66 0%, #006B8A 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      
      .float-info-avatar-icon {
        width: 24px;
        height: 24px;
        color: #ffffff;
      }
      
      /* 下拉框头像图片样式 */
      .float-dropdown-avatar-img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
        object-position: center;
      }
    }
    
    .float-user-info-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      .float-user-info-name {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
        line-height: 1.2;
      }
      
      .float-user-info-phone {
        font-size: 14px;
        color: #6b7280;
        line-height: 1.2;
      }
    }
  }
  
  /* 菜单列表区域 */
  .float-menu-list-section {
    padding: 8px 20px;
    
    .float-menu-divider {
      height: 1px;
      background: #e5e7eb;
      margin: 8px 0;
    }
    
    .float-menu-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 20px;
      cursor: pointer;
      transition: all 0.2s ease;
      font-size: 14px;
      color: #374151;
      
      &:hover {
        background: #f9fafb;
      }
      
      .float-menu-icon {
        width: 18px;
        height: 18px;
        color: #6b7280;
        flex-shrink: 0;
      }
      
      &.float-logout-item {
        color: #dc2626;
        
        .float-logout-icon {
          color: #dc2626;
        }
        
        &:hover {
          background: #f5f5f5;
        }
      }
    }
  }
}

/* 下拉框动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .freedom-login-float {
    bottom: 20px;
    right: 20px;
    
    .float-avatar {
      width: 50px;
      height: 50px;
      
      .float-avatar-icon {
        width: 24px;
        height: 24px;
      }
    }
  }
  
  .freedom-user-dropdown {
    bottom: 80px;
    right: 20px;
    width: 260px;
  }
}
</style>
