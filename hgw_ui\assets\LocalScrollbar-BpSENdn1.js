import{r as e,x as a,d as l,c as t,s as u,z as n,a as o,b as s,O as v,n as c,P as i,L as r,o as d}from"./vue-vendor-D6tHD5lA.js";import{_ as m}from"./index-Bsdr07Jh.js";function h(){const l=e(null),t=e(!1),u=e(null),n=e(null),o=(e,a)=>{try{const l=JSON.parse(e.data);if(a(l),"chujia"===l.pmh_type||l.bd_status){r("https://huigupaimai.oss-cn-beijing.aliyuncs.com/audio/chujia.mp3")}}catch(l){}},s=(e,a,l,n)=>{t.value=!0,u.value=setInterval(()=>{const t=n?n():{fayanId:0,chujiaiId:0};i({pmh_id:a,bd_id:e,user_id:l,fayan_id:t.fayanId,chujia_id:t.chujiaiId})},1e3)},v=()=>{t.value=!1},c=()=>{t.value=!1,u.value&&(clearInterval(u.value),u.value=null),l.value=null},i=e=>{try{if(l.value&&l.value.readyState===WebSocket.OPEN){const a=JSON.stringify(e);l.value.send(a)}}catch(a){}},r=e=>{try{if(n.value)n.value.play().catch(e=>{});else{new Audio(e).play().catch(e=>{})}}catch(a){}},d=()=>{u.value&&(clearInterval(u.value),u.value=null),l.value&&(l.value.close(),l.value=null),t.value=!1};return a(()=>{d()}),{websock:l,isOnline:t,jishi:u,audioRef:n,initWebSocket:(e,a,t,u,n)=>{try{l.value=new WebSocket("wss://mai-wss.hghello.com/wss"),l.value.onmessage=e=>o(e,u),l.value.onopen=()=>s(e,a,t,n),l.value.onerror=v,l.value.onclose=c}catch(i){}},websocketsend:i,playAudio:r,closeWebSocket:d}}const p={class:"local-scrollbar"},f=m(l({__name:"LocalScrollbar",setup(l,{expose:m}){const h=e(),f=e(),b=e(),y=e(),g=e(0),_=e(0),w=e(0),k=e(!1),x=e(0),L=e(0);let S=null;t(()=>{if(_.value<=w.value)return 0;const e=Math.round(g.value/(_.value-w.value)*100);return Math.min(100,Math.max(0,e))});const M=t(()=>_.value>w.value),j=t(()=>{var e;if(!M.value)return{height:"0px",top:"0px"};const a=(null==(e=b.value)?void 0:e.clientHeight)||0,l=Math.max(20,Math.min(.8*a,w.value/_.value*a)),t=_.value-w.value,u=a-l;return{height:`${l}px`,top:`${t>0?g.value/t*u:0}px`,opacity:k.value?"0.8":"0.6"}}),E=()=>{f.value&&(g.value=f.value.scrollTop,_.value=f.value.scrollHeight,w.value=f.value.clientHeight)};m({refresh:()=>{n(()=>{E()})},updateScrollInfo:E});const I=()=>{k.value||E()},H=e=>{if(!b.value||!y.value||!f.value)return;const a=b.value.getBoundingClientRect(),l=e.clientY-a.top,t=y.value.clientHeight,u=b.value.clientHeight,n=_.value-w.value,o=(l-t/2)/(u-t)*n;f.value.scrollTo({top:Math.max(0,Math.min(n,o)),behavior:"smooth"})},O=e=>{e.preventDefault(),e.stopPropagation(),k.value=!0,x.value=e.clientY,L.value=g.value,document.addEventListener("mousemove",R),document.addEventListener("mouseup",T),document.body.style.userSelect="none"},R=e=>{var a;if(!k.value||!b.value||!f.value)return;const l=e.clientY-x.value,t=b.value.clientHeight,u=(null==(a=y.value)?void 0:a.clientHeight)||0,n=_.value-w.value,o=t-u;if(o>0){const e=l/o*n,a=Math.max(0,Math.min(n,L.value+e));f.value.scrollTop=a,E()}},T=()=>{k.value=!1,document.removeEventListener("mousemove",R),document.removeEventListener("mouseup",T),document.body.style.userSelect=""},W=()=>{E()};return u(async()=>{await n(),E(),window.addEventListener("resize",W,{passive:!0}),f.value&&(S=new MutationObserver(()=>{setTimeout(()=>{E()},10)}),S.observe(f.value,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["style","class"]}))}),a(()=>{window.removeEventListener("resize",W),document.removeEventListener("mousemove",R),document.removeEventListener("mouseup",T),S&&(S.disconnect(),S=null)}),(e,a)=>(d(),o("div",{class:"local-scrollbar-container",ref_key:"containerRef",ref:h},[s("div",{class:"local-scrollbar-content",ref_key:"contentRef",ref:f,onScroll:I},[c(e.$slots,"default",{},void 0,!0)],544),v(s("div",p,[s("div",{class:"scrollbar-track",ref_key:"trackRef",ref:b,onClick:H},[s("div",{class:"scrollbar-thumb",ref_key:"thumbRef",ref:y,style:r(j.value),onMousedown:O},null,36)],512)],512),[[i,M.value]])],512))}}),[["__scopeId","data-v-0f2fa3fc"]]);export{f as L,h as u};
