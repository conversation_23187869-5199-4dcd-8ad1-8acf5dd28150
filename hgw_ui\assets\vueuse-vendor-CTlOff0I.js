import{c as e,p as t}from"./vendor-others-DLVEv83C.js";import{au as n,ag as r,y as o,af as a,Z as i,g as u,s,z as l,r as c,u as d,t as f,c as v}from"./vue-vendor-D6tHD5lA.js";function p(n,r){const o=e({});return t(o)}var m,y=Object.defineProperty,O=Object.defineProperties,b=Object.getOwnPropertyDescriptors,w=Object.getOwnPropertySymbols,g=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable,P=(e,t,n)=>t in e?y(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;function I(e,t){const n=o();var r,u;return a(()=>{n.value=e()},(r=((e,t)=>{for(var n in t||(t={}))g.call(t,n)&&P(e,n,t[n]);if(w)for(var n of w(t))h.call(t,n)&&P(e,n,t[n]);return e})({},t),u={flush:null!=void 0?void 0:"sync"},O(r,b(u)))),i(n)}const E="undefined"!=typeof window,j=()=>{},S=E&&(null==(m=null==window?void 0:window.navigator)?void 0:m.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent);function T(e){return"function"==typeof e?e():d(e)}function A(e,t){return function(...n){return new Promise((r,o)=>{Promise.resolve(e(()=>t.apply(this,n),{fn:t,thisArg:this,args:n})).then(r).catch(o)})}}function x(e){return!!n()&&(r(e),!0)}function N(e,t=200,n={}){return A(function(e,t={}){let n,r,o=j;const a=e=>{clearTimeout(e),o(),o=j};return i=>{const u=T(e),s=T(t.maxWait);return n&&a(n),u<=0||void 0!==s&&s<=0?(r&&(a(r),r=null),Promise.resolve(i())):new Promise((e,l)=>{o=t.rejectOnCancel?l:e,s&&!r&&(r=setTimeout(()=>{n&&a(n),r=null,e(i())},s)),n=setTimeout(()=>{r&&a(r),r=null,e(i())},u)})}}(t,n),e)}function Q(e,t=200,n={}){const r=c(e.value),o=N(()=>{r.value=e.value},t,n);return f(e,()=>o()),r}function C(e,t=200,n=!1,r=!0,o=!1){return A(function(e,t=!0,n=!0,r=!1){let o,a,i=0,u=!0,s=j;const l=()=>{o&&(clearTimeout(o),o=void 0,s(),s=j)};return c=>{const d=T(e),f=Date.now()-i,v=()=>a=c();return l(),d<=0?(i=Date.now(),v()):(f>d&&(n||!u)?(i=Date.now(),v()):t&&(a=new Promise((e,t)=>{s=r?t:e,o=setTimeout(()=>{i=Date.now(),u=!0,e(v()),l()},Math.max(0,d-f))})),n||o||(o=setTimeout(()=>u=!0,d)),u=!1,a)}}(t,n,r,o),e)}function D(e,t,n={}){const{immediate:r=!0}=n,o=c(!1);let a=null;function u(){a&&(clearTimeout(a),a=null)}function s(){o.value=!1,u()}function l(...n){u(),o.value=!0,a=setTimeout(()=>{o.value=!1,a=null,e(...n)},T(t))}return r&&(o.value=!0,E&&l()),x(s),{isPending:i(o),start:l,stop:s}}function k(e){var t;const n=T(e);return null!=(t=null==n?void 0:n.$el)?t:n}const _=E?window:void 0;function B(...e){let t,n,r,o;if("string"==typeof e[0]||Array.isArray(e[0])?([n,r,o]=e,t=_):[t,n,r,o]=e,!t)return j;Array.isArray(n)||(n=[n]),Array.isArray(r)||(r=[r]);const a=[],i=()=>{a.forEach(e=>e()),a.length=0},u=f(()=>[k(t),T(o)],([e,t])=>{i(),e&&a.push(...n.flatMap(n=>r.map(r=>((e,t,n,r)=>(e.addEventListener(t,n,r),()=>e.removeEventListener(t,n,r)))(e,n,r,t))))},{immediate:!0,flush:"post"}),s=()=>{u(),i()};return x(s),s}let L=!1;function M(e,t,n={}){const{window:r=_,ignore:o=[],capture:a=!0,detectIframe:i=!1}=n;if(!r)return;S&&!L&&(L=!0,Array.from(r.document.body.children).forEach(e=>e.addEventListener("click",j)));let u=!0;const s=e=>o.some(t=>{if("string"==typeof t)return Array.from(r.document.querySelectorAll(t)).some(t=>t===e.target||e.composedPath().includes(t));{const n=k(t);return n&&(e.target===n||e.composedPath().includes(n))}}),l=[B(r,"click",n=>{const r=k(e);r&&r!==n.target&&!n.composedPath().includes(r)&&(0===n.detail&&(u=!s(n)),u?t(n):u=!0)},{passive:!0,capture:a}),B(r,"pointerdown",t=>{const n=k(e);n&&(u=!t.composedPath().includes(n)&&!s(t))},{passive:!0}),i&&B(r,"blur",n=>{var o;const a=k(e);"IFRAME"!==(null==(o=r.document.activeElement)?void 0:o.tagName)||(null==a?void 0:a.contains(r.document.activeElement))||t(n)})].filter(Boolean);return()=>l.forEach(e=>e())}function R(e,t=!1){const n=c(),r=()=>n.value=Boolean(e());return r(),function(e,t=!0){u()?s(e):t?e():l(e)}(r,t),n}const $="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},z="__vueuse_ssr_handlers__";function F(e,t,{window:n=_,initialValue:r=""}={}){const o=c(r),a=v(()=>{var e;return k(t)||(null==(e=null==n?void 0:n.document)?void 0:e.documentElement)});return f([a,()=>T(e)],([e,t])=>{var a;if(e&&n){const i=null==(a=n.getComputedStyle(e).getPropertyValue(t))?void 0:a.trim();o.value=i||r}},{immediate:!0}),f(o,t=>{var n;(null==(n=a.value)?void 0:n.style)&&a.value.style.setProperty(T(e),t)}),o}$[z]=$[z]||{};var V=Object.getOwnPropertySymbols,W=Object.prototype.hasOwnProperty,G=Object.prototype.propertyIsEnumerable;function H(e,t,n={}){const r=n,{window:o=_}=r,a=((e,t)=>{var n={};for(var r in e)W.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&V)for(var r of V(e))t.indexOf(r)<0&&G.call(e,r)&&(n[r]=e[r]);return n})(r,["window"]);let i;const u=R(()=>o&&"ResizeObserver"in o),s=()=>{i&&(i.disconnect(),i=void 0)},l=f(()=>k(e),e=>{s(),u.value&&o&&e&&(i=new ResizeObserver(t),i.observe(e,a))},{immediate:!0,flush:"post"}),c=()=>{s(),l()};return x(c),{isSupported:u,stop:c}}var J,U,q=Object.getOwnPropertySymbols,Z=Object.prototype.hasOwnProperty,K=Object.prototype.propertyIsEnumerable;function X(e,t,n={}){const r=n,{window:o=_}=r,a=((e,t)=>{var n={};for(var r in e)Z.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&q)for(var r of q(e))t.indexOf(r)<0&&K.call(e,r)&&(n[r]=e[r]);return n})(r,["window"]);let i;const u=R(()=>o&&"MutationObserver"in o),s=()=>{i&&(i.disconnect(),i=void 0)},l=f(()=>k(e),e=>{s(),u.value&&o&&e&&(i=new MutationObserver(t),i.observe(e,a))},{immediate:!0}),c=()=>{s(),l()};return x(c),{isSupported:u,stop:c}}(U=J||(J={})).UP="UP",U.RIGHT="RIGHT",U.DOWN="DOWN",U.LEFT="LEFT",U.NONE="NONE";var Y=Object.defineProperty,ee=Object.getOwnPropertySymbols,te=Object.prototype.hasOwnProperty,ne=Object.prototype.propertyIsEnumerable,re=(e,t,n)=>t in e?Y(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;function oe(e,t,n,r={}){var o,a,i;const{clone:s=!1,passive:l=!1,eventName:d,deep:p=!1,defaultValue:m}=r,y=u(),O=(null==y?void 0:y.emit)||(null==(o=null==y?void 0:y.$emit)?void 0:o.bind(y))||(null==(i=null==(a=null==y?void 0:y.proxy)?void 0:a.$emit)?void 0:i.bind(null==y?void 0:y.proxy));let b=d;b=d||b||`update:${t.toString()}`;const w=e=>{return s?(e=>"function"==typeof e)(s)?s(e):(t=e,JSON.parse(JSON.stringify(t))):e;var t},g=()=>void 0!==e[t]?w(e[t]):m;if(l){const n=g(),r=c(n);return f(()=>e[t],e=>r.value=w(e)),f(r,n=>{(n!==e[t]||p)&&O(b,n)},{deep:p}),r}return v({get:()=>g(),set(e){O(b,e)}})}((e,t)=>{for(var n in t||(t={}))te.call(t,n)&&re(e,n,t[n]);if(ee)for(var n of ee(t))ne.call(t,n)&&re(e,n,t[n])})({linear:function(e){return e}},{easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]});export{H as a,k as b,I as c,F as d,D as e,C as f,S as g,X as h,E as i,oe as j,p as k,M as o,Q as r,x as t,B as u};
