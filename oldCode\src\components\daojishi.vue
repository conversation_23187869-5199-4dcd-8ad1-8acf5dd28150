<!-- 倒计时封装 -->
<template>
	<div class="djs_yzm">
		{{miao}}秒
	</div>
</template>

<script>
	export default {
		props: ['djss'],
		data() {
			return {
				title: 'Hello',
				miao: 60,
				fhcs: true
			}
		},
		mounted() {
			var _this = this;
			// console.log(_this.djss)
			console.log(_this.djss[0].cs);
			var a = setInterval(function() {
				if (!_this.djss[0].cs) { //倒数非true
					if (_this.miao >= 100) {
						_this.fanh()
						clearInterval(a);
					} else {
						_this.miao = _this.miao + 1;
					}
				} else {
					if (_this.miao <= 0) {
						_this.fanh() //返回参数
						clearInterval(a);
					} else {
						_this.miao = _this.miao - 1;
					}
				}
			}, 1000)
		},
		methods: {
			fanh: function() {
				var _this = this;
				_this.$emit('getdata', _this.fhcs);
			}
		}
	}
</script>

<style>
	.djs_yzm {
		color: #A80012;
	}
</style>
