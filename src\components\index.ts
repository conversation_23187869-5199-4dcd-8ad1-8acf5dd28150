import type { App } from 'vue';
import SvgIcon from './SvgIcon.vue';
import Modal from './Modal.vue';
import Carousel from './Carousel.vue';
import PropertyCard from './PropertyCard.vue';
import ServiceSupport from './ServiceSupport.vue';
import AuctionCard from './AuctionCard.vue';
import AuctionListItem from './AuctionListItem.vue';
import CompanyTag from './CompanyTag.vue';
import UploadCard from './UploadCard.vue';
import CustomScrollbar from './CustomScrollbar.vue';

export default {
  install(app: App) {
    app.component('SvgIcon', SvgIcon);
    app.component('Modal', Modal);
    app.component('Carousel', Carousel);
    app.component('PropertyCard', PropertyCard);
    app.component('ServiceSupport', ServiceSupport);
    app.component('AuctionCard', AuctionCard);
    app.component('AuctionListItem', AuctionListItem);
    app.component('CompanyTag', CompanyTag);
    app.component('UploadCard', UploadCard);
    app.component('CustomScrollbar', CustomScrollbar);
  }
};