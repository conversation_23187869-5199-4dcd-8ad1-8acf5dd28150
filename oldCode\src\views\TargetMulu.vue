<template>
	<div class="home  min_wrapper_1500" style="background-color: #F7F7F7">
		<Headers />
		<div style="min-height: 800px;">
			<div class="TargetMulu_title">
				<div class="TargetMulu_title_one">
					<div>竞价会</div>
					<img style="width: 5px;height: 8px;margin: 0 12px;" src="../assets/right.png" alt="">
					<div>{{paihuiInfo.qiyemingcheng}}</div>
					<img style="width: 5px;height: 8px;margin: 0 12px;" src="../assets/right.png" alt="">
					<div>{{paihuiInfo.pmh_name}}</div>
				</div>
				<div
					class="auction_three_div"
				>
				
				<!-- @click="goDetail(paihuiInfo)" -->
					<div class="auction_three_img">
					<img :src="url + paihuiInfo.pmh_pic" @error="(e)=>e.target.src = paihuiInfo.pmh_pic ? 'https://oss.yunpaiwang.com/'+paihuiInfo.pmh_pic : ''" />
					<div
						v-if="paihuiInfo.pmh_xingshi == 2"
						class="homeCentet_three_left_two_ri_backRight"
						style="background: #316ccb"
					>
						<div>线上竞价</div>
					</div>
					<div
						v-if="paihuiInfo.pmh_xingshi == 1"
						class="homeCentet_three_left_two_ri_backRight"
					>
						<div>同步竞价</div>
					</div>
					</div>
					<div class="auction_three_div_right">
						<div class="auction_three_div_right_one">{{ paihuiInfo.pmh_name }}</div>
						<div class="auction_three_div_right_two">拍卖企业 <span>{{ paihuiInfo.qiyemingcheng }}</span></div>
						<div class="auction_three_div_right_line"></div>
						<div class="auction_three_div_right_four">
							<!-- <div class="biaodiMulu" v-if="isdetail" @click.stop="biaodiInfo(paihuiInfo)">标的目录</div> -->
							<div class="auction_three_div_right_four_cont">
								<img
									src="@/assets/images/index/weiguan.png">
								<div><span>{{paihuiInfo.pmh_weiguan}}</span>次围观</div>
							</div>
							<div
								class="auction_three_div_right_four_cont"
								style="margin-left: 84px"
							>
								<img src="@/assets/images/index/redu.png" />
								<div>
									<span>{{ paihuiInfo.biaodinums }}</span>
									个标的
								</div>
							</div>
						</div>
						<div class="auction_three_div_right_five">
							<div class="auction_three_div_right_five_o">
							<div v-if="paihuiInfo.pmh_status == 2">
								结束时间：{{ paihuiInfo.end_time_name }}
							</div>
							<div v-else>开拍时间：{{ paihuiInfo.start_time_name }}</div>
							</div>
							<div class="auction_three_div_right_five_i">查看详情</div>
						</div>
					</div>
				</div>
				<!-- <auction-card v-for="(item,i) in paimaihuilists" :key="i" :data="item" :isdetail="false"></auction-card> -->
			</div>
			<div class="TargetMulu_titles">
				<div class="TargetMulu_titles_div">
					<div @click="OneIndex = 0"
						:class="OneIndex == 0 ?'TargetMulu_titles_div_list selectView':'TargetMulu_titles_div_list'">
						标的列表
					</div>
					<div @click="OneIndex = 1"
						:class="OneIndex == 1 ?'TargetMulu_titles_div_list selectView':'TargetMulu_titles_div_list'">
						竞价会公告
					</div>
				</div>
			</div>
			<div class="biaodi_three" v-show="OneIndex == 0">
				<target-card v-for="item,i in biaodiList" :key="i" :data="item" class="biaodi_item"></target-card>
			</div>
			<div class="About_one" v-show="OneIndex == 1">
				<div class="About_one_title">{{paihuiInfo.pmh_name}}</div>
				<div class="About_one_title_view" v-html="paihuiInfo.pmh_gonggao"></div>
			</div>
		</div>
		<FootersBottom />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import ajax from '../store/Ajax'
	import TargetCard from '../components/targetCard.vue'
	import AuctionCard from '../components/auctionCard.vue'
	export default {
		components: {
			Headers,
			FootersBottom,
			TargetCard,
			AuctionCard
		},
		data() {
			return {
				paimaihuilists: [],
				url: '',
				biaodiList: [],
				pmh_id: 0,
				paihuiInfo: {},
				OneIndex: 0,
				userInfo:null,
			}
		},
		filters: {
			formatDate: function(value) {
				value = value * 1000
				let date = new Date(value);
				// console.log(date)
				let y = date.getFullYear();
				// console.log(y)
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return MM + '月' + d + '日' + h + ':' + m + ':' + s;
			}
		},
		created() {
			this.url = this.$Pc.ossUrl
			if (this.$route.query.id) {
				this.pmh_id = this.$route.query.id
				this.getPmhInfo()
			}
			if (localStorage.getItem('userInfo')) {
				this.userInfo = JSON.parse(localStorage.getItem('userInfo'))
			}
		},
		methods: {
			getPmhInfo() {
				ajax.paimaihuiinfo({
					id: this.pmh_id
				}).then(res => {
					this.paimaihuilists = [res.data]
					this.biaodiList = res.data.biaodi
					this.paihuiInfo = res.data
				})
			},
			// goDetails(item, isPai) {
			// 	let routeData = this.$router.resolve({ //核心语句
			// 		path: '/auctionDetail/' + item.id, //跳转的路径
			// 		query: { //路由传参时push和query搭配使用 ，作用时传递参数
			// 			id: item.id,
			// 			ispaimaih: isPai ? true : false
			// 		}
			// 	})
			// 	window.open(routeData.href, '_blank');

			// },
		}
	}
</script>

<style lang="scss" scoped>
	.About_one {
		width: 1200px;
		min-height: 800px;
		margin: 20px auto;
		padding: 20px 10px;
		box-sizing: border-box;
		background-color: #FFFFFF;
	}

	.About_one_title {
		text-align: center;
		line-height: 130px;
		font-size: 600;
		font-size: 30px;
	}

	.About_one_title_view {
		width: 90%;
		margin: 0 auto;
	}

	.TargetMulu_title {
		width: 1200px;
		margin: 0 auto;
	}

	.TargetMulu_title_one {
		display: flex;
		-js-display: flex;
		align-items: center;
		font-size: 13px;
		font-weight: 500;
		color: #333333;
		margin-top: 20px;
		margin-bottom: 20px;
	}

	

	.TargetMulu_titles {
		width: 100%;
		height: 66px;
		background: #FFFFFF;
	}

	.TargetMulu_titles_div {
		width: 1200px;
		margin: 0 auto;
		height: 66px;
		display: flex;
		-js-display: flex;
		align-items: center;
		color: #333333;
	}

	.TargetMulu_titles_div_list {
		width: 136px;
		height: 66px;
		text-align: center;
		line-height: 66px;
		font-size: 18px;
		font-weight: bold;
		box-sizing: border-box;
		cursor: pointer;
	}

	.selectView {
		border-bottom: 2px solid #D6363B;
		color: #D6363B;
	}

	/* 标的样式 */
	.biaodi_three {
		width: 1200px;
		margin: 20px auto 0;
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
	}
	.biaodi_item{
		margin-right: 20px;
	}
	.biaodi_item:nth-child(4n){
		margin-right: 0;
	}

	.auction_three_div {
		position: relative;
		display: flex;
		width: 1200px;
		// height: 350px;
		// margin-right: 12px;
		padding: 24px 20px;
		background: #FFFFFF;
		border-radius: 20px;
		margin-bottom: 16px;
		transition: all .2s;
		overflow: hidden;
		cursor: pointer;
		box-sizing: border-box;
	}
	.auction_three_div:hover{
		// background-color: #f8f8f8;
  		box-shadow: 2px 2px 10px 2px rgba(0,0,0,.1);
	}

	.auction_three_img {
		position: relative;
		display: flex;
		align-items: center;
		width: 453px;
		height: 240px;
		border-radius: 10px;
		overflow: hidden;
	}

	.auction_three_img>img {
		width: 100%;
	}
	
	.homeCentet_three_left_two_ri_backRight {
		position: absolute;
		left: 0;
		top: 0;
		width: 106px;
		height: 36px;
		background: #A80012;
		border-radius: 10px 0px 10px 0px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 16px;
		color: #FFFFFF;
		line-height: 36px;
		text-align: center;
	}

	.auction_three_div_right {
		position: relative;
		flex: 1;
		margin-left: 32px;
    	box-sizing: border-box;
	}
	.auction_right_info{
		padding-top: 12px;
		padding-bottom: 24px;
		border-bottom: 1px solid #EDEDED;
	}

	.auction_three_div_right_one {
		margin: 15px 0;
		font-family: PingFangSC, PingFang SC;
		font-weight: 600;
		font-size: 24px;
		color: #343434;
		line-height: 30px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.auction_three_div_right_two {
		margin-top: 18px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 16px;
		color: #666666;
		line-height: 20px;
		span{
			color: #2A2A2A;
		}
	}
	
	.auction_three_div_right_line{
		width: 100%;
		height: 1px;
		margin: 24px 0 0;
		background: #EDEDED;
	}

	.auction_three_div_right_three {
		padding-left: 20px;
		box-sizing: border-box;
		color: #000000;
		font-size: 16px;
		font-weight: 600;
		margin-top: 20px;
	}

	.biaodiMulu {
		width: 86px;
		height: 30px;
		background: #F9664F;
		border-radius: 4px;
		font-size: 14px;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 400;
		color: #FFFFFF;
		text-align: center;
		line-height: 30px;
		cursor: pointer;
	}

	.auction_three_div_right_three span {
		color: #7A7A7A;
		font-weight: 400;
	}

	.auction_three_div_right_four {
		display: flex;
		-js-display: flex;
		align-items: center;
		width: 100%;
		margin-top: 26px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 18px;
		color: #777777;
		line-height: 25px;
		box-sizing: border-box;
	}
	.auction_three_div_right_four_cont{
		display: flex;
		-js-display: flex;
		align-items: center;
		margin-left: 8px;
	}
	.auction_three_div_right_four_cont img{
		width: 20px;
		height: 20px;
		margin-right: 10px;
	}
	.auction_three_div_right_four_cont span{
		font-family: PingFangSC, PingFang SC;
		font-weight: 600;
		font-size: 24px;
		color: #333333;
		line-height: 33px;
	}

	.auction_three_div_right_img {
		width: 128px;
		height: 103px;
		position: absolute;
		right: -40px;
		top: 30%;
		transform: translateX(-50%);
	}

	.auction_three_div_right_five {
		position: relative;
		display: flex;
		-js-display: flex;
		align-items: center;
		width: 100%;
		height: 44px;
		margin-top: 20px;
	}

	.auction_three_div_right_five_o {
		position: absolute;
		z-index: 1;
		left: 0px;
		top: 0;
		width: 380px;
		height: 44px;
		padding-left: 38px;
		background: linear-gradient(90deg, #FB6952 3%, #CC120D 100%);
		border-radius: 0px 22px 22px 0px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 16px;
		color: #FFFFFF;
		line-height: 44px;
		box-sizing: border-box;
	}


	.auction_three_div_right_five_i {
		position: absolute;
		z-index: 0;
		right: 80px;
		top: 0;
		width: 455px;
		height: 44px;
		padding-right: 68px;
		background: linear-gradient(118deg, #F9F9F9 0%, #FFC993 100%);
		border-radius: 0px 22px 22px 0px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 600;
		font-size: 18px;
		color: #CC0319;
		text-align: right;
		line-height: 44px;
		box-sizing: border-box;
	}

</style>
