{"name": "huiguyunpc", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "analyze": "npm run build && node scripts/analyze-bundle.js", "analyze:bundle": "node scripts/analyze-bundle.js", "build:analyze": "cross-env ANALYZE=true npm run build-only", "build:optimized": "node scripts/build-optimized.js", "build:production": "npm run build:optimized", "build:visualize": "npm run build:analyze && npm run analyze:bundle", "build:deploy": "node build-and-fix.js", "build:simple": "vite build", "optimize": "node scripts/quick-optimize.js"}, "dependencies": {"@tiptap/core": "^3.1.0", "@tiptap/pm": "^3.1.0", "@tiptap/starter-kit": "^3.1.0", "@tiptap/vue-3": "^3.1.0", "@vueuse/head": "^2.0.0", "animate.css": "^4.1.1", "axios": "^1.6.8", "element-plus": "^2.6.1", "js-md5": "^0.8.3", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "cross-env": "^10.0.0", "fast-glob": "^3.3.3", "npm-run-all2": "^7.0.2", "postcss-px-to-viewport": "^1.1.1", "rollup-plugin-visualizer": "^6.0.3", "sass": "^1.72.0", "sass-loader": "^14.1.1", "terser": "^5.43.1", "typescript": "~5.8.0", "unplugin-auto-import": "^20.0.0", "unplugin-element-plus": "^0.10.0", "unplugin-vue-components": "^29.0.0", "vite": "^6.2.4", "vite-plugin-prerender": "^1.0.8", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}