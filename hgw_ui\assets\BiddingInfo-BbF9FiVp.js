import{m as e,l as a}from"./element-plus-BiAL0NdQ.js";import{u as t,a as i}from"./vue-router-D0b9rEnV.js";import{d as l,r as s,Q as o,c as n,t as c,s as r,z as v,x as u,a as p,b as d,I as y,F as m,a1 as f,T as b,G as g,K as h,D as k,C as _,E as w,$ as j,o as C,q as x}from"./vue-vendor-D6tHD5lA.js";import{_ as q,S as I,A as $}from"./index-Bsdr07Jh.js";import{a as O}from"./app-assets-OmEvQhWx.js";import{a as T}from"./utils-common-PdkFOSu3.js";import"./element-icons-Cfq32zG_.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./vendor-lodash-D0OfQ6x6.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./app-stores-CLUCXxRF.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./crypto-vendor-CkZKNqwc.js";const N={class:"filter-container"},R={class:"filter-content"},z={class:"labels-section"},D={class:"options-section"},F={class:"province-row-group"},L={class:"filter-options province-options first-row"},M=["onClick"],A={key:0,class:"city-row first-row-cities"},P={class:"filter-options city-options"},Q=["onClick"],B={class:"province-row-group"},E={class:"filter-options province-options second-row"},G=["onClick"],H={key:0,class:"city-row second-row-cities"},K={class:"filter-options city-options"},S=["onClick"],W={class:"province-row-group"},J={class:"filter-options province-options third-row"},U=["onClick"],V={key:0,class:"city-row third-row-cities"},X={class:"filter-options city-options"},Y=["onClick"],Z={class:"option-row"},ee={class:"filter-options status-options"},ae=["onClick"],te={class:"selected-conditions"},ie={class:"tags-container"},le={class:"result-count"},se={class:"count-number"},oe=q(l({__name:"Filter",props:{total:{type:Number,default:0}},emits:["filter-change"],setup(a,{emit:i}){const l=t(),x=a,q=s(),I=s(),$=O,T=o({province:"",city:"",keyword:"",page:1,status:"1,5,6",type:0}),oe=s(""),ne=n(()=>{const e=Math.ceil(be.length/3);return be.slice(0,e)}),ce=n(()=>{const e=Math.ceil(be.length/3);return be.slice(e,2*e)}),re=n(()=>{const e=Math.ceil(be.length/3);return be.slice(2*e)}),ve=n(()=>"first"===oe.value&&T.province),ue=n(()=>"second"===oe.value&&T.province),pe=n(()=>"third"===oe.value&&T.province),de=n(()=>{if(!T.province||"first"!==oe.value)return[];const e=T.province.substring(0,2);return[{label:"不限",value:""},...Object.entries($.area.city_list).filter(([a])=>a.substring(0,2)===e).map(([e,a])=>({label:a.replace(/[省市]$/g,""),value:e}))]}),ye=n(()=>{if(!T.province||"second"!==oe.value)return[];const e=T.province.substring(0,2);return[{label:"不限",value:""},...Object.entries($.area.city_list).filter(([a])=>a.substring(0,2)===e).map(([e,a])=>({label:a.replace(/[省市]$/g,""),value:e}))]}),me=n(()=>{if(!T.province||"third"!==oe.value)return[];const e=T.province.substring(0,2);return[{label:"不限",value:""},...Object.entries($.area.city_list).filter(([a])=>a.substring(0,2)===e).map(([e,a])=>({label:a.replace(/[省市]$/g,""),value:e}))]}),fe=[{label:"不限",value:"1,5,6"},{label:"未开始",value:1},{label:"已成交",value:5},{label:"核实中",value:6}],be=[{label:"不限",value:""},...Object.entries($.area.province_list).map(([e,a])=>({label:a.replace(/[省市]$/g,""),value:e}))];s(70);const ge=()=>{q.value&&I.value&&v(()=>{const e=q.value.offsetHeight;I.value.style.height=`${e}px`})};let he=null;const ke=n(()=>{var e;const a=[];if(T.province){const e=$.area.province_list[T.province];e&&a.push({key:"province",label:e.replace(/[省市]$/g,"")})}if(T.city){const e=$.area.city_list[T.city];e&&a.push({key:"city",label:e.replace(/[省市]$/g,"")})}if(T.status&&"1,5,6"!==T.status){const t=null==(e=fe.find(e=>e.value===T.status))?void 0:e.label;a.push({key:"status",label:t})}return a}),_e=(e,a)=>{T.province=e,oe.value=e?a:"",T.city="",Ce()},we=e=>{T.city=e,Ce()},je=i,Ce=()=>{je("filter-change",{...T,selectedCount:ke.value.length})};return c(()=>T.province,()=>{v(()=>{ge()})}),c(()=>T.province,()=>{v(()=>{ge()})}),c([()=>de.value.length,()=>ye.value.length,()=>me.value.length,oe],()=>{v(()=>{ge()})}),r(()=>{v(()=>{ge(),q.value&&(he=new ResizeObserver(()=>{ge()}),he.observe(q.value));const{name:e,type:a}=l.currentRoute.value.query;if("string"==typeof e&&"string"==typeof a&&e in T&&(T[e]=a),T.province){const e=ne.value.some(e=>e.value===T.province);oe.value=e?"first":"second"}})}),u(()=>{he&&(he.disconnect(),he=null)}),(a,t)=>{const i=e;return C(),p("div",N,[d("div",R,[d("div",z,[d("div",{class:"label-item area",ref_key:"provinceTitleRef",ref:I},"所属省份",512),t[0]||(t[0]=d("div",{class:"label-item"},"标的状态",-1)),t[1]||(t[1]=d("div",{class:"label-item"},"已选条件",-1))]),t[4]||(t[4]=d("div",{class:"main-divider"},null,-1)),d("div",D,[d("div",{class:"option-row province-city-container",ref_key:"provinceOptionsRef",ref:q,style:{"margin-top":"10px"}},[d("div",F,[d("div",L,[(C(!0),p(m,null,f(ne.value,e=>(C(),p("div",{key:e.value,class:k(["filter-option province-option",{active:T.province===e.value}]),onClick:a=>_e(e.value,"first")},h(e.label),11,M))),128))]),y(b,{name:"city-slide"},{default:g(()=>[ve.value&&de.value.length>1?(C(),p("div",A,[d("div",P,[(C(!0),p(m,null,f(de.value,e=>(C(),p("div",{key:e.value,class:k(["filter-option city-option",{active:T.city===e.value}]),onClick:a=>we(e.value)},h(e.label),11,Q))),128))])])):_("",!0)]),_:1})]),d("div",B,[d("div",E,[(C(!0),p(m,null,f(ce.value,e=>(C(),p("div",{key:e.value,class:k(["filter-option province-option",{active:T.province===e.value}]),onClick:a=>_e(e.value,"second")},h(e.label),11,G))),128))]),y(b,{name:"city-slide"},{default:g(()=>[ue.value&&ye.value.length>1?(C(),p("div",H,[d("div",K,[(C(!0),p(m,null,f(ye.value,e=>(C(),p("div",{key:e.value,class:k(["filter-option city-option",{active:T.city===e.value}]),onClick:a=>we(e.value)},h(e.label),11,S))),128))])])):_("",!0)]),_:1})]),d("div",W,[d("div",J,[(C(!0),p(m,null,f(re.value,e=>(C(),p("div",{key:e.value,class:k(["filter-option province-option",{active:T.province===e.value}]),onClick:a=>_e(e.value,"third")},h(e.label),11,U))),128))]),y(b,{name:"city-slide"},{default:g(()=>[pe.value&&me.value.length>1?(C(),p("div",V,[d("div",X,[(C(!0),p(m,null,f(me.value,e=>(C(),p("div",{key:e.value,class:k(["filter-option city-option",{active:T.city===e.value}]),onClick:a=>we(e.value)},h(e.label),11,Y))),128))])])):_("",!0)]),_:1})])],512),d("div",Z,[d("div",ee,[(C(),p(m,null,f(fe,e=>d("div",{key:e.value,class:k(["filter-option",{active:T.status===e.value}]),onClick:a=>{return t=e.value,T.status=t,void Ce();var t}},h(e.label),11,ae)),64))])]),d("div",te,[d("div",ie,[(C(!0),p(m,null,f(ke.value,e=>(C(),w(i,{key:e.key,closable:"",onClose:a=>(e=>{switch(e){case"type":T.type=0;break;case"province":T.province="",oe.value="",T.city="";break;case"city":T.city="";break;case"status":T.status="1,5,6"}Ce()})(e.key),class:"condition-tag"},{default:g(()=>[j(h(e.label),1)]),_:2},1032,["onClose"]))),128))]),d("div",le,[t[2]||(t[2]=d("span",{class:"count-text"},"灰谷网为你筛选了",-1)),d("span",se,h(x.total),1),t[3]||(t[3]=d("span",{class:"count-text"},"条相关结果",-1))])])])])])}}}),[["__scopeId","data-v-8d14c51f"]]),ne={class:"info"},ce={class:"data-list"},re={key:0},ve={class:"auction-cards"},ue={class:"pagination"},pe={key:1,class:"empty"},de={class:"empty-icon"},ye=q(l({__name:"BiddingInfo",setup(e){const l=t(),o=i(),n=s({province:"",city:"",keyword:"",page:1,status:"1,5,6",type:0}),v=s([]),u=s(0),b=async()=>{try{const e=await T.getAuctionList(n.value);if(1==e.code){const a=e.data.data.map(async e=>({id:e.id,pmhId:e.pmh_id,bdName:e.bd_title,startTime:e.start_time_name,endTime:e.end_time_name,bdPic:"https://huigupaimai.oss-cn-beijing.aliyuncs.com/"+e.bd_url,bdQipaijia:e.bd_qipaijia,qpjDanwie:e.qpj_danwie,bdWeiguan:e.bd_weiguan,timeLabel:"截止报名",scheduleLabel:"预计开始",status:e.bd_status})),t=await Promise.all(a);v.value=t,u.value=e.data.total}}catch(e){}};c(()=>o.query.keyword,e=>{e&&"string"==typeof e&&(n.value.keyword=e,n.value.page=1,b())},{immediate:!0}),r(()=>{const e=o.query.keyword;e&&"string"==typeof e&&(n.value.keyword=e),b()});const g=e=>{l.push({name:"auctionDetail",query:{id:e.productId,pmhId:e.pmhId,crumbsTitle:"标的信息"}})},h=e=>{n.value=e,b()},k=e=>{n.value.page=e,b()};return(e,t)=>{const i=a;return C(),p("div",ne,[y(oe,{onFilterChange:h,total:u.value,page:n.value.page},null,8,["total","page"]),t[1]||(t[1]=d("div",{class:"divider"},null,-1)),d("div",ce,[0!==v.value.length?(C(),p("div",re,[d("div",ve,[(C(!0),p(m,null,f(v.value,e=>(C(),w($,x({key:e.id},{ref_for:!0},e,{onClick:g}),null,16))),128))]),d("div",ue,[y(i,{"current-page":n.value.page,total:u.value,"page-size":16,layout:"total, prev, pager, next, jumper",onCurrentChange:k},null,8,["current-page","total"])])])):(C(),p("div",pe,[d("div",de,[y(I,{iconName:"search",className:"empty-search-icon"})]),t[0]||(t[0]=d("div",{class:"empty-text"},"没有找到相关拍品",-1))]))])])}}}),[["__scopeId","data-v-4ce5ed29"]]);export{ye as default};
