<template>
	<div class="min_wrapper_1500 " style="background-color: #FFFFFF;">
		<Headers />
		<div class="About_title">
			<div class="About_title_one">入驻企业注册</div>
			<div class="info">
				<div class="UserInfoCenter_right_fours_two_s">
					<div style="width: 110px;font-size: 14px;"><span>*</span>企业名称：</div>
					<el-input v-model="info.qiyemingcheng" style="width: 310px;" placeholder=""></el-input>
				</div>
				<div class="UserInfoCenter_right_fours_two_s">
					<div style="width: 110px;font-size: 14px;"><span>*</span>联系人：</div>
					<el-input v-model="info.name" style="width: 310px;" placeholder=""></el-input>
				</div>
				<div class="UserInfoCenter_right_fours_two_s">
					<div style="width: 110px;font-size: 14px;"><span>*</span>手机号：</div>
					<el-input v-model="info.mobie" style="width: 310px;" placeholder=""></el-input>
				</div>
				<div class="UserInfoCenter_right_fours_two_s">
					<div style="width: 110px;font-size: 14px;"><span>*</span>验证码：</div>
					<el-input v-model="info.yzcode" style="width: 310px;" placeholder=""></el-input>
					<div class="UserInfoCenter_right_fours_two_s_view" @click="hqyzm" v-if="yz_hd">发送验证码</div>
					<djs class="UserInfoCenter_right_fours_two_s_view" v-if="!yz_hd" :djss="yzm_zj_cs"
						@getdata='js_yzm_fh'>
					</djs>
				</div>
				<div class="infobtn" @click="submit">提交</div>
			</div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import djs from '@/components/daojishi.vue'
	import ajax from '../store/Ajax'
	export default {
		name: 'about',
		components: {
			Headers,
			FootersBottom,
			homeRight,
			djs
		},
		data() {
			return {
				info: {
					qiyemingcheng: '',
					name: '',
					mobie: '',
					yzcode: '',
					qy_status: 0
				},
				weituo: '委托企业',
				yz_hd: true, //验证码切换标签
				yzm_zj_cs: [{
					cs: true
				}], //控制验证码传递
			}
		},
		created() {
			this.info.qy_status = this.$route.query.typeIndex
		},
		methods: {
			js_yzm_fh: function(hd) {
				//验证码回调
				if (hd) {
					this.yz_hd = true;
				} else {
					console.log(hd);
				}
			},
			hqyzm() {
				// 导航轮播图
				if (!this.info.mobie) return this.$message.error('请填写手机号码')
				if (!/^1(3|4|5|7|8|9|6)\d{9}$/i.test(this.info.mobie)) this.$message.error('请输入正确的手机号码')
				ajax.LoginPress({
					mobile: this.info.mobie
				}).then(res => {
					if (res.code == 1) {
						this.yz_hd = false;
						this.$set(this.yzm_zj_cs, 'cs', false);
						this.$message.success(res.msg)
					}else{
						this.$message.error(res.msg)
					}
				}).catch(err => {

				})
			},
			submit() {
				if (!this.info.qiyemingcheng) return this.$message.error('请填写企业名称')
				if (!this.info.name) return this.$message.error('请填写联系人姓名')
				if (!this.info.mobie) return this.$message.error('请填写手机号码')
				if (!this.info.yzcode) return this.$message.error('请填写验证码')

				ajax.qiyeruzhu(this.info).then(res => {
					if(res.code == 0){
						this.$message.error(res.msg)
					}else{
						this.$message.success(res.msg)
					}
				}).catch(err => {

				})
			}
		}
	}
</script>

<style scoped="scoped">
	.About_title {
		min-height: 1000px;
		width: 1200px;
		margin: 0 auto;
		padding-top: 50px;
		box-sizing: border-box;
	}

	.About_title_one {
		font-size: 24px;
		font-weight: bold;
		color: #333333;
	}

	.info {
		width: 450px;
		margin: 97px auto 0;
	}

	.UserInfoCenter_right_fours_two_s {
		width: 450px;
		display: flex;
		-js-display: flex;
		align-items: center;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		margin-bottom: 16px;
		position: relative;
	}

	.UserInfoCenter_right_fours_two_s span {
		color: #A80012;
	}

	.infobtn {
		width: 300px;
		height: 36px;
		background: #A80012;
		border-radius: 2px;
		text-align: center;
		line-height: 36px;
		font-size: 14px;
		font-weight: 400;
		color: #FFFFFF;
		margin-top: 54px;
		margin-left: 110px;
	}

	.UserInfoCenter_right_fours_two_s_view {
		height: 36px;
		width: 100px;
		text-align: center;
		line-height: 36px;
		position: absolute;
		right: 30px;
		font-size: 12px;
		font-weight: 500;
		color: #A80012;
		cursor: pointer;
	}
</style>
