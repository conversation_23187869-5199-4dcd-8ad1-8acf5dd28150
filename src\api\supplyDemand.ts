import { newApi } from '@/utils/api-new'

// 物资类型树节点类型定义
export interface MaterialTypeNode {
  id: string
  parentId: string
  name: string
  code: string
  level: number
  leaf: number
  sort: number
  status: number
  delFlag: number
  children?: MaterialTypeNode[]
}

// 供应需求信息类型定义
export interface SupplyDemandInfo {
  id?: string
  entrustOrderId?: string
  tenantId?: number
  userId?: string
  type?: string // 供求方式 4供应, 5求购
  infoTitle?: string // 信息标题
  highlights?: string // 供应亮点
  materialType?: string // 物资类型
  province?: string // 省份
  city?: string // 城市
  district?: string // 区县
  address?: string // 详细地址
  materialDesc?: string // 物资详细描述
  depreciationDegree?: number // 折旧程度(09-九成新 08-八成新...)
  storageMethod?: number // 存放方式
  quantity?: number // 物资数量
  unit?: string // 物资单位
  price?: number // 物资价格
  brand?: string // 物资品牌
  model?: string // 物资型号
  validDate?: string // 有效期时间
  relationUser?: string // 联系人姓名
  relationPhone?: string // 联系人电话
  status?: number // 状态(1-草稿 2-提交)
  attachmentList?: any[] // 附件列表
}

// 保存供应需求参数类型
export interface SaveSupplyDemandParams {
  hgySupplyDemand: SupplyDemandInfo
  hgyEntrustOrder: {
    relationUser: string
    relationPhone: string
    entrustType: number // 委托类型(1-增值 2-自主 3-供需)
    serviceType: number // 服务类型(1-竞价委托 2-资产处置 3-采购信息 4-供应 5-求购)
    status: number // 状态(1-草稿 2-提交)
  }
}

/**
 * 获取物资类型树形数据
 */
export const getMaterialTree = (params?: any) => {
  return newApi.get('/hgy/material/hgyMaterialType/getMaterialTree', params)
}

/**
 * 保存供应需求信息（新增）
 */
export const saveOrderAndSupplyDemand = (params: SaveSupplyDemandParams) => {
  return newApi.post('/hgy/supplyDemand/hgySupplyDemand/saveOrderAndSupplyDemand', params)
}

/**
 * 更新供应需求信息（修改）
 */
export const updateSupplyDemand = (params: SupplyDemandInfo) => {
  return newApi.put('/hgy/supplyDemand/hgySupplyDemand/updateSupplyDemand', params)
}

/**
 * 根据ID查询供应需求详情
 */
export const querySupplyDemandById = (params: { id: string }) => {
  return newApi.get('/hgy/supplyDemand/hgySupplyDemand/queryById', params)
}

/**
 * 分页查询供应需求列表
 */
export const querySupplyDemandList = (params: any) => {
  return newApi.get('/hgy/supplyDemand/hgySupplyDemand/list', params)
}

/**
 * 删除供应需求
 */
export const deleteSupplyDemand = (params: { id: string }) => {
  return newApi.delete('/hgy/supplyDemand/hgySupplyDemand/delete', params)
}

/**
 * 根据ID查询物资类型
 */
export const queryMaterialTypeById = (params: { id: string }) => {
  return newApi.get('/hgy/material/hgyMaterialType/queryById', params)
}
