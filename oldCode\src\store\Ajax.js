import axiosp from 'axios'
import md5 from 'js-md5';
import qs from 'qs';
import {
	MessageBox,
	Message
  } from 'element-ui'
// axios.defaults.withCredentials = true
// home页面的顶部导航数据
var axios = axiosp.create({
	// baseURL: '/api/',
	// baseURL: 'https://mai-api.hghello.com/'
	baseURL: 'http://39.101.72.34:80'
});
axios.interceptors.response.use(
	function(response) {
		//判断是否有是ie9
		if (isIE9()) {
			//特殊处理response
			if (response.code == 1 && response.request) {
				if (
					response.request.responseType === "json" &&
					response.request.responseText
				) {
					response.data = JSON.parse(response.request.responseText);
					console.log("repsonse", response);
				}
			}
		}
		return response;
	},
	function(error) {
		if (error.response) {
			// 对响应错误做点什么
			console.log("error", error);
			return Promise.reject(error);
		}
	});
// axios.interceptors.response.use(
// 	/**
// 	 * If you want to get http information such as headers or status
// 	 * Please return  response => response
// 	 */
	
// 	/**
// 	 * Determine the request status by custom code
// 	 * Here is just an example
// 	 * You can also judge the status by HTTP Status Code
// 	 */
// 	response => {
// 		const res = response.data
	
// 		// if the custom code is not 20000, it is judged as an error.
// 		if (res.code === 5) {
// 			Message({
// 				message: res.msg || 'Error',
// 				type: 'error',
// 				duration: 5 * 1000
// 			})
// 			setTimeout(() => {
// 				location.reload()
// 			}, 2000)
// 		} else if (res.code === 0) {
// 			Message({
// 				message: res.msg || 'Error',
// 				type: 'error',
// 				duration: 5 * 1000
// 			})
// 			// 50008: Illegal token; 50012: Other clients logged in; 50014: Token expired;
// 			// if (res.code === 50008 || res.code === 50012 || res.code === 50014) {
// 			// 	// to re-login
// 			// 	MessageBox.confirm('You have been logged out, you can cancel to stay on this page, or log in again',
// 			// 	'Confirm logout', {
// 			// 		confirmButtonText: 'Re-Login',
// 			// 		cancelButtonText: 'Cancel',
// 			// 		type: 'warning'
// 			// 	}).then(() => {
// 			// 		store.dispatch('user/resetToken').then(() => {
// 			// 			location.reload()
// 			// 		})
// 			// 	})
// 			// }
// 			return Promise.reject(new Error(res.msg || 'Error'))
// 		} else {
// 			return response
// 		}
// 	},
// 	error => {
// 		console.log('err' + error) // for debug
// 		Message({
// 		message: error.message,
// 		type: 'error',
// 		duration: 5 * 1000
// 		})
// 		return Promise.reject(error)
// 	}
// 	)
function isIE9() {
	if (
		navigator.appName == "Microsoft Internet Explorer" &&
		parseInt(
			navigator.appVersion
			.split(";")[1]
			.replace(/[ ]/g, "")
			.replace("MSIE", "")
		) <= 9
	) {
		return true;
	}
	return false;
}
// 获取当前时间戳参数
function getTime() {
	return ((new Date().getTime() * 0.001).toFixed(0))
}
// 生成md5加密token
function getToken() {
	let time = getTime()
	return md5(md5(`${time}`) + 'huiguwegfg')
}
// 获取顶部参数
function getParams(isSlect) {
	let time = getTime()
	let token = getToken()

	let apptokens = localStorage.getItem('apptoken') || '';
	let params = {}
	if (apptokens !== 'undefined') {
		if (isSlect) {
			params = {
				token: token,
				timestamp: time,
				newban:399,
				apptoken: apptokens
			}
		} else {
			params = {
				token: token,
				type: 1,
				newban:399,
				timestamp: time,
				apptoken: apptokens
			}
		}
	} else {
		if (isSlect) {
			params = {
				token: token,
				newban:399,
				timestamp: time
			}
		} else {
			params = {
				token: token,
				newban:399,
				type: 1,
				timestamp: time
			}
		}
	}
	return params
}
// 获取首页数据
function HomeBanner() {
	return new Promise((resolve, reject) => {
		axios.post(`ypwapi/ptindex/bannerlist`, getParams())
			.then(res => {
				// console.log(res)
				resolve(res.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// home页面的竞价会条数
function Homepmhnum(id) {
	return new Promise((resole, reject) => {
		axios.post("ypwapi/ptindex/pmh_num", getParams())
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// home页面近期竞价会
function HomeJinqipaimaihui(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			day: data.day,
			...tepm
		}
		axios.post("ypwapi/ptindex/jinqipaimaihui", getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// home成交案例
function HomeDaycjbiaodio() {
	return new Promise((resole, reject) => {
		axios.post("ypwapi/ptindex/daycjbiaodi", getParams())
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// home今日拍卖标的
function daybiaodi() {
	return new Promise((resole, reject) => {
		axios.post("ypwapi/ptindex/daybiaodi", getParams())
			.then(data => {
				// console.log(data.data.data)
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 首页竞价会公告
function paimaihui() {
	return new Promise((resole, reject) => {
		axios.post("ypwapi/ptindex/paimaihui", getParams())
			.then(data => {
				// console.log(data.data.data)
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 首页新闻列表展示
function newsindex(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			cateid: data.cateid,
			limit: data.limit,
			...tepm
		}
		axios.post("ypwapi/ptindex/newsindex", getParamss)
			.then(data => {
				// console.log(data.data.data)
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 合作企业
function hzqiyelist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			...data,
			...tepm
		}
		axios.post(`ypwapi/ptindex/hzqiyelist`, getParamss)
			.then(data => {
				// console.log(data.data.data)
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 注册
function register(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			mobile: data.mobile,
			password: data.password,
			yzcode: data.yzcode,
			...tepm
		}
		axios.post(`ypwapi/register`, getParamss)
			.then(data => {
				// console.log(data.data.data)
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 发送验证码
function LoginPress(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			mobile: data.mobile,
			...tepm
		}
		axios.post('setting/alismsnew/sendsms', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 验证码 登录
function Login(data) {
	return new Promise((resole, reject) => {
		// console.log('你好啊')
		let tepm = getParams()
		let getParamss = {
			mobile: data.mobile,
			password: data.password,
			...tepm
		}
		qs.stringify(getParamss)
		axios.post('ypwapi/login', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 验证码登录
function dx_login(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			mobile: data.mobile,
			yzcode: data.yzcode,
			...tepm
		}
		axios.post('ypwapi/login/dx_login', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 标的列表
function biaodilist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			province: data.province,
			city: data.city,
			type: data.type,
			status: data.status,
			starttime: data.starttime,
			keyword: data.keyword,
			page: data.page,
			...tepm
		}
		axios.post('ypwapi/biaodi/biaodilist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 标的参数
function biaodicanshu(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		axios.post('ypwapi/biaodi/biaoditiaojian', tepm)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 竞价会列表
function paimaihuilist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			pmh_xingshi: data.xingshi,
			pmh_status: data.status,
			starttime: data.starttime,
			keyword: data.keyword,
			page: data.page,
			...tepm
		}
		axios.post('ypwapi/paimaihui/paimaihuilist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 拍卖公司列表
function pmqiyeList(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			shouzimu: data.shouzimu,
			keyword: data.keyword,
			page: data.page,
			...tepm
		}
		axios.post('ypwapi/pmqiye/list', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 企业详情
function pmqiyeDetail(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			id: data.id,
			...tepm
		}
		axios.post('ypwapi/pmqiye/info', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 我参与的标列表
function mycybiaodilist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			member_id: data.member_id,
			page: data.page,
			...tepm
		}
		axios.post('ypwapi/member/mycybiaodilist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 我收藏的企业
function myscqiyelist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			member_id: data.member_id,
			page: data.page,
			...tepm
		}
		axios.post('ypwapi/member/myscqiyelist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 我收藏的标列表
function myscbiaodilist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			member_id: data.member_id,
			page: data.page,
			...tepm
		}
		axios.post('ypwapi/member/myscbiaodilist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 我竞的的标列表
function myjdbiaodilist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			member_id: data.member_id,
			page: data.page,
			...tepm
		}
		axios.post('ypwapi/member/myjdbiaodilist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}


// 我报名的标列表
function mybmbiaodilist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			member_id: data.member_id,
			page: data.page,
			...tepm
		}
		axios.post('ypwapi/member/mybmbiaodilist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 保存头像
function saveheader(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			member_id: data.member_id,
			avatar: data.avatar,
			...tepm
		}
		axios.post('ypwapi/member/saveheader', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 获取oss签名信息
function aliosspolicy(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		axios.post('setting/alioss/aliosspolicy', tepm)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 标的收藏，取消收藏
function scbiaodi(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			member_id: data.member_id,
			bd_id: data.bd_id,
			isquxiao: data.isquxiao,
			...tepm
		}
		axios.post('ypwapi/member/scbiaodi', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 企业收藏，取消收藏
function scqiye(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			member_id: data.member_id,
			qiye_id: data.qiye_id,
			isquxiao: data.isquxiao,
			...tepm
		}
		axios.post('ypwapi/member/scqiye', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 企业,个人认证
function rzupload(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/member/rzupload', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 标的详情
function biaodiinfo(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/biaodi/biaodiinfo', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 竞价会详情
function xq_bdlist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/biaodi/xq_bdlist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 未审的标的详情
function weishen_biaodiinfo(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/biaodi/weishen_biaodiinfo', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 未审的竞价会详情
function weishen_xq_bdlist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/biaodi/weishen_xq_bdlist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 出价记录
function chujiaList(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/chujia/list', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 发言记录
function fayanList(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/fayan/list', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 报名标的
function baoming_bd(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/biaodi/baoming_bd', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 看货
function kanhuo_bd(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/biaodi/kanhuo_bd', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 确认上传缴纳证明
function zhengming_bd(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/biaodi/zhengming_bd', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 出价
function chujia_bd(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/biaodi/chujia_bd', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 新闻列表
function newslist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/ptindex/newslist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 新闻详情
function newsinfo(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/ptindex/newsinfo', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 企业竞价会列表
function pmhlist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/pmqiye/pmhlist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 企业标的列表
function bdlist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/pmqiye/bdlist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 企业拍卖公告列表
function pmhgglist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/pmqiye/pmhgglist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 报名信息
function bmverify(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/member/bmverify', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 竞价会信息
function paimaihuibd(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/paimaihui/paimaihuibd', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 标的收藏，取消
function biaodiscqiye(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/member/scbiaodi', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 标的是否收藏
function biaodizybdsc(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/biaodi/zybdsc', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 企业入驻
function qiyeruzhu(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams()
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/ruzhu/qiyeruzhu', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 忘记密码
function zhaohuipwd(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/member/zhaohuipwd', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 修改密码
function xiugaimima(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/member/up_pass', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 竞价会公告
function pmhinfo(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/ptindex/pmhinfo', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 竞价会公告列表
function pmhlistInfo(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/ptindex/pmhlist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 企业是否收藏
function zyqyscIsClick(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/pmqiye/zyqysc', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 认证信息
function getRenzheng(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/member/rzinfo', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}


// 是否认证
function isRenzheng(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/member/isrz', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 减价出价
function jianjiabd(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('/ypwapi/biaodi/jianjia_bd', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 减价出价的竞价列表
function jianjialist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('/ypwapi/chujia/jianjialist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 一次性出价
function yicixingChujia(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('/ypwapi/biaodi/yicixing_chujia', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 一次性出价记录
function yicixingChujialist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('/ypwapi/chujia/yicixing_chujialist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 混拍竞价排名
function hunJialist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('/ypwapi/biaodi/hun_jialist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 竞价会详情
function paimaihuiinfo(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('/ypwapi/paimaihui/paimaihuiinfo', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 物资租赁分类
function zulinfenlei(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwweituo/zulinceshi/zulinfenlei', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 物资租赁分类
function zulinfenleinew(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwweituo/zulinceshi/zulinfenleinew', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 物资租赁列表
function zulinlist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwweituo/zulinceshi/zulinlist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 物资租赁详情
function zulininfo(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwweituo/zulinceshi/zulininfo', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 钢价行情
function gangjiainfo(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwweituo/gangjia/gangjiainfo', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 物资设备租售成交案例
function anlilist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwweituo/zulinceshi/chengjiao', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 跳转物资设备租售两个页面请求
function touser(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/ziguan/touser', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}

// 公告列表（销售和招标）
function zhaobiaolist(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/ptindex/zhaobiaolist', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
// 公告详情（销售和招标）
function zhaobiaoinfo(data) {
	return new Promise((resole, reject) => {
		let tepm = getParams(1)
		let getParamss = {
			...data,
			...tepm
		}
		axios.post('ypwapi/ptindex/zhaobiaoinfo', getParamss)
			.then(data => {
				resole(data.data)
			})
			.catch(err => {
				reject(err)
			})
	})
}
export default {
	zhaobiaolist,
	zhaobiaoinfo,
	HomeBanner,
	Homepmhnum,
	HomeJinqipaimaihui,
	HomeDaycjbiaodio,
	daybiaodi,
	paimaihui,
	newsindex,
	hzqiyelist,
	register,
	LoginPress,
	Login,
	dx_login,
	biaodilist,
	paimaihuilist,
	pmqiyeList,
	pmqiyeDetail,
	mycybiaodilist,
	myscqiyelist,
	myscbiaodilist,
	myjdbiaodilist,
	mybmbiaodilist,
	aliosspolicy,
	biaodicanshu,
	saveheader,
	scbiaodi,
	scqiye,
	rzupload,
	biaodiinfo,
	xq_bdlist,
	weishen_biaodiinfo,
	weishen_xq_bdlist,
	chujiaList,
	fayanList,
	baoming_bd,
	kanhuo_bd,
	zhengming_bd,
	chujia_bd,
	newslist,
	newsinfo,
	pmhlist,
	bdlist,
	pmhgglist,
	bmverify,
	paimaihuibd,
	biaodiscqiye,
	biaodizybdsc,
	qiyeruzhu,
	zhaohuipwd,
	xiugaimima,
	pmhinfo,
	pmhlistInfo,
	zyqyscIsClick,
	getRenzheng,
	isRenzheng,
	jianjiabd,
	jianjialist,
	yicixingChujia,
	yicixingChujialist,
	hunJialist,
	paimaihuiinfo,
	zulinfenlei,
	zulininfo,
	zulinlist,
	zulinfenleinew,
	gangjiainfo,
	anlilist,
	touser
}
