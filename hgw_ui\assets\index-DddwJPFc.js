import"./element-plus-BiAL0NdQ.js";import{d as s,a as o,b as t,I as r,a7 as e,o as i}from"./vue-vendor-D6tHD5lA.js";import{_ as p}from"./index-Bsdr07Jh.js";import"./element-icons-Cfq32zG_.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./vendor-lodash-D0OfQ6x6.js";import"./app-assets-OmEvQhWx.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./vue-router-D0b9rEnV.js";import"./utils-common-PdkFOSu3.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./crypto-vendor-CkZKNqwc.js";import"./app-stores-CLUCXxRF.js";const a={class:"bidding"},m={class:"bidding-main"},n=p(s({__name:"index",setup:s=>(s,p)=>{const n=e("router-view");return i(),o("div",a,[t("main",m,[r(n)])])}}),[["__scopeId","data-v-fd9bf6ea"]]);export{n as default};
