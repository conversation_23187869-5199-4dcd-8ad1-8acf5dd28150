<template>
	<div style="background-color: #F7F7F7;" class="min_wrapper_1500 ">
		<Headers @getKeyWord='inputKeyword' />
		<div style="background-color: #F7F7F7;padding-top: 20px;box-sizing: border-box;">
			<div class="auction">
				<div class="auction_title">
					<div class="auction_title_div">
						<div class="auction_title_div_left" style="position: relative;top: 8px;">所属省份</div>
						<div class="auction_title_div_right" style="padding: 20px 0;box-sizing: border-box;">
							<div v-for="item,i in areaList" @click="setAreaIndex(i)" :key="i"
								:class="i==areaIndex?'auction_title_div_rights':'auction_title_div_rightss'">
								{{item.name}}
							</div>
						</div>
					</div>
					<div class="auction_title_div" v-if="areaIndex !=0">
						<div class="auction_title_div_left" style="position: relative;top: 8px;">所属城市</div>
						<div class="auction_title_div_right" style="padding: 25px 0;box-sizing: border-box;">
							<div v-for="item,i in cityList[areaIndex - 1].city" @click="setCityIndex(i)" :key="i"
								:class="i==cityIndex?'auction_title_div_rights':'auction_title_div_rightss'">
								{{item.name}}
							</div>
						</div>
					</div>
					<div class="auction_title_div">
						<div class="auction_title_div_left">标的类型</div>
						<div class="auction_title_div_right" style="padding-top: 10px;box-sizing: border-box;">
							<div v-for="(item,i) in type" @click="settypeIndex(i)" :key="i"
								:class="i==typeIndex?'auction_title_div_rights':'auction_title_div_rightss'">
								{{item.text}}
							</div>
						</div>
					</div>
					<!-- 	<div class="auction_title_div">
						<div class="auction_title_div_left">评估价格</div>
						<div class="auction_title_div_right">
							<div v-for="item,i in 3"
								:class="i==0?'auction_title_div_rights':'auction_title_div_rightss'">不限
							</div>
						</div>
					</div> -->
					<div class="auction_title_div">
						<div class="auction_title_div_left">标的状态</div>
						<div class="auction_title_div_right" style="padding-top: 10px;box-sizing: border-box;">
							<div v-for="(item,i) in status" @click="setstatusIndex(i)" :key="i"
								:class="i==statusIndex?'auction_title_div_rights':'auction_title_div_rightss'">
								{{item.text}}
							</div>
						</div>
					</div>
					<div class="auction_title_div">
						<div class="auction_title_div_left">开拍时间</div>
						<div class="auction_title_div_right" style="padding-top: 10px;box-sizing: border-box;">
							<div v-for="(item,i) in starttime" @click="setstarttimeIndex(i)" :key="i"
								:class="i==starttimeIndex?'auction_title_div_rights':'auction_title_div_rightss'">
								{{item.text}}
							</div>
						</div>
					</div>
					<div class="auction_title_divs">
						<div class="auction_title_divs_left">
							<div class="auction_title_div_left" style="padding-top: 5px;box-sizing: border-box;">已选条件
							</div>
							<div class="auction_title_divs_left_right">
								<div @click="setAreaIndex(0)" class="auction_title_divs_left_rights"
									v-if="areaIndex !=0">
									所选省份：{{areaList[areaIndex].name}} X</div>
								<div @click="setCityIndex(0)" class="auction_title_divs_left_rights"
									v-if="cityIndex != 0 && areaIndex !=0">
									所选城市：{{cityList[areaIndex-1].city[cityIndex].name}} X</div>
								<div @click="settypeIndex(0)" class="auction_title_divs_left_rights"
									v-if="typeIndex !=0">
									标的类型：{{type[typeIndex].text}} X</div>
								<div @click="setstatusIndex(0)" class="auction_title_divs_left_rights"
									v-if="statusIndex !=0">
									标的状态：{{status[statusIndex].text}} X</div>
								<div @click="setstarttimeIndex(0)" class="auction_title_divs_left_rights"
									v-if="starttimeIndex !=0">
									开拍时间：{{starttime[starttimeIndex].text}} X</div>
								<div @click="removeKeyword" class="auction_title_divs_left_rights" v-if="info.keyword">
									关键词：{{info.keyword}} X</div>
							</div>
						</div>
						<div class="auction_title_divs_right">灰谷网为你筛选到 <span>{{total}}</span> 条相关结果</div>
					</div>
				</div>
				<!-- <div class="auction_two">
					<img @click="goEnter" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/images/settle1.png">
					<img @click="goEnter" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/images/settle2.png">
				</div> -->
				<div class="biaodi_three">
					<target-card v-for="item,i in biaodiList" :data="item" :key="i" class="biaodi_item"></target-card>
				</div>
				<div class="auctionpage">
					<el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
						:current-page='info.page' :page-size="per_page" layout="prev, pager, next, jumper"
						:total="total">
					</el-pagination>
				</div>
			</div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import area from '../assets/js/area.js'
	import ajax from '../store/Ajax'
	import TargetCard from '../components/targetCard.vue'
	export default {
		name: 'Biaodi',
		components: {
			Headers,
			FootersBottom,
			homeRight,
			TargetCard
		},
		data() {
			return {
				areaList: [],
				// url: '',
				info: {
					province: 0,
					city: 0,
					type: 0,
					status: 0,
					starttime: 0,
					keyword: '',
					page: 1
				},
				starttime: [],
				starttimeIndex: 0,
				status: [],
				statusIndex: 0,
				type: [],
				typeIndex: 0,
				biaodiList: [],
				total: 0,
				areaIndex: 0,
				cityList: [],
				cityIndex: 0,
				per_page: 0,
				userInfo: null,
			}
		},
		filters: {
			formatDate: function(value) {
				value = value * 1000
				let date = new Date(value);
				// console.log(date)
				let y = date.getFullYear();
				// console.log(y)
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return MM + '月' + d + '日' + h + ':' + m + ':' + s;
			}
		},
		created() {
			let arae = area.area.province_list
			for (var key in arae) {
				this.areaList.push({
					id: key,
					name: arae[key]
				})
			}
			this.areaList.unshift({
				id: 0,
				name: '不限'
			})

			if (this.$route.query) {
				if (this.$route.query.typeIndex) {
					this.typeIndex = this.$route.query.typeIndex
					this.info.type = this.typeIndex
				}
				if (this.$route.query.areaIndex) {
					// this.setAreaIndex(this.$route.query.areaIndex)
					this.areaIndex = this.$route.query.areaIndex
					this.info.province = this.areaList[this.areaIndex].id
				}
				if (this.$route.query.bd_status) {
					this.status = this.$route.query.bd_status
					this.info.status = this.status
					this.statusIndex = this.status					
				}
				if (this.$route.query.password) {
					this.inputKeyword(this.$route.query.password)
				}
			}
			if (localStorage.getItem('userInfo')) {
				this.userInfo = JSON.parse(localStorage.getItem('userInfo'))
			}
			// this.url = this.$Pc.ossUrl
			// 标的参数
			ajax.biaodicanshu(this.info).then(res => {
				this.type = res.data.bd_type
				this.starttime = res.data.starttime
				this.status = res.data.zhuangtai
			})
			// 标的列表
			ajax.biaodilist(this.info).then(res => {
				this.biaodiList = res.data.data
				this.total = res.data.total
				this.per_page = res.data.per_page
			})

			let city = area.area.city_list
			let temp = []
			for (var key in city) {
				temp.push({
					id: key,
					name: city[key]
				})
			}
			temp.forEach((el, index) => {
				if (this.cityList.length == 0) {
					let idTitle = el.id.substr(0, 2)
					this.cityList.push({
						city: [{
							id: idTitle,
							name: '不限'
						}, el]
					})
				} else {
					let idTitle = el.id.substr(0, 2)
					let id = this.cityList[this.cityList.length - 1].city[0].id
					let cityId = id.substr(0, 2)
					if (idTitle != cityId) {
						this.cityList.push({
							city: [{
								id: idTitle,
								name: '不限'
							}, el]
						})
					} else {
						let objects = this.cityList[this.cityList.length - 1]
						objects.city.push(el)
					}
				}
			})
		},
		methods: {
			goDetails(item) {
				let routeData = this.$router.resolve({ //核心语句
					path: '/auctionDetail/' + item.id, //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: item.id,
						ispaimaih: false
					}
				})
				window.open(routeData.href, '_blank');

			},
			setCityIndex(i) {
				this.cityIndex = i
				if (this.cityIndex == 0) {
					this.info.city = 0
				} else {
					this.info.city = this.cityList[this.areaIndex - 1].city[this.cityIndex].id
				}
				this.info.page = 1
				this.getTarget()
			},
			setAreaIndex(i) {
				this.areaIndex = i
				this.cityIndex = 0
				this.info.province = this.areaList[this.areaIndex].id
				this.info.city = 0
				this.info.page = 1
				this.getTarget()
			},
			settypeIndex(i) {
				this.typeIndex = i
				this.info.page = 1
				this.info.type = i
				this.getTarget()
			},
			setstatusIndex(i) {
				this.statusIndex = i
				this.info.page = 1
				this.info.status = this.status[i].value * 1
				this.getTarget()
			},
			setstarttimeIndex(i) {
				this.starttimeIndex = i
				this.info.page = 1
				this.info.starttime = i
				this.getTarget()
			},
			handleSizeChange(index) {
				this.info.page = index
				this.getTarget()
			},
			handleCurrentChange(index) {
				this.info.page = index
				this.getTarget()
			},
			goEnter() {
				this.$router.push({
					path: '/enter'
				})
			},
			getTarget() {
				ajax.biaodilist(this.info).then(res => {
					this.biaodiList = res.data.data
					this.total = res.data.total
					this.per_page = res.data.per_page
				})
			},
			inputKeyword(keyword) {
				this.info.keyword = keyword
				this.info.page = 1
				this.getTarget()
			},
			removeKeyword() {
				this.info.keyword = ''
				this.info.page = 1
				this.getTarget()
			}
		}

	}
</script>

<style type="text/css" scoped="scoped">
	.auction {
		width: 1200px;
		min-height: 1000px;
		padding-bottom: 50px;
		box-sizing: border-box;
		margin: 0 auto;
	}

	.auction_title {
		min-height: 252px;
		background: #FFFFFF;
		border: 1px solid #EEEEEE;
		margin-top: 20px;
	}

	.auction_title_div {
		min-height: 58px;
		border-bottom: 1px solid #EEEEEE;
		display: flex;
		-js-display: flex;
		/* align-items: center; */
		padding: 0 15px;
		box-sizing: border-box;
	}

	.auction_title_div_left {
		width: 66px;
		height: 58px;
		line-height: 58px;
		flex-shrink: 0;
	}

	.auction_title_divs {
		width: 100%;
		min-height: 75px;
		display: flex;
		-js-display: flex;
		justify-content: space-between;
		padding: 0 15px;
		box-sizing: border-box;
	}

	.auction_title_div_right {
		min-height: 58px;
		width: 1036px;
		display: flex;
		-js-display: flex;
		flex-wrap: wrap;
		align-items: center;
		flex-shrink: 0;
		margin-left: 28px;
	}

	.auction_title_div_right div {
		min-width: 70px;
		height: 30px;
		text-align: center;
		line-height: 32px;
		font-size: 14px;
		font-weight: 400;
		color: #5A5A5A;
		cursor: pointer;
		margin-bottom: 10px;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.auction_title_div_rights {
		min-width: 70px;
		height: 30px;
		background: #A80012;
		line-height: 30px !important;
		color: white !important;
		margin-bottom: 10px;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.auction_title_div_rightss:hover {
		min-width: 70px;
		height: 30px;
		background: #A80012;
		line-height: 30px !important;
		color: white !important;
		margin-bottom: 10px;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.auction_title_divs_left {
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auction_title_divs_right {
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		height: 75px;
		line-height: 75px;
		transform: translateY(0) !important;
	}

	.auction_title_divs_right span {
		color: #A80012;
	}

	.auction_title_divs_left_right {
		width: 680px;
		display: flex;
		min-height: 75px;
		-js-display: flex;
		align-items: flex-start;
		flex-wrap: wrap;
		margin-left: 25px;
		padding-top: 30px;
		box-sizing: border-box;
	}

	.auction_title_divs_left_rights {
		min-width: 168px;
		height: 26px;
		background: #FFFFFF;
		border: 1px solid #A80012;
		border-radius: 2px;
		font-size: 14px;
		font-weight: 400;
		color: #A80012;
		text-align: center;
		line-height: 26px;
		margin-right: 15px;
		margin-bottom: 10px;
		cursor: pointer;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.auction_two {
		-js-display: flex;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 20px;
	}

	.auction_two img {
		display: block;
		width: 590px;
		/* height: 84px; */
	}

	.auctionpage {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
	}

	/deep/.el-pagination.is-background .el-pager li:not(.disabled).active {
		background-color: #A80012;
		color: white;
	}

	.biaodi_three {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 20px;
	}
	.biaodi_item{
		margin-right: 20px;
	}
	.biaodi_item:nth-child(4n){
		margin-right: 0;
	}
</style>
