# 页面布局说明

本项目中的发布页面样式都是一致的，可以借鉴D:\Works\code\jeecgboot\jeecgboot-vue3\src\views\entrust\appreciationEntrust，该组件是增值委托组件，发布供应的样式和该组件完全相同，不要擅自做出更改，当然，发布供应页面不需要根据服务类型显示不同的字段了，改页面中也不需要选择服务类型

## 页面中的板块与字段

顶部是步骤条，总共分为两部：第一步编辑供应信息，第二步发布供应信息

### 板块：

#### 第一步中的板块：

最上方是供应物资类型板块，该板块中有一个级联选择器，没有label级联选择器中的数据是通过接口获取的，接口地址为：/hgy/material/hgyMaterialType/getMaterialTree级联选择器中的数据结构为： [ id string ID parentId string 父ID name string 物资名称 code string 物资编码 level number leaf number sort number status number delFlag number children array ] 需要有搜索功能

接下来是基本供应信息板块该板块第一行是信息标题，占满整行，第二行是物资品牌，物资型号，新旧程度（1-9对应一成新到九成新）

下面是规格与存放板块该板块只有一行：省市区选择框，该选择框样式保持和增值委托中的一致，不需要label，省市区分开三个框，后面是存放方式（选择框），物资数量，物资价格

下面是亮点与展示板块该板块有三行物资照片（建议尺寸800\*800像素，最多上传15张）物资视频（建议视频宽高比16:9，突出商品核心卖点，时长9~30秒）供应亮点详细描述（富文本）

#### 第二步中的板块

信息有效性板块时间信息选择框联系人信息板块联系姓名输入框，联系电话输入框

## 字段

{ /\* */ hgyEntrustOrder?: { /*服务单ID,需要规则生成 \*/ id?: string;

    /*租户ID */
    tenantId?: number;

    /*用户ID */
    userId?: string;

    /*委托企业ID */
    entrustCompanyId?: number;

    /*委托企业名称 */
    entrustCompanyName?: string;

    /*受委托企业ID */
    onEntrustCompanyId?: number;

    /*受委托企业名称 */
    onEntrustCompanyName?: string;

    /*委托类型(1-增值 2-自主) */
    entrustType?: number;

    /*服务类型(1-竞价委托 2-资产处置 3-采购信息) */
    serviceType?: number;

    /*状态(1-草稿 2-待审核 3-审核通过 4-审核拒绝 5-已发布 6-已成交 7-已撤拍) */
    status?: number;

    /*联系人 */
    relationUser?: string;

    /*联系电话 */
    relationPhone?: string;

    /*审核意见 */
    auditOpinion?: string;

    /*审核时间 */
    auditTime?: Record<string, unknown>;

    /*审核人 */
    auditUser?: string;

    /*创建时间 */
    createTime?: Record<string, unknown>;

    /*更新时间 */
    updateTime?: Record<string, unknown>;

    /*删除状态 */
    delFlag?: number;

    /*创建人 */
    createBy?: string;

    /*更新人 */
    updateBy?: string;

};

/\* */ hgySupplyDemand?: { /*id \*/ id?: string;

    /*委托单ID */
    entrustOrderId?: string;

    /*租户ID */
    tenantId?: number;

    /*用户ID */
    userId?: string;

    /*供求方式 4供应, 5求购 */
    type?: string;

    /*信息标题 */
    infoTitle?: string;

    /*供应亮点 */
    highlights?: string;

    /*物资类型 */
    materialType?: string;

    /*省份 */
    province?: string;

    /*城市 */
    city?: string;

    /*区县 */
    district?: string;

    /*详细地址 */
    address?: string;

    /*物资详细描述 */
    materialDesc?: string;

    /*折旧程度(09-九成新 08-八成新...) */
    depreciationDegree?: number;

    /*存放方式，如：仓库、露天堆放、集装箱等 */
    storageMethod?: number;

    /*物资数量 */
    quantity?: number;

    /*物资单位 */
    unit?: string;

    /*物资价格 */
    price?: number;

    /*物资品牌 */
    brand?: string;

    /*物资型号 */
    model?: string;

    /*服务付费方式 1买方支付；2卖方支付 */
    servicePayType?: string;

    /*信息有效期 */
    validType?: string;

    /*信息有效期 */
    validDate?: string;

    /*联系人 */
    relationUser?: string;

    /*联系电话 */
    relationPhone?: string;

    /*围观数量 */
    viewNum?: number;

    /*状态(1-草稿 2-待审核 3-审核通过 4-审核拒绝 5-已发布 6-已成交 7-已撤拍 8 已过期) */
    status?: number;

    /*删除状态 */
    delFlag?: number;

    /*创建人 */
    createBy?: string;

    /*创建时间 */
    createTime?: string;

    /*更新人 */
    updateBy?: string;

    /*更新时间 */
    updateTime?: string;

    /*附件信息 */
    attachmentList?: {
      /*附件ID */
      id?: string;

      /*租户ID */
      tenantId?: number;

      /*用户ID */
      userId?: string;

      /*业务类型 */
      bizType?: string;

      /*业务ID */
      bizId?: string;

      /*文件名称 */
      fileName?: string;

      /*文件路径 */
      filePath?: string;

      /*文件大小(字节) */
      fileSize?: number;

      /*文件类型 */
      fileType?: string;

      /*上传时间 */
      createTime?: string;

      /*删除状态 */
      delFlag?: number;

      /*创建人 */
      createBy?: string;

      /*更新人 */
      updateBy?: string;

      /*更新时间 */
      updateTime?: string;
    }[];

}; }

字段信息是从接口文档中直接复制的，不是所有的都需要使用，根据页面中的字段来选择即可

# 接口说明

添加供求信息的接口为：/hgy/supplyDemand/hgySupplyDemand/saveOrderAndSupplyDemand，post请求现在我们实现的是供应页面，type应该传4

附件和图片的数据上传时的结构仔细看一下增值委托中的处理方式，样式也仔细查看，不要实现出来不一样

## 修改相关的接口

修改的情况下提交接口是/hgy/supplyDemand/hgySupplyDemand/updateSupplyDemand，put请求但是需要在请求参数中添加id字段，表示修改的是哪一条记录

修改的情况下需要通过id查询数据并回显，查询数据的接口为/hgy/supplyDemand/hgySupplyDemand/queryById，get请求，需要传递id
