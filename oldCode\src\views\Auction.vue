<template>
	<div class="min_wrapper_1500" style="background-color: #F7F7F7;min-width: 1500px;">
		<Headers @getKeyWord='inputKeyword' />
		<div style="background-color:#F7F7F7;;padding-top: 20px;box-sizing: border-box;">
			<div class="auction">
				<div class="auction_title">
					<div class="auction_title_div">
						<div class="auction_title_div_left">竞价形式</div>
						<div class="auction_title_div_right">
							<div v-for="(item,i) in xingshi" @click="xingshiIndexClic(i)"
								:class="i==xingshiIndex?'auction_title_div_rights':'auction_title_div_rightss'">{{item}}
							</div>
						</div>
					</div>
					<div class="auction_title_div">
						<div class="auction_title_div_left">竞价状态</div>
						<div class="auction_title_div_right">
							<div v-for="(item,i) in status" @click="statusIndexClic(i)"
								:class="i==statusIndex?'auction_title_div_rights':'auction_title_div_rightss'">{{item}}
							</div>
						</div>
					</div>
					<div class="auction_title_div">
						<div class="auction_title_div_left">开拍时间</div>
						<div class="auction_title_div_right">
							<div v-for="(item,i) in starttime" @click="starttimeIndexClic(i)"
								:class="i==starttimeIndex?'auction_title_div_rights':'auction_title_div_rightss'">
								{{item}}
							</div>
						</div>
					</div>
					<div class="auction_title_divs">
						<div class="auction_title_divs_left">
							<div class="auction_title_div_left">已选条件</div>
							<div class="auction_title_divs_left_right">
								<div @click="removeKeyword" class="auction_title_divs_left_rights" v-if="info.keyword">
									关键词：{{info.keyword}} X</div>
								<div @click="statusIndexClic(0)" class="auction_title_divs_left_rights"
									v-if="statusIndex !=0">
									竞价状态：{{status[statusIndex]}} X</div>
								<div @click="starttimeIndexClic(0)" class="auction_title_divs_left_rights"
									v-if="starttimeIndex !=0">
									开拍时间：{{starttime[starttimeIndex]}} X</div>
								<div @click="xingshiIndexClic(0)" class="auction_title_divs_left_rights"
									v-if="xingshiIndex !=0">
									竞价形式：{{xingshi[xingshiIndex]}} X</div>
							</div>
						</div>
						<div class="auction_title_divs_right">灰谷网为你筛选到 <span>{{total}}</span> 条相关结果</div>
					</div>
				</div>
				<!-- <div class="auction_two">
					<img @click="goEnter" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/images/settle1.png">
					<img @click="goEnter" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/images/settle2.png">
				</div> -->
				<div class="auction_three">
					<auction-card v-for="(item,i) in paimaihuilists" :key="i" :data="item" class="auction_item"></auction-card>
				</div>
				<div class="auctionpage">
					<el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
						:current-page='info.page' :page-size="per_page" layout="prev, pager, next, jumper"
						:total="total">
					</el-pagination>
				</div>
			</div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import ajax from '../store/Ajax'
	import AuctionCard from '../components/auctionCard.vue'
	export default {
		name: 'Auction',
		components: {
			Headers,
			FootersBottom,
			homeRight,
			AuctionCard
		},
		data() {
			return {
				info: {
					xingshi: 0,
					status: 0,
					starttime: 0,
					keyword: '',
					page: 0,
				},
				starttime: ['不限', '未来3天', '未来7天', '未来15日'],
				starttimeIndex: 0,
				status: ['不限', '未开始', '进行中', '已结束'],
				statusIndex: 0,
				xingshi: ['不限', '同步', '线上'],
				xingshiIndex: 0,
				total: 0,
				url: '',
				paimaihuilists: [],
				per_page: 0
			}
		},
		created() {

			if (this.$route.query) {
				if (this.$route.query.password) {
					this.info.keyword = this.$route.query.password
					// this.inputKeyword(this.$route.query.password)
				}
				if(this.$route.query.ac_status){
					let i = this.$route.query.ac_status
					this.statusIndex = i
					this.info.status = i
					// this.info.page = 0
					// this.statusIndexClic(this.$route.query.ac_status)
				}
			}

			this.url = this.$Pc.ossUrl
			this.getTarget()
			// ajax.paimaihuilist(this.info).then(res => {
			// 	this.paimaihuilists = res.data.data
			// 	this.total = res.data.total
			// 	this.per_page = res.data.per_page
			// })
		},
		methods: {
			goEnter() {
				this.$router.push({
					path: '/enter'
				})
			},
			getTarget() {
				// 导航轮播图
				ajax.paimaihuilist(this.info).then(res => {
					this.paimaihuilists = res.data.data
					this.total = res.data.total
					this.per_page = res.data.per_page
				})
			},
			handleCurrentChange() {
				this.info.page = index - 1
				this.getTarget()
			},
			xingshiIndexClic(i) {
				this.xingshiIndex = i
				this.info.xingshi = i
				this.info.page = 0
				this.getTarget()
			},
			statusIndexClic(i) {
				this.statusIndex = i
				this.info.status = i
				this.info.page = 0
				this.getTarget()
			},
			starttimeIndexClic(i) {
				this.starttimeIndex = i
				this.info.starttime = i
				this.info.page = 0
				this.getTarget()
			},
			handleSizeChange(index) {
				this.info.page = index
				this.getTarget()
			},
			handleCurrentChange(index) {
				this.info.page = index
				this.getTarget()
			},
			inputKeyword(keyword) {
				this.info.keyword = keyword
				this.info.page = 1
				this.getTarget()
			},
			removeKeyword() {
				this.info.keyword = ''
				this.info.page = 1
				this.getTarget()
			}
		}
	}
</script>

<style type="text/css" scoped="scoped">
	.auction {
		width: 1200px;
		min-height: 1000px;
		padding-bottom: 50px;
		box-sizing: border-box;
		margin: 0 auto;
	}

	.auction_title {
		height: 252px;
		background: #FFFFFF;
		border: 1px solid #EEEEEE;
		margin-top: 20px;
	}

	.auction_title_div {
		height: 58px;
		border-bottom: 1px solid #EEEEEE;
		display: flex;
		-js-display: flex;
		align-items: center;
		padding: 0 15px;
		box-sizing: border-box;
	}

	.auction_title_div_left {
		width: 66px;
		height: 58px;
		line-height: 58px;
	}

	.auction_title_divs {
		width: 100%;
		height: 75px;
		display: flex;
		-js-display: flex;
		justify-content: space-between;
		padding: 0 15px;
		box-sizing: border-box;
	}

	.auction_title_div_right {
		height: 58px;
		display: flex;
		-js-display: flex;
		align-items: center;
		margin-left: 28px;
	}

	.auction_title_div_right div {
		width: 70px;
		height: 30px;
		text-align: center;
		line-height: 32px;
		font-size: 14px;
		font-weight: 400;
		color: #5A5A5A;
		cursor: pointer;
	}

	.auction_title_div_rights {
		width: 70px;
		height: 30px;
		background: #A80012;
		line-height: 30px !important;
		color: white !important;
	}

	.auction_title_div_rightss:hover {
		width: 70px;
		height: 30px;
		background: #A80012;
		line-height: 30px !important;
		color: white !important;
	}

	.auction_title_divs_left {
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.auction_title_divs_right {
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		height: 75px;
		line-height: 75px;
		transform: translateY(0) !important;
	}

	.auction_title_divs_right span {
		color: #A80012;
	}

	.auction_title_divs_left_right {
		display: flex;
		height: 75px;
		-js-display: flex;
		align-items: center;
		margin-left: 25px;
	}

	.auction_title_divs_left_rights {
		min-width: 168px;
		height: 26px;
		background: #FFFFFF;
		border: 1px solid #A80012;
		border-radius: 2px;
		font-size: 14px;
		font-weight: 400;
		color: #A80012;
		text-align: center;
		line-height: 26px;
		margin-right: 15px;
		margin-bottom: 10px;
		cursor: pointer;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.auction_two {
		-js-display: flex;
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 20px;
	}

	.auction_two img {
		display: block;
		width: 590px;
		/* height: 84px; */
	}

	.auction_three {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 30px;
	}
	.auction_item{
		margin-right: 12px;
	}
	.auction_item:nth-child(4n){
		margin-right: 0;
	}

	

	.auctionpage {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
	}

	/deep/.el-pagination.is-background .el-pager li:not(.disabled).active {
		background-color: #A80012;
		color: white;
	}
</style>
