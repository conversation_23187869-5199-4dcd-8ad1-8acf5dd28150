





<!DOCTYPE html>
<html lang="en" data-color-mode="auto" data-light-theme="light" data-dark-theme="dark">
  <head>
    <meta charset="utf-8">
  <link rel="dns-prefetch" href="https://github.githubassets.com">
  <link rel="dns-prefetch" href="https://avatars.githubusercontent.com">
  <link rel="dns-prefetch" href="https://github-cloud.s3.amazonaws.com">
  <link rel="dns-prefetch" href="https://user-images.githubusercontent.com/">
  <link rel="preconnect" href="https://github.githubassets.com" crossorigin>
  <link rel="preconnect" href="https://avatars.githubusercontent.com">



  <link crossorigin="anonymous" media="all" integrity="sha512-dkuYFW+ra8yYSt342e5pJEeslPSjMcrMvNxlYZMyM/X+/WJHDPvoCuGq3LFojI7B0dQWwZNRiPMnbi9IfUgTaA==" rel="stylesheet" href="https://github.githubassets.com/assets/light-764b98156fab6bcc984addf8d9ee6924.css" /><link crossorigin="anonymous" media="all" integrity="sha512-UrAu23+eyncWvaQFwsLbgSKtmLb2aH1bcT4hJnnRdkaPuY1eu9bumt33FyHHFDX8hskTUNWNkIsMCz7FWQQHwA==" rel="stylesheet" href="https://github.githubassets.com/assets/dark-52b02edb7f9eca7716bda405c2c2db81.css" /><link data-color-theme="dark_dimmed" crossorigin="anonymous" media="all" integrity="sha512-kyu73YWtU8Fu2e7p+Hv094CRhaTvr8yy95vc1SQ2+MeWVWakGeIh/lv9yIFaYAb8J3oM6uBLGcn1kS6M1GxBCQ==" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_dimmed-932bbbdd85ad53c16ed9eee9f87bf4f7.css" /><link data-color-theme="dark_high_contrast" crossorigin="anonymous" media="all" integrity="sha512-jZSKF7Gx8T/AFthO0CUkWWpG5EBlIZb+tIYu8KgP/kizn7fpXEiXJcB73GTZ69wSVVSZ6Y1Cw286qP7pVZr0gg==" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_high_contrast-8d948a17b1b1f13fc016d84ed0252459.css" /><link data-color-theme="dark_colorblind" crossorigin="anonymous" media="all" integrity="sha512-E02WD8opZPpYu6LM9dlUSIHQgXtLmzi1KxMnaN/SA7k6ILsvpNJjpkBPU1sC98MitAOkCNIe6ozqY8+pHnrHZg==" rel="stylesheet" data-href="https://github.githubassets.com/assets/dark_colorblind-134d960fca2964fa58bba2ccf5d95448.css" /><link data-color-theme="light_colorblind" crossorigin="anonymous" media="all" integrity="sha512-VWdBPHZj3WCDwaO0N2W8yvDZt7TNZohRIYK4sjjSU56485rCWazxnLr4p3DU8eqn2+eSj3CYYpw4+DzmwHOwew==" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_colorblind-5567413c7663dd6083c1a3b43765bcca.css" /><link data-color-theme="light_high_contrast" crossorigin="anonymous" media="all" integrity="sha512-dw8LrBQMvo9HDd5lo2UEp/tvMVR6zJjrQkQTBVrhyaHDlL1p7UiQ9/xpqYxOz9s7s1Qh5Bjokuzu7NX0U5BeYA==" rel="stylesheet" data-href="https://github.githubassets.com/assets/light_high_contrast-770f0bac140cbe8f470dde65a36504a7.css" />
  <link crossorigin="anonymous" media="all" integrity="sha512-qnQ/F8LLYoQMGThUwEBSlMxiYHch+S/b9RRB7xQLd2KV0SYwDTKOddCpUyASbG3S9MM13xxngC/E5j0xOhwqSw==" rel="stylesheet" href="https://github.githubassets.com/assets/frameworks-aa743f17c2cb62840c193854c0405294.css" />
  <link crossorigin="anonymous" media="all" integrity="sha512-UClkQCXZUB72OhcRH/0tTtWJpsH0AtA2wYP5hy3F2QoFGJbTSRKa0tDGevxG9D1jfxsDCpvRoboRMbaloQCrDA==" rel="stylesheet" href="https://github.githubassets.com/assets/behaviors-5029644025d9501ef63a17111ffd2d4e.css" />
  <link crossorigin="anonymous" media="all" integrity="sha512-lI46H2564tEkXTyrrH+Hwlin/71MJJMEuhHUMWxvDjpn2UnzYo+PkR2vvPwgr3pEt6EhZI8pZhfatt9dY2ZZhQ==" rel="stylesheet" href="https://github.githubassets.com/assets/tab-size-fix-948e3a1f6e7ae2d1245d3cabac7f87c2.css" />
  
  
  
  <link crossorigin="anonymous" media="all" integrity="sha512-foTM2RT00n9huyeXycaPaAA2OB3VgcdnttfrlXkgcO+w1cWJWKuemtq0btkUF6OZdj2QL+8pnsjo5GYavaorvA==" rel="stylesheet" href="https://github.githubassets.com/assets/github-7e84ccd914f4d27f61bb2797c9c68f68.css" />

    <script crossorigin="anonymous" defer="defer" integrity="sha512-vkftKchtwD8WsIMRv6keDt+WfX8+SAcIsUPCrzBbL2ojw8iJ1qck2x+XFSii9xkMywZvwKOvVkGMdm09IUjmlg==" type="application/javascript" src="https://github.githubassets.com/assets/environment-be47ed29.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-SgFo8ZBR3G08lUHbaKRkXXaJiV20mGz2B1l7DUjAbMcONGjtoqctNm04wc0FCwGkCGZS1DjQjvqvyR7bXIhB9Q==" type="application/javascript" src="https://github.githubassets.com/assets/chunk-frameworks-4a0168f1.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-3ufRKZJe/1/AvITVwGVuNFv3n9rbKlZcpZkfa2IwVFwI37H7AC45jYGBDi0LrlaY0Kdyb6YzEjclzP+lSjJx9Q==" type="application/javascript" src="https://github.githubassets.com/assets/chunk-vendor-dee7d129.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-vgWV8b1FcpGErHwOitpIbvNmtQZwsw55TUraZngdfnHB3P+ZQnxzKD1L//RqzlWMB9sExBRhfOt1rHCxrru/Fg==" type="application/javascript" src="https://github.githubassets.com/assets/github-elements-be0595f1.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-FsLFwcG9qttn9+zLs7l3eGB3jOBkJR/6m4AhfAGDyFzX4psPjBIiWCEKWE4Ds0QrIDkhDK9EbFEZONpZB8iJWw==" type="application/javascript" src="https://github.githubassets.com/assets/behaviors-16c2c5c1.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-NsqOZulahxwyFvnVkvdWUl3tJo8dICSXPZeWtXHW36wdzv+O57ZPdMg48/x3/qVHBKj1ZgiX/1ljY3MEjSiIpw==" type="application/javascript" src="https://github.githubassets.com/assets/notifications-global-36ca8e66.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-bnTiV3ChTOIytbYke2f/akOZjF8qc2ZcMxdRvcKFhcPnrqRggcbDk++jYMo4VmZGICVtKMzH3ZBKxAtU75JocA==" type="application/javascript" data-module-id="./chunk-action-list-element.js" data-src="https://github.githubassets.com/assets/chunk-action-list-element-6e74e257.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-vu177vjMAp7fHx+zkCoAvGVkQPtNX1L2oWm3v4J1k6WbHtUI8iQMc8ovtNOKnN89/npQsJrksRBjWyJauDJuaw==" type="application/javascript" data-module-id="./chunk-advanced.js" data-src="https://github.githubassets.com/assets/chunk-advanced-beed7bee.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-iG8aKaXiAy7ONu7OJuY1pf3FGFzrs4mYYGnUD47a1kXP5URyMlP1RpAFyWTFeyirHY/UoR2mmkkBD40j/9ouPA==" type="application/javascript" data-module-id="./chunk-animate-on-scroll.js" data-src="https://github.githubassets.com/assets/chunk-animate-on-scroll-886f1a29.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-DR/O+c2yX9jstdyxwVDlaM8zRuOACj/aap9ABecyw8toE3a9rXjQgytQlFPQu74JufIDXrD3E1xAOX+ZscWG1g==" type="application/javascript" data-module-id="./chunk-area.js" data-src="https://github.githubassets.com/assets/chunk-area-0d1fcef9.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-lchWLejOHZUQUJA5VcNlSnbRzzpiJVi0Sg1NcZTkhocfF0PDO8LeAkitCPsqUWW6F2hznj3bDp2rouTLXug6/Q==" type="application/javascript" data-module-id="./chunk-array.js" data-src="https://github.githubassets.com/assets/chunk-array-95c8562d.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-3uI1B0EEO9qeMc4sytdIZhShTElkyQ2rTiGJKrMVjdcM1xxmf7IhAlJpcSQUp5CR3SLbJOr3+9nAWs8XErl9Iw==" type="application/javascript" data-module-id="./chunk-async-export.js" data-src="https://github.githubassets.com/assets/chunk-async-export-dee23507.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-owLCfEz1kyCuCC2dJj3PrIlbMLSAatThR4RbMUlvOdTBRzOf7bjbnY+5VhhGyLMO+M9LxaITgDkHkZjQ+Vzvng==" type="application/javascript" data-module-id="./chunk-axis.js" data-src="https://github.githubassets.com/assets/chunk-axis-a302c27c.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-m/O2Z0gPqnm57wbjLsBOvQh6Q2Pe9ZPuJB//JVxnLopUDBm+2oIbe85Dtn7Gxa3vxLD0dnPsyzLnZ1p2rnMsMQ==" type="application/javascript" data-module-id="./chunk-band.js" data-src="https://github.githubassets.com/assets/chunk-band-9bf3b667.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-eGxp1b7dZ1/tJRddGRSK4XEFLlUWaApzGS/WiLLNaH03D5ZEOsf0pozYT2DfAGxsar1XKqw16YJ/Zdqrl3W94g==" type="application/javascript" data-module-id="./chunk-bar-chart.js" data-src="https://github.githubassets.com/assets/chunk-bar-chart-786c69d5.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-1sfVVMUvgk/el5qm4XY7G3N6Ayu9Rob6D3L4SLwpQ7YRagyYcPbOwz0I6pekJeYdm4qCWJ7r5Gjxv7aMFahFyg==" type="application/javascript" data-module-id="./chunk-branch-from-issue-button.js" data-src="https://github.githubassets.com/assets/chunk-branch-from-issue-button-d6c7d554.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-wbBcXgaTyqYZV0cFCjt3iajgggnAS4zuBrCfF3BE/DaV91Q9otJQ3C6swM/VbsQup7M/7CX83ET2oB24CYTxUg==" type="application/javascript" data-module-id="./chunk-business-audit-log-map-element.js" data-src="https://github.githubassets.com/assets/chunk-business-audit-log-map-element-c1b05c5e.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-O8pqrkSL5oxV9UX4NnVr4OP4AoLDtYsLWP+c6kHdig07RpR+E8i5t8MHSew7i4F4aQeO7e0QnS1HG7vdFN+TEA==" type="application/javascript" data-module-id="./chunk-code-frequency-graph-element.js" data-src="https://github.githubassets.com/assets/chunk-code-frequency-graph-element-3bca6aae.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-AExSe8VNj7qShs1fhiZ4hQzo+XTHfxuyVa2kYKY1Y45IrgP2rt9kns3E7ZI/gMD+pFB9fbYVYYchepaIKtvIvA==" type="application/javascript" data-module-id="./chunk-codemirror-linter-util.js" data-src="https://github.githubassets.com/assets/chunk-codemirror-linter-util-004c527b.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-cxSDHgd8tLA0WhqTMj4rKWUOq9Ml8lms1Fe+VXbWdG1dA6xh9fVqmoksU4pEwKUsnPo6t8pkyoT5e0JteiWqdg==" type="application/javascript" data-module-id="./chunk-codemirror.js" data-src="https://github.githubassets.com/assets/chunk-codemirror-7314831e.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-J8jHMgG3jS4CpHyg8r7/4fkGdTqIN02DLHqUXcqIAmRWCEgjN1dBjBocAjsdSxTK9aSuLAcdk+BxOsvXA/XbyA==" type="application/javascript" data-module-id="./chunk-codespaces-policy-form-element.js" data-src="https://github.githubassets.com/assets/chunk-codespaces-policy-form-element-27c8c732.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-xhSAO0KtnFAlRqAK+mg8BPj/J334ccvnCmmjmBQBCgZcsoO9teHJSS6oAn3XOWYFsWPU2JehwG7S3OVEbLwdUg==" type="application/javascript" data-module-id="./chunk-color-modes.js" data-src="https://github.githubassets.com/assets/chunk-color-modes-c614803b.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-v8cHj6fIJrUr2THCh/9KFMq1ynoKuurR4+FC2Ett8SMLXKeZlPKNBdug2YUHDTdReeN3k8V4w5alEdiPSoSaZw==" type="application/javascript" data-module-id="./chunk-column-chart.js" data-src="https://github.githubassets.com/assets/chunk-column-chart-bfc7078f.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-K1eAx1C+s8mvcaY3Th9Cp74vhHKHOAIliDNhx9cvkySU206+oh+r5IdPgLBgvjltYQ0u7rYgTj4WTR7YITykYQ==" type="application/javascript" data-module-id="./chunk-command-palette-item-element.js" data-src="https://github.githubassets.com/assets/chunk-command-palette-item-element-2b5780c7.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-ZH0ltCkNIWq+MQ+ZUp72mxC5MQqYoRqzO2Pz9dj2vnSEtQM9wFeXVotYdX2mCug1xm3Dgm1c00WxlaHQFkb/FA==" type="application/javascript" data-module-id="./chunk-command-palette-page-element.js" data-src="https://github.githubassets.com/assets/chunk-command-palette-page-element-647d25b4.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-v9LXKvRELqsowcou8tLTR6l5xTTGdvCuGAJkJwxGvev1tPItmj5dvqgev9P+49KwCo3AU1VRYJsQbTVaMtDpag==" type="application/javascript" data-module-id="./chunk-command-palette-page-stack-element.js" data-src="https://github.githubassets.com/assets/chunk-command-palette-page-stack-element-bfd2d72a.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-8CPX9EOL6LeIUQfFLN6WCCoXCS/T1rcvsp39+xEPf69tRl0eUMx38hyohAVuemW/tfHzWveB3ZSGTiaPR9mXZA==" type="application/javascript" data-module-id="./chunk-commit-activity-graph-element.js" data-src="https://github.githubassets.com/assets/chunk-commit-activity-graph-element-f023d7f4.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-ma5kPBC7F91AHGLF0bRfHrNwtb44oiBos1RiRrZBreIv3l4ILmpZTRKzvjjxhkMTVjzZpIIRINZ+52qXJlM6hQ==" type="application/javascript" data-module-id="./chunk-community-contributions.js" data-src="https://github.githubassets.com/assets/chunk-community-contributions-99ae643c.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-HDsLJf6gAN+WDFaJneJwmIY82XkZKWqeX7tStBLRh1XM53K8vMV6JZvjq/UQXszaNVWxWcuYtgYTG6ZWo8+QSw==" type="application/javascript" data-module-id="./chunk-confetti.js" data-src="https://github.githubassets.com/assets/chunk-confetti-1c3b0b25.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-wpenTjQMyR7zUKeyNv37Agdwcr3a/c71kpUwVTrExMbL6RFpA/g8aL+I5AbLKuaw4dyVjNfy0DFHBmFqm8Yr+A==" type="application/javascript" data-module-id="./chunk-contributions-spider-graph.js" data-src="https://github.githubassets.com/assets/chunk-contributions-spider-graph-c297a74e.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-GhU0pCon7adgIojIk58o2W46AJZjPLfsbZ+1hRmcjs/IUQTNszjQPWk8XTlLcGQCG4lJhDFyPgkZQTsrGTnfTA==" type="application/javascript" data-module-id="./chunk-contributors-graph-element.js" data-src="https://github.githubassets.com/assets/chunk-contributors-graph-element-1a1534a4.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-AdShwMIaVrtYr+LC9cc7rsC64wQJgoCdDjLJXaD4kVhdmuld7dk748AQueuoomE4OZypdoHSPmxQ56Wge/qtig==" type="application/javascript" data-module-id="./chunk-cookies.js" data-src="https://github.githubassets.com/assets/chunk-cookies-01d4a1c0.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-jitxouuFY6SUcDZV5W3jhadVEIfFBfCQZxfPV3kxNnsWEBzbxMJFp0ccLb7+OlBjSs1zU/MNtuOV6T9Ay7lx4w==" type="application/javascript" data-module-id="./chunk-copy.js" data-src="https://github.githubassets.com/assets/chunk-copy-8e2b71a2.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-miaiZ1xkDsWBUsURHOmeYtbgVKQGnm1octCo/lDXUmPzDyjtubnHULRVw1AK+sttwdwyB0+LOyhIVAWCNSGx+A==" type="application/javascript" data-module-id="./chunk-delayed-loading-element.js" data-src="https://github.githubassets.com/assets/chunk-delayed-loading-element-9a26a267.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-g9egUfzV36Fhfld8PbxfEm/V9ePFqvd9i5DIKra4wJ61M/AnpOOadhUD2CZ//ww84eRnAMJRQRHOYZ2wZRo2IQ==" type="application/javascript" data-module-id="./chunk-dependencies.js" data-src="https://github.githubassets.com/assets/chunk-dependencies-83d7a051.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-wBjQrIEVDfRYbLE8K9/p0RsN2hm2TLsDC7HjtOE4/W/k5xxiTSYFL9tQ/7F2lUBWzEh8cHs0qRbfISK4RM3Ndw==" type="application/javascript" data-module-id="./chunk-discussion-page-views.js" data-src="https://github.githubassets.com/assets/chunk-discussion-page-views-c018d0ac.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-b1Ek52iZsK0ARpkrQWUZAXpXEPZb22/aE3/Jqo5UB31AflgfWkd+n7um9yvoVhbe7BEzN2s/zp0cO9S1VhmspQ==" type="application/javascript" data-module-id="./chunk-discussions-daily-contributors.js" data-src="https://github.githubassets.com/assets/chunk-discussions-daily-contributors-6f5124e7.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-aEx47zNdROJnjHKqN/Jiwo9zCh/5YZV8Z7IKlbKoWCqwwQbHjRhvSlrr5yfTvzQH0pnfIB1FeecWcPqfzIL/fQ==" type="application/javascript" data-module-id="./chunk-discussions-new-contributors.js" data-src="https://github.githubassets.com/assets/chunk-discussions-new-contributors-684c78ef.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-T092OZtYeslxDIimT2TR4IWlO+6B9q6zenOAqRekKME3TgdR1nKinRgV0PTYZ3Y3IITjimtvjqQpN5kW11o0bw==" type="application/javascript" data-module-id="./chunk-drag-drop.js" data-src="https://github.githubassets.com/assets/chunk-drag-drop-4f4f7639.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-EvJ2Fip59DXgARNuwTWgjdVqoCjhXQL73SP9yexijlWStKq92sfbKeGK5R4wIP0QOr39WsnW/Kaw3Wpl1QPfog==" type="application/javascript" data-module-id="./chunk-edit-hook-secret-element.js" data-src="https://github.githubassets.com/assets/chunk-edit-hook-secret-element-12f27616.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-lJhSlHBxDYfafMGsvjfmbReBxHE64RGTSucXtcG3vTpWvu2vlw/heQjiHB+JwYpnWvcXh0Tn9oLlo90LEpUfIA==" type="application/javascript" data-module-id="./chunk-edit.js" data-src="https://github.githubassets.com/assets/chunk-edit-94985294.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-6tEhsGQ/83I2ttMZD5WpT0LS99zL4PWHRQeY1Z+s3o0WkzmBFBzzc+gEMOMlqPg5j1m6Hf5sqV24lio1HnONtQ==" type="application/javascript" data-module-id="./chunk-emoji-picker-element.js" data-src="https://github.githubassets.com/assets/chunk-emoji-picker-element-ead121b0.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-euLLeTFpj2DeM/lF0qLZedbrh2iLKL86jMNj8ydBs0Dr24s0ZxmdYnenUo2UIVD8r3t2DgXaahNQmstHlXpmtA==" type="application/javascript" data-module-id="./chunk-extent.js" data-src="https://github.githubassets.com/assets/chunk-extent-7ae2cb79.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-4ogHszDC1IJgI2qQ92j4jrbWk8/esq5ocXsCwkODOz/s7sg5NXI28EkEQIlXtAaQfjctTKW7Er1sWWCWYU10Wg==" type="application/javascript" data-module-id="./chunk-failbot.js" data-src="https://github.githubassets.com/assets/chunk-failbot-e28807b3.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-sZbLUSqYvu2x/vJWKw5IfN1p9Gmk8aeCAnH0CgQDCG2A+cY3K9UrBE8dl/1/US6ivVhBAxupTaUUH0eJ7XRo/Q==" type="application/javascript" data-module-id="./chunk-feature-callout-element.js" data-src="https://github.githubassets.com/assets/chunk-feature-callout-element-b196cb51.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-5r/m4Q9I8PFeDYmfZIE5Nmi+ua6kmrqzCpKXwr/9uKdNZe3Vcywf3vzVHxc8ggzU8ujHJqGIMTZXF/E2N+tFtg==" type="application/javascript" data-module-id="./chunk-file-filter-element.js" data-src="https://github.githubassets.com/assets/chunk-file-filter-element-e6bfe6e1.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-8mFWnW8tKukTuJ5KV8c3133BhxXt/Mmp0KD3ge9pWKcejZxB8STIx9vOeyM/Nzysu8c5EFjHB1K5FTbRar90Rw==" type="application/javascript" data-module-id="./chunk-file-filter-persistence.js" data-src="https://github.githubassets.com/assets/chunk-file-filter-persistence-f261569d.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-Jtwo3YPYYT0MH/fxgVIBW7VdDRtCfCpJ7RbVpAIbOaXsi2FGGu2CpBhXms6HohAe360TUi8ya8pNh9Prfx4h0w==" type="application/javascript" data-module-id="./chunk-file-tree-element.js" data-src="https://github.githubassets.com/assets/chunk-file-tree-element-26dc28dd.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-HmlTAbYYypu6xIfkc5lZknDVCpWNWBWqotVdG16NPCcFVn1eCQMAjQZMgYtwONmU97MwJ3QBSjlj3RZpBI8QcQ==" type="application/javascript" data-module-id="./chunk-file-tree-toggle-element.js" data-src="https://github.githubassets.com/assets/chunk-file-tree-toggle-element-1e695301.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-m550igp/MI25d8uV92BSkHD3ADNUxD7F/vv6GdUwlBc1ko2wXgw819mcbtVpTFb10Y/rx8jqS4Fq5K5xRfcAcQ==" type="application/javascript" data-module-id="./chunk-filter-input.js" data-src="https://github.githubassets.com/assets/chunk-filter-input-9b9e748a.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-PnU+sfTXdyJ04N6yBgLo5Z2SjobVkjg3KeIMPPFahdKGVHIjIB5qXL0Lyc251ZrlqGoiZy6pFaPeK5zn4MGjKg==" type="application/javascript" data-module-id="./chunk-format-symbol.js" data-src="https://github.githubassets.com/assets/chunk-format-symbol-3e753eb1.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-Lrm8u2MdzXjItcf94UC8RyxQRLxI99xOHX4lSm2/BeP3Qp/fY31KJsB/PK6jPRgAcV6MeGYFepWP8y1q2R4WUg==" type="application/javascript" data-module-id="./chunk-get-repo-element.js" data-src="https://github.githubassets.com/assets/chunk-get-repo-element-2eb9bcbb.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-1YyRxy7P1eKjAsJP+/J5r58i7pUkqQV1iLcEi8BM8mXtvgkCx5oGBTsTCXY7kNxZwdM89wuW0bUqEmT/K2pAmA==" type="application/javascript" data-module-id="./chunk-index.esm.js" data-src="https://github.githubassets.com/assets/chunk-index.esm-d58c91c7.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-l1nMNuiARJ5TD7tYJoxL9CWJpuRIQizQB5Yow6uYdQLjHF+jhlhjIRCOkjlwu2cPdw5IZjD4cR21hGYyX4fQ2w==" type="application/javascript" data-module-id="./chunk-index.js" data-src="https://github.githubassets.com/assets/chunk-index-9759cc36.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-qOUpd16ML/w+JHQ+AgsVZx091bwb/LDsyH4VCFRjcgYSyvOX3C9sKGbPD71wRrLGXuAVmZMhW/rHxciExPYJhg==" type="application/javascript" data-module-id="./chunk-index2.js" data-src="https://github.githubassets.com/assets/chunk-index2-a8e52977.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-QNDWaHxe34aiO2cy7YLEP+JwDSLTn2f6XbiLs57XjWDv+fCEsKSju2pBNnbQ0gW1BNvmnKTdFm3a0Bfks7G8cg==" type="application/javascript" data-module-id="./chunk-index3.js" data-src="https://github.githubassets.com/assets/chunk-index3-40d0d668.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-/lTFJkfTffOvpQ3Fs6eq02kNzMlBwxwonQU6if7W0VbA7+7Kq2zWUUlX8iVH0/oA8DcVVps3tP5lFqBr/6oj6g==" type="application/javascript" data-module-id="./chunk-index4.js" data-src="https://github.githubassets.com/assets/chunk-index4-fe54c526.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-Ntnd0/5ZBGVRYTRI3XtP6vjjlgg5MyYB+mTrkAR16+nHItQyS+EVU5IeydLg7OtU30Xk85euBkFABjLw4BmU1g==" type="application/javascript" data-module-id="./chunk-index5.js" data-src="https://github.githubassets.com/assets/chunk-index5-36d9ddd3.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-MS1Zii7vEXvNGfCzfot3Ay7Rb4jDjjTve/4lJs0ZVeznfQClBiTPhjvURBIH+eaoHprIg+ajqeamiG2/TNkKFQ==" type="application/javascript" data-module-id="./chunk-index6.js" data-src="https://github.githubassets.com/assets/chunk-index6-312d598a.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-PzguLzCZSX4+hasAu7u8YDVWgFv/0OVZooA+vfjBMr3TYfRwT+DCbLq3B08j4ntyYKddfGSKJjfh4r4bVz3YyQ==" type="application/javascript" data-module-id="./chunk-index7.js" data-src="https://github.githubassets.com/assets/chunk-index7-3f382e2f.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-Q8q7N7XzNXhgaWGGb+GJg1j747S64eBnN4i9fBnc2ZC+iaTX4RNv7nP9QyHXBqKHusT2KuXfOGc2hO6STYS38A==" type="application/javascript" data-module-id="./chunk-input-demux.js" data-src="https://github.githubassets.com/assets/chunk-input-demux-43cabb37.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-4ZToQVMOXo8sxNzItJtpJQ/aBNgkzSVfq2YGbUQnbX59DpBemMGOuph6ShQgeRcuJk+x92FKYlhl92AbejD+ww==" type="application/javascript" data-module-id="./chunk-insights-query.js" data-src="https://github.githubassets.com/assets/chunk-insights-query-e194e841.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-Mma9eU389bpaBMusL0AOdyVXlum506TbS103AlnuSSzmGYEPNtSmgFlSPx5S5wxJmj1Wc1NGtCDvs71md1bapQ==" type="application/javascript" data-module-id="./chunk-invitations.js" data-src="https://github.githubassets.com/assets/chunk-invitations-3266bd79.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-XHi7nspAcN9D8ZOLZZlCDDSUaHeZfd7T/fiPAvkkxOigMlG6TzfvftFdy0l9XGRrv0AVSQVQkKKKExJXPVzopg==" type="application/javascript" data-module-id="./chunk-jump-to.js" data-src="https://github.githubassets.com/assets/chunk-jump-to-5c78bb9e.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-8Zr3D4tATIN9RgbnUQPSG6ynrNzIHEQBEC2X9FWxfXk4tbP4gp+ey/Qin4S/7Ey38XRxRg16Ut2fhSHkH1zA1g==" type="application/javascript" data-module-id="./chunk-launch-code-element.js" data-src="https://github.githubassets.com/assets/chunk-launch-code-element-f19af70f.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-92jpdKelNx62F7UBT82SokGtEavcA+2aKd7tNcefzagcDmCnjTk/8ffmZ24eOPBi62VW4CDooFMXLenxxWercA==" type="application/javascript" data-module-id="./chunk-line-chart.js" data-src="https://github.githubassets.com/assets/chunk-line-chart-f768e974.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-GHMEiXdvG00Lv7Tnj+S7/TnqlcyVi41j6xapftzfnYoGWz5iYCMK5XCoDFmCmSgliaA0pmVHO356D2u3zJCicw==" type="application/javascript" data-module-id="./chunk-line.js" data-src="https://github.githubassets.com/assets/chunk-line-18730489.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-SErVGzFh5d4RY684E3TP0k4oymFEXft5zG2STX8WWBVsYLU1CzKWKooMuhIGsL77HG7pyviD12Dy3zs0GN41bg==" type="application/javascript" data-module-id="./chunk-linear.js" data-src="https://github.githubassets.com/assets/chunk-linear-484ad51b.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-YUCb5GVAF8qPEnN/WtyP9j81k8qOlMzl35cLqVFLixJgBMqQ+ZT+ELoBUx2OTqs5IrYwqq9c+gk4O0qRN3h0EQ==" type="application/javascript" data-module-id="./chunk-locale.js" data-src="https://github.githubassets.com/assets/chunk-locale-61409be4.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-z5CnRrYFTqXt/cLpxeZbeOJFhX9E0mmje1Cf2cNNuGf8Wiu8zKxbOgH5o1gb2FL7PpCBcq4NWx+OR0J7h0eTjw==" type="application/javascript" data-module-id="./chunk-map.js" data-src="https://github.githubassets.com/assets/chunk-map-cf90a746.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-w1zvHEr+ukXz942T2Wj7EwtnDLb0XudEbM1dfe/v6Ln77HA3BEArg9Dj+fiDaG6Z3MSXCeY8wHAd2z59d69OiA==" type="application/javascript" data-module-id="./chunk-marketplace-insights-graph-element.js" data-src="https://github.githubassets.com/assets/chunk-marketplace-insights-graph-element-c35cef1c.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-mrSgB49LKpHqm3TdOykgNjMFS4UUA1rVAVRP5S0OdfZWUCyZh4//xMBZ0F9WHGUkxzibMdM7cO4vxmtsQ9+49A==" type="application/javascript" data-module-id="./chunk-memex-project-picker-element.js" data-src="https://github.githubassets.com/assets/chunk-memex-project-picker-element-9ab4a007.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-cvjyIYhR2ZkuFAXHYZSjPTc5wXYOdISgqbXw69CXpDXdxffXmXuzjCcGJNVk3mDNYsVH4Q9sb2UMNPFrNxxRUQ==" type="application/javascript" data-module-id="./chunk-metric-selection-element.js" data-src="https://github.githubassets.com/assets/chunk-metric-selection-element-72f8f221.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-wpN+s55e0JkuffVR++S87PjAhQog0M/U4+l4pD/Ps8w9yNma6Pdmeij+RTxCSdDzqjgC9knsjPpZ5+ohkRd4ww==" type="application/javascript" data-module-id="./chunk-min.js" data-src="https://github.githubassets.com/assets/chunk-min-c2937eb3.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-aYNntJOZeBzyhsX35l/XAH5STic/eL+BLWcOEitDxJJdLPzhFCMC/uuVZkTr0uEk84PgB6OLeGNVhF1xh28v2w==" type="application/javascript" data-module-id="./chunk-navigation-list-element.js" data-src="https://github.githubassets.com/assets/chunk-navigation-list-element-698367b4.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-7O/+vlRg/HpW7pZPRm6d6d+GOwZORnfka9aIuPq+dEmM7sAyLs26CLTZspQQ9H3KI/TENHVfUdR9Klki0us47w==" type="application/javascript" data-module-id="./chunk-network-graph-element.js" data-src="https://github.githubassets.com/assets/chunk-network-graph-element-eceffebe.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-MYHfk4RC1G7um5oS3Cg8p/6FekxAiWQQvmCR526sy3/Yxbe6WEgDm5D64atvKXEqPps/0VgTxmSUH4XICbv5sw==" type="application/javascript" data-module-id="./chunk-nodrag.js" data-src="https://github.githubassets.com/assets/chunk-nodrag-3181df93.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-uBp/iTTm9wygkEp69MZdyvOA9r1CjfB/B6mvg5icD6kR3YsGNAF3+NXSJA08a6d71+XyhFU5471+c4aGHV42Sw==" type="application/javascript" data-module-id="./chunk-notification-list-focus.js" data-src="https://github.githubassets.com/assets/chunk-notification-list-focus-b81a7f89.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-Q3U6SxBFQ5A32gRRGIreL5uMJHvT7uHHdIHKeKhXcvnOY+4g+HTIklhD3EJIyIrI4cT7wjNlmoZnXSmCiVeveg==" type="application/javascript" data-module-id="./chunk-org-insights-graph-element.js" data-src="https://github.githubassets.com/assets/chunk-org-insights-graph-element-43753a4b.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-UpfXcE8D72JtkgF68l5LopywUMIDLi+ov+G9Kyaiz+zZUQxAXzu3jkIOJ5D0IaK7A54EOHWGpFSsMWnZQ+GsEQ==" type="application/javascript" data-module-id="./chunk-overview.js" data-src="https://github.githubassets.com/assets/chunk-overview-5297d770.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-UNClmPHQl1PshdnfY4UcBJ3IfLJKhyacyGth2ryONYoo7tN2A9b3b5MrqoH1prN83ReSOSizz5ymvXBzHhDEIg==" type="application/javascript" data-module-id="./chunk-package-dependencies-security-graph-element.js" data-src="https://github.githubassets.com/assets/chunk-package-dependencies-security-graph-element-50d0a598.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-rm1qIXRm4iMgRzHKiWNxs89dEXeQEvDZZ7qiYVFDhwbPBgNGl2Thwc/fSe8jjtq9eErkecWSDrFycssBZgoxyw==" type="application/javascript" data-module-id="./chunk-pointer.js" data-src="https://github.githubassets.com/assets/chunk-pointer-ae6d6a21.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-qEMigPltoBeFtlfHd0r2kjylnlwHjhf9+QfoggBvDF5uYSMtRBn1YvtRU7nwpoWRwtH/PQ/eFTNkSNKjQ1mauQ==" type="application/javascript" data-module-id="./chunk-premium-runners.js" data-src="https://github.githubassets.com/assets/chunk-premium-runners-a8432280.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-t4isH071LdL4Tr7566ikb6m29E/n8eaRq6epgtAnR0abJ8kPXm4pymE6OLHgu71UAS5EBUG24OQO80F/Xuifkw==" type="application/javascript" data-module-id="./chunk-premium-sponsors-graph-element.js" data-src="https://github.githubassets.com/assets/chunk-premium-sponsors-graph-element-b788ac1f.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-Fqr0kZtS5m0gCcFK7pEjobxs5uMHolBWDRRM8MtBZC2kmuYFh2vvkKVfhNxl2/DfdpfkirfOGVuoK6JNcooEFA==" type="application/javascript" data-module-id="./chunk-presence-avatars.js" data-src="https://github.githubassets.com/assets/chunk-presence-avatars-16aaf491.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-5H5N/3G/20nmVKntphXb9z0H9q3URFDmHSccLhFkMSA8ILAA9mYlRKCWAWoDcl/W437jtGw1tIxjWStfInvidw==" type="application/javascript" data-module-id="./chunk-profile-pins-element.js" data-src="https://github.githubassets.com/assets/chunk-profile-pins-element-e47e4dff.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-vFR+IqThljOLrAWmjhOL/kiQrjgZZg95uPovX0J7kRH5p7Y049LDRZaXLMDijfeqqk71d3MMn9XP5bUcH+lB9w==" type="application/javascript" data-module-id="./chunk-profile.js" data-src="https://github.githubassets.com/assets/chunk-profile-bc547e22.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-wuV9s7/hK5IRrrhWO1zcnPdPsPQRyKsv/nS0Ks9UepMyu3nLLZoX13F1jmuMOXI0khbFEHW5QR9cIr48izoLNA==" type="application/javascript" data-module-id="./chunk-project-picker-element.js" data-src="https://github.githubassets.com/assets/chunk-project-picker-element-c2e57db3.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-Y12rkUUxkt2zbNz77w3YoYzl3qJlIBcLZO3cWbGhd9Y1ev0NmyyuJMWAMPdXfjMACGFhDq9U8z1/PqmXDpYCew==" type="application/javascript" data-module-id="./chunk-pulse-authors-graph-element.js" data-src="https://github.githubassets.com/assets/chunk-pulse-authors-graph-element-635dab91.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-MU7VY0mux9rnjbuPVXGG86/+7UndHVzcSyt3JvD+cxDXvvPzpQA8k/XrZHykVBw+5qUSjjjAnQ5WD/T/q5EJng==" type="application/javascript" data-module-id="./chunk-range.js" data-src="https://github.githubassets.com/assets/chunk-range-314ed563.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-4c1T1xmasSz69dd9MxySBvtZlnV5u3bFXJXlJLlFe57DoQJ/9FHWx8sVKFvrDjDCnmy+oEbB3Zw9FKduYh71XA==" type="application/javascript" data-module-id="./chunk-readme-toc-element.js" data-src="https://github.githubassets.com/assets/chunk-readme-toc-element-e1cd53d7.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-4aLsPyiz8vqmHsO/bT8AF1ucYnON4JsLOfK5rD85xxOWbSpjet1Ljn4lIy+c/Un7AawwEPW7MBiG+5u+JDD2aA==" type="application/javascript" data-module-id="./chunk-ref-selector.js" data-src="https://github.githubassets.com/assets/chunk-ref-selector-e1a2ec3f.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-rylM3uiZ0RoRl560qJv60gb35nFfvE8DRrVVYM3IAJQnjbqiewiw9KwY/3R3bdKp/ER3Knadmvc0xZWgjk8g3w==" type="application/javascript" data-module-id="./chunk-reload-after-polling-element.js" data-src="https://github.githubassets.com/assets/chunk-reload-after-polling-element-af294cde.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-IEmqpNrj7gV5hmoghm0RZSrOwk6vqQD8zdUsbj77iH1ZXVVTUjA8pbYqN3meO+IEkRFq+37fG3Wu/HCPtuAxdw==" type="application/javascript" data-module-id="./chunk-remote-clipboard-copy.js" data-src="https://github.githubassets.com/assets/chunk-remote-clipboard-copy-2049aaa4.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-8ME7nLIXe5Oqqah8+VbsNkORdavh5B6Pddl9RvJGniLt1e85L3nu2N5yWhMaucy+pyqg3jUR0pJ7RI8nlNYDtA==" type="application/javascript" data-module-id="./chunk-remote-content-element.js" data-src="https://github.githubassets.com/assets/chunk-remote-content-element-f0c13b9c.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-lwVBrgolfkJsmzF0Pq7DQWA84scBWfKrbYvsJkld4RhieE7u3hPGQ+jcWN5g4cS8Vl8E6k28tAO86b1bMBiafw==" type="application/javascript" data-module-id="./chunk-responsive-underlinenav.js" data-src="https://github.githubassets.com/assets/chunk-responsive-underlinenav-970541ae.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-OtjCc7UezN3MvWiHOS/5lYkrBdHOAhcFrGD6/zMgTAugw5BSOaRrrux3wJxv9OlHHgxI32nOkesATHdRyucitA==" type="application/javascript" data-module-id="./chunk-runner-groups.js" data-src="https://github.githubassets.com/assets/chunk-runner-groups-3ad8c273.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-YT1sZoBexRNsvw2mnXAtdf87/5j3cL8ji/WS6h9F0mMh0wyUEx3EElel6roRMoI2Zq+bn5d1i8TSCeZJ84a6Rw==" type="application/javascript" data-module-id="./chunk-series-table.js" data-src="https://github.githubassets.com/assets/chunk-series-table-613d6c66.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-0ho0SIKf1TsQpvFNdpkKw57DmpU8LampLRvO67Q0G+6744f/qI8uTtL96F5tOPT56dG40cwoKB8v6kYqyGipvw==" type="application/javascript" data-module-id="./chunk-severity-calculator-element.js" data-src="https://github.githubassets.com/assets/chunk-severity-calculator-element-d21a3448.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-SGxKZJPMoMDPJ4mVhReJ1NPmXxuTuePhizAUPkKphsivzZhVQstruqwewm3oUpP1yONOG8MITAodv/5iKRsEtw==" type="application/javascript" data-module-id="./chunk-sortable-behavior.js" data-src="https://github.githubassets.com/assets/chunk-sortable-behavior-486c4a64.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-roc/0cfaua9wDuXAOgLBYymh7ketXEgdvrXH5cKsDrwrHA8RdLZyyKvncKkwMTtAXvFBYnhOfxrRYJuu5kCyCQ==" type="application/javascript" data-module-id="./chunk-spoofed-commit-warning.js" data-src="https://github.githubassets.com/assets/chunk-spoofed-commit-warning-ae873fd1.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-n9CzyJxFxwIDn7201fJbWS0Pb+zHZ55ZREoB+DPm2fEEcVi6NQy9u74s28zd23c4HZcOQb+hgBHfx/evvTz0gg==" type="application/javascript" data-module-id="./chunk-stacked-area-chart.js" data-src="https://github.githubassets.com/assets/chunk-stacked-area-chart-9fd0b3c8.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-Z5vxYqlrzgKkUgwWnpxHQ7mGgyY4QVo6rX+t+/+TEiFrVv4hH0bIN7cl/YvFJ5FVrLz/LFu7mhSnEWByM5sJXw==" type="application/javascript" data-module-id="./chunk-stacks-input-config-view.js" data-src="https://github.githubassets.com/assets/chunk-stacks-input-config-view-679bf162.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-gmw7obKL/JEHWPp6zWFh+ynbXUFOidj1DN2aPiTDwP8Gair0moVuDmA340LD84A29I3ZPak19CEiumG+oIiseg==" type="application/javascript" data-module-id="./chunk-tag-input.js" data-src="https://github.githubassets.com/assets/chunk-tag-input-826c3ba1.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-0IOfDfOHJW06z0GwRYJjp03TxPkyF3OxjI4KP+rgd565nAqN0uN7QChz1+/v2/ueP2WXmWKOA6BA7byyMw2Lfw==" type="application/javascript" data-module-id="./chunk-three.module.js" data-src="https://github.githubassets.com/assets/chunk-three.module-d0839f0d.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-wbtDXrsp1UlaVwYqy/OhzlQoCcW05Y6X4Yij81VM9EVikXjOi1IFOj94YTV7/4pn8V0fzIC2tXTbqV3H9c215Q==" type="application/javascript" data-module-id="./chunk-time.js" data-src="https://github.githubassets.com/assets/chunk-time-c1bb435e.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-wIbptf1DKOsqdNtk6eFJNttwm/Rpkl3djUJp4AFKbFOJ29W4u19sJtGhVOQEgeCycpaRZKGuluEeSlilsTvV0A==" type="application/javascript" data-module-id="./chunk-tip.js" data-src="https://github.githubassets.com/assets/chunk-tip-c086e9b5.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-4GJz2wyWwjq7P4hyx3qSkjvnTO7RG5cWvnePVXPB+Oji6MBVugAdl7kCTKbpX8+Ae2ONvGJwFzSc9A7m1pqzXw==" type="application/javascript" data-module-id="./chunk-toast.js" data-src="https://github.githubassets.com/assets/chunk-toast-e06273db.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-8NptiBtmq1Tp1REnAZfwNrntCZfLkMrIxiKtYHKWTt4O1tSw8B4yHVHzoe1VL20rznteHQYZS0cGUg038/h8Xw==" type="application/javascript" data-module-id="./chunk-traffic-clones-graph-element.js" data-src="https://github.githubassets.com/assets/chunk-traffic-clones-graph-element-f0da6d88.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-LOqVKLx7QjibeF9lWG50Iy/ctBXf/qI+fwcyhFssQKtd7RtdZKkPr8h4wPNZVv4w68Jr1RTFaC9CzPZMebgAqQ==" type="application/javascript" data-module-id="./chunk-traffic-visitors-graph-element.js" data-src="https://github.githubassets.com/assets/chunk-traffic-visitors-graph-element-2cea9528.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-zSAZt0KaQlTdiPwxtiJWRHdMz6uoW46PkM/zU78IyWQre/829J0AFfNUuHwXBihMmjDnlUF8k1DE3Ecqo4RtFg==" type="application/javascript" data-module-id="./chunk-traffic.js" data-src="https://github.githubassets.com/assets/chunk-traffic-cd2019b7.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-R+tG3SLlxMfqFe/QsCsVkiImlyr9aDIRGs81jLQFG/8IC/WqUcHWrS4KJdn93V23uBIhL1CStpD5WOGltedi9g==" type="application/javascript" data-module-id="./chunk-turbo.es2017-esm.js" data-src="https://github.githubassets.com/assets/chunk-turbo.es2017-esm-47eb46dd.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-x4ou3us3j9DGRirCSdyrM8EftEUvAa5ZJ0ytbiYyODaofJGTH3l8Tx09pgF9dVFf71bVMq1s2sR5HYko8RTnSQ==" type="application/javascript" data-module-id="./chunk-tweetsodium.js" data-src="https://github.githubassets.com/assets/chunk-tweetsodium-c78a2ede.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-ODZJzCJpaOfusrIka5QVZQcPiO9LBGyrrMYjhhJWSLuCN5WbZ5xiEiiOPOKVu71dqygyRdB2TY7AKPA1J5hqdg==" type="application/javascript" data-module-id="./chunk-unveil.js" data-src="https://github.githubassets.com/assets/chunk-unveil-383649cc.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-tboPFFNqWJk3So4NaMf8stl6PdgATilRXIrAd6oa/LLFANkTZKkCFY4QIMWD+GR103+kAr7lz7bWSkeLRArvyQ==" type="application/javascript" data-module-id="./chunk-user-status-submit.js" data-src="https://github.githubassets.com/assets/chunk-user-status-submit-b5ba0f14.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-6yI/t7GcFRajrquWdg5NqQdrvgI4rWRWcuZhNmAHH/T2aw2iB0uAco9nNYoETW3zzJ+tmnC7qqfArrHaYmk1oQ==" type="application/javascript" data-module-id="./chunk-voting.js" data-src="https://github.githubassets.com/assets/chunk-voting-eb223fb7.js"></script>
    <script crossorigin="anonymous" defer="defer" integrity="sha512-q34Q0/s/exmYECvsCRWCxp+22UY2cZK8MPrKv3LjI3bNU5AD3Fzv+JhN0Lgfvd2mgju3llixlQ4z2RvkTIw2oA==" type="application/javascript" data-module-id="./chunk-webgl-warp.js" data-src="https://github.githubassets.com/assets/chunk-webgl-warp-ab7e10d3.js"></script>
  
  <script crossorigin="anonymous" defer="defer" integrity="sha512-CxqLYfz9O4JFnbHtoVR59nYRCcyRPSZIaeeZBRUJptql+rEPoNWcuerP67UO2XIT1hS6Ff7x+6VnZzoJrP4RYQ==" type="application/javascript" src="https://github.githubassets.com/assets/repositories-0b1a8b61.js"></script>
<script crossorigin="anonymous" defer="defer" integrity="sha512-H7fZNdblbS0nyaIZqFRskpUo6Xfd5ZOP1EWnAh2hDKOGMAr5zlGof9/CvfRkeCmQmm48OUy7p4GrraIgxZNdJQ==" type="application/javascript" src="https://github.githubassets.com/assets/diffs-1fb7d935.js"></script>

  <meta name="viewport" content="width=device-width">
  
  <title>Vue-archive/blink.gif at master · Rkatsiteli/Vue-archive · GitHub</title>
    <meta name="description" content="Vue 汇总. Contribute to Rkatsiteli/Vue-archive development by creating an account on GitHub.">
    <link rel="search" type="application/opensearchdescription+xml" href="/opensearch.xml" title="GitHub">
  <link rel="fluid-icon" href="https://github.com/fluidicon.png" title="GitHub">
  <meta property="fb:app_id" content="****************">
  <meta name="apple-itunes-app" content="app-id=**********" />
    <meta name="twitter:image:src" content="https://opengraph.githubassets.com/cf4fb651e30ba7ad8b1ac607073062a3d4bb4ece8a4cf18068e8f9f3b7552198/Rkatsiteli/Vue-archive" /><meta name="twitter:site" content="@github" /><meta name="twitter:card" content="summary_large_image" /><meta name="twitter:title" content="Vue-archive/blink.gif at master · Rkatsiteli/Vue-archive" /><meta name="twitter:description" content="Vue 汇总. Contribute to Rkatsiteli/Vue-archive development by creating an account on GitHub." />
    <meta property="og:image" content="https://opengraph.githubassets.com/cf4fb651e30ba7ad8b1ac607073062a3d4bb4ece8a4cf18068e8f9f3b7552198/Rkatsiteli/Vue-archive" /><meta property="og:image:alt" content="Vue 汇总. Contribute to Rkatsiteli/Vue-archive development by creating an account on GitHub." /><meta property="og:image:width" content="1200" /><meta property="og:image:height" content="600" /><meta property="og:site_name" content="GitHub" /><meta property="og:type" content="object" /><meta property="og:title" content="Vue-archive/blink.gif at master · Rkatsiteli/Vue-archive" /><meta property="og:url" content="https://github.com/Rkatsiteli/Vue-archive" /><meta property="og:description" content="Vue 汇总. Contribute to Rkatsiteli/Vue-archive development by creating an account on GitHub." />
    



    

  <link rel="assets" href="https://github.githubassets.com/">
  

  <meta name="request-id" content="C827:1523:39A63:10BE82:6216D59A" data-pjax-transient="true"/><meta name="html-safe-nonce" content="fe6eda91537cd47b801e61d094df8fb1740b4e3cfbaa479bb8e1823d73e2f075" data-pjax-transient="true"/><meta name="visitor-payload" content="eyJyZWZlcnJlciI6Imh0dHBzOi8vZ2l0aHViLmNvbS9Sa2F0c2l0ZWxpL1Z1ZS1hcmNoaXZlL3RyZWUvbWFzdGVyLzYlMjAlRTQlQkQlOEQlRTYlOTUlQjAlRTUlQUYlODYlRTclQTAlODElRTglQkUlOTMlRTUlODUlQTUlRTYlQTElODYlRUYlQkMlODglRTQlQkIlQkYlRTclODUlQTclRTYlOTQlQUYlRTQlQkIlOTglRTUlQUUlOUQlRUYlQkMlODkvcHdkL2ltZyIsInJlcXVlc3RfaWQiOiJDODI3OjE1MjM6MzlBNjM6MTBCRTgyOjYyMTZENTlBIiwidmlzaXRvcl9pZCI6IjMyNDk3NjgxMzQwNzc3NjQwMTEiLCJyZWdpb25fZWRnZSI6InNvdXRoZWFzdGFzaWEiLCJyZWdpb25fcmVuZGVyIjoic291dGhlYXN0YXNpYSJ9" data-pjax-transient="true"/><meta name="visitor-hmac" content="8c6a925527fa01498679ac0882c3d876f484abccb187fa5308e45929fd7e6e2e" data-pjax-transient="true"/>

    <meta name="hovercard-subject-tag" content="repository:*********" data-pjax-transient>


  <meta name="github-keyboard-shortcuts" content="repository,source-code" data-pjax-transient="true" />

  

  <meta name="selected-link" value="repo_source" data-pjax-transient>

    <meta name="google-site-verification" content="c1kuD-K2HIVF635lypcsWPoD4kilo5-jA_wBFyT4uMY">
  <meta name="google-site-verification" content="KT5gs8h0wvaagLKAVWq8bbeNwnZZK1r1XQysX3xurLU">
  <meta name="google-site-verification" content="ZzhVyEFwb7w3e0-uOTltm8Jsck2F5StVihD0exw2fsA">
  <meta name="google-site-verification" content="GXs5KoUUkNCoaAZn7wPN-t01Pywp9M3sEjnt_3_ZWPc">

<meta name="octolytics-url" content="https://collector.github.com/github/collect" />

  <meta name="analytics-location" content="/&lt;user-name&gt;/&lt;repo-name&gt;/blob/show" data-pjax-transient="true" />

  



  <meta name="optimizely-datafile" content="{&quot;version&quot;: &quot;4&quot;, &quot;rollouts&quot;: [], &quot;typedAudiences&quot;: [], &quot;anonymizeIP&quot;: true, &quot;projectId&quot;: &quot;***********&quot;, &quot;variables&quot;: [], &quot;featureFlags&quot;: [], &quot;experiments&quot;: [{&quot;status&quot;: &quot;Running&quot;, &quot;audienceIds&quot;: [], &quot;variations&quot;: [{&quot;variables&quot;: [], &quot;id&quot;: &quot;20438636352&quot;, &quot;key&quot;: &quot;control&quot;}, {&quot;variables&quot;: [], &quot;id&quot;: &quot;20484957397&quot;, &quot;key&quot;: &quot;treatment&quot;}], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;growth_ghec_onboarding_experience&quot;, &quot;layerId&quot;: &quot;20467848595&quot;, &quot;trafficAllocation&quot;: [{&quot;entityId&quot;: &quot;20484957397&quot;, &quot;endOfRange&quot;: 1000}, {&quot;entityId&quot;: &quot;20484957397&quot;, &quot;endOfRange&quot;: 3000}, {&quot;entityId&quot;: &quot;20484957397&quot;, &quot;endOfRange&quot;: 5000}, {&quot;entityId&quot;: &quot;20484957397&quot;, &quot;endOfRange&quot;: 6000}, {&quot;entityId&quot;: &quot;20484957397&quot;, &quot;endOfRange&quot;: 8000}, {&quot;entityId&quot;: &quot;20484957397&quot;, &quot;endOfRange&quot;: 10000}], &quot;forcedVariations&quot;: {&quot;85e2238ce2b9074907d7a3d91d6feeae&quot;: &quot;control&quot;}}, {&quot;status&quot;: &quot;Running&quot;, &quot;audienceIds&quot;: [], &quot;variations&quot;: [{&quot;variables&quot;: [], &quot;id&quot;: &quot;20667381018&quot;, &quot;key&quot;: &quot;control&quot;}, {&quot;variables&quot;: [], &quot;id&quot;: &quot;20680930759&quot;, &quot;key&quot;: &quot;treatment&quot;}], &quot;id&quot;: &quot;20652570897&quot;, &quot;key&quot;: &quot;project_genesis&quot;, &quot;layerId&quot;: &quot;20672300363&quot;, &quot;trafficAllocation&quot;: [{&quot;entityId&quot;: &quot;20667381018&quot;, &quot;endOfRange&quot;: 5000}, {&quot;entityId&quot;: &quot;20680930759&quot;, &quot;endOfRange&quot;: 10000}], &quot;forcedVariations&quot;: {&quot;83356e17066d336d1803024138ecb683&quot;: &quot;treatment&quot;, &quot;18e31c8a9b2271332466133162a4aa0d&quot;: &quot;treatment&quot;, &quot;10f8ab3fbc5ebe989a36a05f79d48f32&quot;: &quot;treatment&quot;, &quot;1686089f6d540cd2deeaec60ee43ecf7&quot;: &quot;treatment&quot;}}, {&quot;status&quot;: &quot;Running&quot;, &quot;audienceIds&quot;: [], &quot;variations&quot;: [{&quot;variables&quot;: [], &quot;id&quot;: &quot;20898546114&quot;, &quot;key&quot;: &quot;control&quot;}, {&quot;variables&quot;: [], &quot;id&quot;: &quot;20923036705&quot;, &quot;key&quot;: &quot;treatment_a&quot;}, {&quot;variables&quot;: [], &quot;id&quot;: &quot;20965581308&quot;, &quot;key&quot;: &quot;treatment_b&quot;}], &quot;id&quot;: &quot;20902325119&quot;, &quot;key&quot;: &quot;contact_sales_page_optimizations&quot;, &quot;layerId&quot;: &quot;20969031091&quot;, &quot;trafficAllocation&quot;: [{&quot;entityId&quot;: &quot;20965581308&quot;, &quot;endOfRange&quot;: 3330}, {&quot;entityId&quot;: &quot;20898546114&quot;, &quot;endOfRange&quot;: 5000}, {&quot;entityId&quot;: &quot;20898546114&quot;, &quot;endOfRange&quot;: 6670}, {&quot;entityId&quot;: &quot;20923036705&quot;, &quot;endOfRange&quot;: 10000}], &quot;forcedVariations&quot;: {}}], &quot;audiences&quot;: [{&quot;conditions&quot;: &quot;[\&quot;or\&quot;, {\&quot;match\&quot;: \&quot;exact\&quot;, \&quot;name\&quot;: \&quot;$opt_dummy_attribute\&quot;, \&quot;type\&quot;: \&quot;custom_attribute\&quot;, \&quot;value\&quot;: \&quot;$opt_dummy_value\&quot;}]&quot;, &quot;id&quot;: &quot;$opt_dummy_audience&quot;, &quot;name&quot;: &quot;Optimizely-Generated Audience for Backwards Compatibility&quot;}], &quot;groups&quot;: [], &quot;sdkKey&quot;: &quot;WTc6awnGuYDdG98CYRban&quot;, &quot;environmentKey&quot;: &quot;production&quot;, &quot;attributes&quot;: [{&quot;id&quot;: &quot;16822470375&quot;, &quot;key&quot;: &quot;user_id&quot;}, {&quot;id&quot;: &quot;17143601254&quot;, &quot;key&quot;: &quot;spammy&quot;}, {&quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;organization_plan&quot;}, {&quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;is_logged_in&quot;}, {&quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;geo&quot;}, {&quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;requestedCurrency&quot;}, {&quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;country_code&quot;}], &quot;botFiltering&quot;: false, &quot;accountId&quot;: &quot;***********&quot;, &quot;events&quot;: [{&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;hydro_click.dashboard.teacher_toolbox_cta&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.organizations.complete_sign_up&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;no_metric.tracked_outside_of_optimizely&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.org_onboarding_checklist.add_repo&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.repository_imports.create&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.help.learn_more_about_repository_creation&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;test_event&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18191963644&quot;, &quot;key&quot;: &quot;click.empty_org_repo_cta.transfer_repository&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18195612788&quot;, &quot;key&quot;: &quot;click.empty_org_repo_cta.import_repository&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18210945499&quot;, &quot;key&quot;: &quot;click.org_onboarding_checklist.invite_members&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18211063248&quot;, &quot;key&quot;: &quot;click.empty_org_repo_cta.create_repository&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18215721889&quot;, &quot;key&quot;: &quot;click.org_onboarding_checklist.update_profile&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18224360785&quot;, &quot;key&quot;: &quot;click.org_onboarding_checklist.dismiss&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18234832286&quot;, &quot;key&quot;: &quot;submit.organization_activation.complete&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18252392383&quot;, &quot;key&quot;: &quot;submit.org_repository.create&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18257551537&quot;, &quot;key&quot;: &quot;submit.org_member_invitation.create&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18259522260&quot;, &quot;key&quot;: &quot;submit.organization_profile.update&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18564603625&quot;, &quot;key&quot;: &quot;view.classroom_select_organization&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18568612016&quot;, &quot;key&quot;: &quot;click.classroom_sign_in_click&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18572592540&quot;, &quot;key&quot;: &quot;view.classroom_name&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18574203855&quot;, &quot;key&quot;: &quot;click.classroom_create_organization&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18582053415&quot;, &quot;key&quot;: &quot;click.classroom_select_organization&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;18589463420&quot;, &quot;key&quot;: &quot;click.classroom_create_classroom&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.classroom_create_first_classroom&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.classroom_grant_access&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;view.classroom_creation&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;upgrade_account_plan&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.signup&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.view_account_billing_page&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.dismiss_signup_prompt&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.contact_sales&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.compare_account_plans&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.upgrade_account_cta&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.open_account_switcher&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.visit_account_profile&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.switch_account_context&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.homepage_signup&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.homepage_signup&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.create_enterprise_trial&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.create_organization_team&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.input_enterprise_trial_form&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.continue_with_team&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.create_organization_free&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.signup_continue.username&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.signup_continue.create_account&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.signup_continue.email&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.signup_continue.password&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;view.pricing_page&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.create_account&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.upgrade_payment_form&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.create_organization&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.recommended_plan_in_signup.discuss_your_needs&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;submit.verify_primary_user_email&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.recommended_plan_in_signup.try_enterprise&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.recommended_plan_in_signup.team&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;***********&quot;, &quot;key&quot;: &quot;click.recommended_plan_in_signup.continue_free&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20251097193&quot;, &quot;key&quot;: &quot;recommended_plan&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20438619534&quot;, &quot;key&quot;: &quot;click.pricing_calculator.1_member&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20456699683&quot;, &quot;key&quot;: &quot;click.pricing_calculator.15_members&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20467868331&quot;, &quot;key&quot;: &quot;click.pricing_calculator.10_members&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20476267432&quot;, &quot;key&quot;: &quot;click.trial_days_remaining&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;20476357660&quot;, &quot;key&quot;: &quot;click.discover_feature&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20479287901&quot;, &quot;key&quot;: &quot;click.pricing_calculator.custom_members&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20481107083&quot;, &quot;key&quot;: &quot;click.recommended_plan_in_signup.apply_teacher_benefits&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20483089392&quot;, &quot;key&quot;: &quot;click.pricing_calculator.5_members&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;, &quot;20652570897&quot;], &quot;id&quot;: &quot;20484283944&quot;, &quot;key&quot;: &quot;click.onboarding_task&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20484996281&quot;, &quot;key&quot;: &quot;click.recommended_plan_in_signup.apply_student_benefits&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;20486713726&quot;, &quot;key&quot;: &quot;click.onboarding_task_breadcrumb&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;20490791319&quot;, &quot;key&quot;: &quot;click.upgrade_to_enterprise&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;20491786766&quot;, &quot;key&quot;: &quot;click.talk_to_us&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;20494144087&quot;, &quot;key&quot;: &quot;click.dismiss_enterprise_trial&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;, &quot;20652570897&quot;], &quot;id&quot;: &quot;20499722759&quot;, &quot;key&quot;: &quot;completed_all_tasks&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;, &quot;20652570897&quot;], &quot;id&quot;: &quot;20500710104&quot;, &quot;key&quot;: &quot;completed_onboarding_tasks&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;20513160672&quot;, &quot;key&quot;: &quot;click.read_doc&quot;}, {&quot;experimentIds&quot;: [&quot;20652570897&quot;], &quot;id&quot;: &quot;20516196762&quot;, &quot;key&quot;: &quot;actions_enabled&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;20518980986&quot;, &quot;key&quot;: &quot;click.dismiss_trial_banner&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20535446721&quot;, &quot;key&quot;: &quot;click.issue_actions_prompt.dismiss_prompt&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20557002247&quot;, &quot;key&quot;: &quot;click.issue_actions_prompt.setup_workflow&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20595070227&quot;, &quot;key&quot;: &quot;click.pull_request_setup_workflow&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20626600314&quot;, &quot;key&quot;: &quot;click.seats_input&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20642310305&quot;, &quot;key&quot;: &quot;click.decrease_seats_number&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20662990045&quot;, &quot;key&quot;: &quot;click.increase_seats_number&quot;}, {&quot;experimentIds&quot;: [], &quot;id&quot;: &quot;20679620969&quot;, &quot;key&quot;: &quot;click.public_product_roadmap&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;20761240940&quot;, &quot;key&quot;: &quot;click.dismiss_survey_banner&quot;}, {&quot;experimentIds&quot;: [&quot;***********&quot;], &quot;id&quot;: &quot;20767210721&quot;, &quot;key&quot;: &quot;click.take_survey&quot;}, {&quot;experimentIds&quot;: [&quot;20652570897&quot;], &quot;id&quot;: &quot;20795281201&quot;, &quot;key&quot;: &quot;click.archive_list&quot;}, {&quot;experimentIds&quot;: [&quot;20902325119&quot;], &quot;id&quot;: &quot;20966790249&quot;, &quot;key&quot;: &quot;contact_sales.submit&quot;}, {&quot;experimentIds&quot;: [&quot;20902325119&quot;], &quot;id&quot;: &quot;20996500333&quot;, &quot;key&quot;: &quot;contact_sales.existing_customer&quot;}, {&quot;experimentIds&quot;: [&quot;20902325119&quot;], &quot;id&quot;: &quot;20996890162&quot;, &quot;key&quot;: &quot;contact_sales.blank_message_field&quot;}, {&quot;experimentIds&quot;: [&quot;20902325119&quot;], &quot;id&quot;: &quot;21000470317&quot;, &quot;key&quot;: &quot;contact_sales.personal_email&quot;}, {&quot;experimentIds&quot;: [&quot;20902325119&quot;], &quot;id&quot;: &quot;21002790172&quot;, &quot;key&quot;: &quot;contact_sales.blank_phone_field&quot;}], &quot;revision&quot;: &quot;1065&quot;}" />
  <!-- To prevent page flashing, the optimizely JS needs to be loaded in the
    <head> tag before the DOM renders -->
  <script crossorigin="anonymous" defer="defer" integrity="sha512-fhp8EDqbNbae6xwqU8vIxb89PjZnn5tMKAJRXukI4l3n/MMRnhDkQ0XLt1a8gcaHTOvJ0HTElC0eU8RPpMboYA==" type="application/javascript" src="https://github.githubassets.com/assets/optimizely-7e1a7c10.js"></script>



  

      <meta name="hostname" content="github.com">
    <meta name="user-login" content="">


      <meta name="expected-hostname" content="github.com">


    <meta name="enabled-features" content="MARKETPLACE_PENDING_INSTALLATIONS">


  <meta http-equiv="x-pjax-version" content="643f1a5899fdc81fe753e8388377471bf5d369ac5a4c467fdc45e3b5497047c0" data-turbo-track="reload">
  <meta http-equiv="x-pjax-csp-version" content="ad743a89372c421844ffcba4fd906096b07b7fd7c2a57617ff2d2f0fdf463e56" data-turbo-track="reload">
  <meta http-equiv="x-pjax-css-version" content="4183ada13d84caf4864da550d6284e97172b165563fce90e21812e1c5fef9c9c" data-turbo-track="reload">
  <meta http-equiv="x-pjax-js-version" content="42ef0509a6f8747ce9140d1512ddc9fc8e5e141ae6076759fe6778971298277c" data-turbo-track="reload">
  

    
  <meta name="go-import" content="github.com/Rkatsiteli/Vue-archive git https://github.com/Rkatsiteli/Vue-archive.git">

  <meta name="octolytics-dimension-user_id" content="16810179" /><meta name="octolytics-dimension-user_login" content="Rkatsiteli" /><meta name="octolytics-dimension-repository_id" content="*********" /><meta name="octolytics-dimension-repository_nwo" content="Rkatsiteli/Vue-archive" /><meta name="octolytics-dimension-repository_public" content="true" /><meta name="octolytics-dimension-repository_is_fork" content="false" /><meta name="octolytics-dimension-repository_network_root_id" content="*********" /><meta name="octolytics-dimension-repository_network_root_nwo" content="Rkatsiteli/Vue-archive" />



    <link rel="canonical" href="https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif" data-pjax-transient>


  <meta name="browser-stats-url" content="https://api.github.com/_private/browser/stats">

  <meta name="browser-errors-url" content="https://api.github.com/_private/browser/errors">

  <meta name="browser-optimizely-client-errors-url" content="https://api.github.com/_private/browser/optimizely_client/errors">

  <link rel="mask-icon" href="https://github.githubassets.com/pinned-octocat.svg" color="#000000">
  <link rel="alternate icon" class="js-site-favicon" type="image/png" href="https://github.githubassets.com/favicons/favicon.png">
  <link rel="icon" class="js-site-favicon" type="image/svg+xml" href="https://github.githubassets.com/favicons/favicon.svg">

<meta name="theme-color" content="#1e2327">
<meta name="color-scheme" content="light dark" />


  <link rel="manifest" href="/manifest.json" crossOrigin="use-credentials">

  </head>

  <body class="logged-out env-production page-responsive page-blob" style="word-wrap: break-word;">
    

    <div class="position-relative js-header-wrapper ">
      <a href="#start-of-content" class="px-2 py-4 color-bg-accent-emphasis color-fg-on-emphasis show-on-focus js-skip-to-content">Skip to content</a>
      <span data-view-component="true" class="progress-pjax-loader js-pjax-loader-bar Progress position-fixed width-full">
    <span style="width: 0%;" data-view-component="true" class="Progress-item progress-pjax-loader-bar left-0 top-0 color-bg-accent-emphasis"></span>
</span>      
      


        

            <header class="Header-old header-logged-out js-details-container Details position-relative f4 py-2" role="banner">
  <div class="container-xl d-lg-flex flex-items-center p-responsive">
    <div class="d-flex flex-justify-between flex-items-center">
      <a class="mr-4 color-fg-inherit" href="https://github.com/" aria-label="Homepage" data-ga-click="(Logged out) Header, go to homepage, icon:logo-wordmark">
        <svg height="32" aria-hidden="true" viewBox="0 0 16 16" version="1.1" width="32" data-view-component="true" class="octicon octicon-mark-github">
    <path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path>
</svg>
      </a>

        <div class="d-lg-none css-truncate css-truncate-target width-fit p-2">
          

        </div>

      <div class="d-flex flex-items-center">
            <a href="/signup?ref_cta=Sign+up&amp;ref_loc=header+logged+out&amp;ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fblob%2Fshow&amp;source=header-repo"
              class="d-inline-block d-lg-none f5 no-underline border color-border-default rounded-2 px-2 py-1 mr-3 mr-sm-5 color-fg-inherit"
              data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="161196ee34f9eaf7a40fa5b820a5a21fec59c1787e6b9a104e97a70bc9ef4a7e"
            >
              Sign&nbsp;up
            </a>

        <button aria-label="Toggle navigation" aria-expanded="false" type="button" data-view-component="true" class="js-details-target btn-link d-lg-none mt-1 color-fg-inherit">  <svg aria-hidden="true" height="24" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-three-bars">
    <path fill-rule="evenodd" d="M1 2.75A.75.75 0 011.75 2h12.5a.75.75 0 110 1.5H1.75A.75.75 0 011 2.75zm0 5A.75.75 0 011.75 7h12.5a.75.75 0 110 1.5H1.75A.75.75 0 011 7.75zM1.75 12a.75.75 0 100 1.5h12.5a.75.75 0 100-1.5H1.75z"></path>
</svg>
</button>      </div>
    </div>

    <div class="HeaderMenu HeaderMenu--logged-out position-fixed top-0 right-0 bottom-0 height-fit position-lg-relative d-lg-flex flex-justify-between flex-items-center flex-auto">
      <div class="d-flex d-lg-none flex-justify-end border-bottom color-bg-subtle p-3">
        <button aria-label="Toggle navigation" aria-expanded="false" type="button" data-view-component="true" class="js-details-target btn-link">  <svg aria-hidden="true" height="24" viewBox="0 0 24 24" version="1.1" width="24" data-view-component="true" class="octicon octicon-x color-fg-muted">
    <path fill-rule="evenodd" d="M5.72 5.72a.75.75 0 011.06 0L12 10.94l5.22-5.22a.75.75 0 111.06 1.06L13.06 12l5.22 5.22a.75.75 0 11-1.06 1.06L12 13.06l-5.22 5.22a.75.75 0 01-1.06-1.06L10.94 12 5.72 6.78a.75.75 0 010-1.06z"></path>
</svg>
</button>      </div>

        <nav class="mt-0 px-3 px-lg-0 mb-5 mb-lg-0" aria-label="Global">
          <ul class="d-lg-flex list-style-none">
              <li class="mr-0 mr-lg-3 position-relative flex-wrap flex-justify-between flex-items-center border-bottom border-lg-bottom-0 d-block d-lg-flex flex-lg-nowrap flex-lg-items-center">
    <details class="HeaderMenu-details details-overlay details-reset width-full">
      <summary class="HeaderMenu-summary HeaderMenu-link px-0 py-3 border-0 no-wrap d-block d-lg-inline-block">
        Why GitHub?
        <svg x="0" y="0" viewBox="0 0 14 8" xml:space="preserve" fill="none" class="icon-chevon-down-mktg position-absolute position-lg-relative"><path d="M1,1l6.2,6L13,1"></path></svg>
      </summary>
      <div class="dropdown-menu flex-auto rounded px-0 mt-0 pb-4 p-lg-4 position-relative position-lg-absolute left-0 left-lg-n4">
        <ul class="list-style-none f5 pb-1">
              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--primary text-bold py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Features&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Features;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="2c1ba1fc80f656fc193894828471ae29393fb9aa5188b40a09c56b273b730a6f" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Features&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Features;&quot;}" href="/features">
      Features
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Mobile&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Mobile;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="5a404cd65fd3ffd3ebada0f3787ecaf75818e88a74ef5c63c0aed11fc7789907" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Mobile&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Mobile;&quot;}" href="/mobile">
      Mobile
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Actions&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Actions;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="247d2a61454cdf99bde96d6087719ec75f829fbce3f68ff93266e0801c6064be" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Actions&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Actions;&quot;}" href="/features/actions">
      Actions
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Codespaces&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Codespaces;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="0a3d8b2eed6aef733703990e10a49a316cf4da0cc93a6281055b21e759be21e8" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Codespaces&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Codespaces;&quot;}" href="/features/codespaces">
      Codespaces
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Packages&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Packages;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="691e4b166f8bb6d5ead7d6514fb4a6f399a0220897b39dd59cc9791f2b2589e5" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Packages&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Packages;&quot;}" href="/features/packages">
      Packages
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Security&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Security;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="5e9b357283b3158c8d9aba88a7fa5c5e16881a0792953f25bce9ed4de170ebd5" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Security&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Security;&quot;}" href="/features/security">
      Security
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Code review&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Code review;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="269705dff925663fb9f1442fa31230bfe9eb5964d4c3df2ddd0bfcd93e690298" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Code review&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Code review;&quot;}" href="/features/code-review">
      Code review
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Issues&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Issues;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="536f00018b7f9e7b42eb9557e95c2869e454634e3a9a564ca5944b5d720cea92" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Issues&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Issues;&quot;}" href="/features/issues">
      Issues
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Integrations&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Integrations;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="20531e06184f954ea92bbef8db1d34b589e721a6970621cf5f72c8cb9856e84d" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Integrations&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Integrations;&quot;}" href="/features/integrations">
      Integrations
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--primary text-bold border-top pt-4 pb-2 mt-3" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to GitHub Sponsors&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:GitHub Sponsors;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="f144f8fe247d5f7dd9e31041755ea8a96b087fcf61a94348c7665b3cf92d7ad1" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to GitHub Sponsors&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:GitHub Sponsors;&quot;}" href="/sponsors">
      GitHub Sponsors
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--primary text-bold py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Customer stories&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Customer stories;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="4347de4460d5d0ee394da4ac356103ca502d5b786630180420db526473f0d108" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Why GitHub?&quot;,&quot;action&quot;:&quot;click to go to Customer stories&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Customer stories;&quot;}" href="/customer-stories">
      Customer stories
</a>  </li>

        </ul>
      </div>
    </details>
</li>


              <li class="mr-0 mr-lg-3 position-relative flex-wrap flex-justify-between flex-items-center border-bottom border-lg-bottom-0 d-block d-lg-flex flex-lg-nowrap flex-lg-items-center">
    <a class="HeaderMenu-link no-underline py-3 d-block d-lg-inline-block" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header menu top item (logged out)&quot;,&quot;action&quot;:&quot;click to go to Team&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Team;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="4d1bcba732dca03de2dfa8a90d3201b6360cb1589ddbe0f9d5e29fde3ec4086d" data-analytics-event="{&quot;category&quot;:&quot;Header menu top item (logged out)&quot;,&quot;action&quot;:&quot;click to go to Team&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Team;&quot;}" href="/team">Team</a>
</li>

              <li class="mr-0 mr-lg-3 position-relative flex-wrap flex-justify-between flex-items-center border-bottom border-lg-bottom-0 d-block d-lg-flex flex-lg-nowrap flex-lg-items-center">
    <a class="HeaderMenu-link no-underline py-3 d-block d-lg-inline-block" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header menu top item (logged out)&quot;,&quot;action&quot;:&quot;click to go to Enterprise&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Enterprise;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="3cc558a2448e20a85410f095854f2aec64baa2bcf62fb5828fe055a0b5f21d3b" data-analytics-event="{&quot;category&quot;:&quot;Header menu top item (logged out)&quot;,&quot;action&quot;:&quot;click to go to Enterprise&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Enterprise;&quot;}" href="/enterprise">Enterprise</a>
</li>


            <li class="mr-0 mr-lg-3 position-relative flex-wrap flex-justify-between flex-items-center border-bottom border-lg-bottom-0 d-block d-lg-flex flex-lg-nowrap flex-lg-items-center">
    <details class="HeaderMenu-details details-overlay details-reset width-full">
      <summary class="HeaderMenu-summary HeaderMenu-link px-0 py-3 border-0 no-wrap d-block d-lg-inline-block">
        Explore
        <svg x="0" y="0" viewBox="0 0 14 8" xml:space="preserve" fill="none" class="icon-chevon-down-mktg position-absolute position-lg-relative"><path d="M1,1l6.2,6L13,1"></path></svg>
      </summary>
      <div class="dropdown-menu flex-auto rounded px-0 mt-0 pb-4 p-lg-4 position-relative position-lg-absolute left-0 left-lg-n4">
        <ul class="list-style-none f5 pb-1">
              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--primary text-bold py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Explore GitHub&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Explore GitHub;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="d5ff405c3acf5f17d077fe3829ae5086e309a1c29fd82cc6201a9320785e52eb" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Explore GitHub&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Explore GitHub;&quot;}" href="/explore">
      Explore GitHub
</a>  </li>

              <li class="color-fg-muted text-normal f6 text-mono mb-1 border-top pt-3 mt-3 mb-1">Learn and contribute</li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Topics&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Topics;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="be57487e4df0b59aba0e1470786f0d8cf93d1480a7d504a9d8484666c30e76e2" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Topics&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Topics;&quot;}" href="/topics">
      Topics
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Collections&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Collections;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="d53329a08a657bd9b3653ed3ff0273bc975d87e9d1ff75d4ed3165ca26bf6337" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Collections&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Collections;&quot;}" href="/collections">
      Collections
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Trending&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Trending;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="4844262be2b7f2a630b5dcc1c8bf7174aafa7a0274260e1fae9f14b14fddfff7" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Trending&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Trending;&quot;}" href="/trending">
      Trending
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Learning Lab&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Learning Lab;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="d8ea676d1f01fba5862b1588bfa0ff8d0b2d8634dabbef3c85570fc352e5bf8e" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Learning Lab&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Learning Lab;&quot;}" href="https://lab.github.com/">
      Learning Lab
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Open source guides&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Open source guides;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="aceb4d4c8bc895ed892e12deab8b537a4d81703fd1d168437f44bf5cb9e329d4" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Open source guides&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Open source guides;&quot;}" href="https://opensource.guide">
      Open source guides
</a>  </li>

              <li class="color-fg-muted text-normal f6 text-mono mb-1 border-top pt-3 mt-3 mb-1">Connect with others</li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to The ReadME Project&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:The ReadME Project;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="e030d94177fc04c14b375c371a8317493204130463e3f9468b2f83f381e6682a" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to The ReadME Project&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:The ReadME Project;&quot;}" href="/readme">
      The ReadME Project
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Events&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Events;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="9af3803d7cc7253c355e465b9bfb255efefbbc9e57efdd7aa8d8868a1250e192" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Events&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Events;&quot;}" href="/events">
      Events
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Community forum&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Community forum;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="d6360bff294eb04010f3ca5e4229100cab5bf210edf5ba6c34b7758dea6e2e69" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to Community forum&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Community forum;&quot;}" href="https://github.community">
      Community forum
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to GitHub Education&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:GitHub Education;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="3488667287cee77a0107ce220fc554f0b723421a51c98dd790084e334e8ed3b4" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to GitHub Education&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:GitHub Education;&quot;}" href="https://education.github.com">
      GitHub Education
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to GitHub Stars program&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:GitHub Stars program;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="f985c1e2524b2c649b84848a3157325f86c02b20c7351f9348d6a71df3b7914f" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Explore&quot;,&quot;action&quot;:&quot;click to go to GitHub Stars program&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:GitHub Stars program;&quot;}" href="https://stars.github.com">
      GitHub Stars program
</a>  </li>

        </ul>
      </div>
    </details>
</li>


            <li class="mr-0 mr-lg-3 position-relative flex-wrap flex-justify-between flex-items-center border-bottom border-lg-bottom-0 d-block d-lg-flex flex-lg-nowrap flex-lg-items-center">
    <a class="HeaderMenu-link no-underline py-3 d-block d-lg-inline-block" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header menu top item (logged out)&quot;,&quot;action&quot;:&quot;click to go to Marketplace&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Marketplace;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="f51799ac07e7bf231be3112f5a80eda4350c0f079c35d0dee6da097263e60e1e" data-analytics-event="{&quot;category&quot;:&quot;Header menu top item (logged out)&quot;,&quot;action&quot;:&quot;click to go to Marketplace&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Marketplace;&quot;}" href="/marketplace">Marketplace</a>
</li>


            <li class="mr-0 mr-lg-3 position-relative flex-wrap flex-justify-between flex-items-center border-bottom border-lg-bottom-0 d-block d-lg-flex flex-lg-nowrap flex-lg-items-center">
    <details class="HeaderMenu-details details-overlay details-reset width-full">
      <summary class="HeaderMenu-summary HeaderMenu-link px-0 py-3 border-0 no-wrap d-block d-lg-inline-block">
        Pricing
        <svg x="0" y="0" viewBox="0 0 14 8" xml:space="preserve" fill="none" class="icon-chevon-down-mktg position-absolute position-lg-relative"><path d="M1,1l6.2,6L13,1"></path></svg>
      </summary>
      <div class="dropdown-menu flex-auto rounded px-0 mt-0 pb-4 p-lg-4 position-relative position-lg-absolute left-0 left-lg-n4">
        <ul class="list-style-none f5 pb-1">
              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--primary text-bold py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Pricing&quot;,&quot;action&quot;:&quot;click to go to Plans&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Plans;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="1ee06ac86ab1799a9fbbe44291dfc69576f6491eab8b1ccb1f3498135e49ca4a" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Pricing&quot;,&quot;action&quot;:&quot;click to go to Plans&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Plans;&quot;}" href="/pricing">
      Plans
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Pricing&quot;,&quot;action&quot;:&quot;click to go to Compare plans&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Compare plans;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="552b88063604cb0e840db585630797c433d330c6fe8c6f8cbbdab902bad010aa" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Pricing&quot;,&quot;action&quot;:&quot;click to go to Compare plans&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Compare plans;&quot;}" href="/pricing#compare-features">
      Compare plans
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--secondary py-2" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Pricing&quot;,&quot;action&quot;:&quot;click to go to Contact Sales&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Contact Sales;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="885113b931a1ccc4d2bdbc393c517c6facdd21b46f88ffb941500db2f250ddfe" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Pricing&quot;,&quot;action&quot;:&quot;click to go to Contact Sales&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Contact Sales;&quot;}" href="https://github.com/enterprise/contact">
      Contact Sales
</a>  </li>

              <li>
    <a class="lh-condensed-ultra d-block no-underline position-relative Link--primary text-bold border-top pt-4 pb-2 mt-3" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Header dropdown (logged out), Pricing&quot;,&quot;action&quot;:&quot;click to go to Education&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Education;&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="d12b23cd733c0dbcf8d4de638413acd744b30c2336845b570e73cd470702fdb7" data-analytics-event="{&quot;category&quot;:&quot;Header dropdown (logged out), Pricing&quot;,&quot;action&quot;:&quot;click to go to Education&quot;,&quot;label&quot;:&quot;ref_page:/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif;ref_cta:Education;&quot;}" href="https://education.github.com">
      Education
</a>  </li>

        </ul>
      </div>
    </details>
</li>

          </ul>
        </nav>

      <div class="d-lg-flex flex-items-center px-3 px-lg-0 text-center text-lg-left">
          <div class="d-lg-flex min-width-0 mb-3 mb-lg-0">
            



<div class="header-search flex-auto js-site-search position-relative flex-self-stretch flex-md-self-auto mb-3 mb-md-0 mr-0 mr-md-3 scoped-search site-scoped-search js-jump-to"
>
  <div class="position-relative">
    <!-- '"` --><!-- </textarea></xmp> --></option></form><form class="js-site-search-form" role="search" aria-label="Site" data-scope-type="Repository" data-scope-id="*********" data-scoped-search-url="/Rkatsiteli/Vue-archive/search" data-owner-scoped-search-url="/users/Rkatsiteli/search" data-unscoped-search-url="/search" action="/Rkatsiteli/Vue-archive/search" accept-charset="UTF-8" method="get">
      <label class="form-control input-sm header-search-wrapper p-0 js-chromeless-input-container header-search-wrapper-jump-to position-relative d-flex flex-justify-between flex-items-center">
        <input type="text"
          class="form-control input-sm header-search-input jump-to-field js-jump-to-field js-site-search-focus js-site-search-field is-clearable"
          data-hotkey=s,/
          name="q"
          data-test-selector="nav-search-input"
          placeholder="Search"
          data-unscoped-placeholder="Search GitHub"
          data-scoped-placeholder="Search"
          autocapitalize="off"
          role="combobox"
          aria-haspopup="listbox"
          aria-expanded="false"
          aria-autocomplete="list"
          aria-controls="jump-to-results"
          aria-label="Search"
          data-jump-to-suggestions-path="/_graphql/GetSuggestedNavigationDestinations"
          spellcheck="false"
          autocomplete="off"
        >
        <input type="hidden" data-csrf="true" class="js-data-jump-to-suggestions-path-csrf" value="0oCSZOb2g9uknIi7l36RrkjmBZ9Kq6hkZmKUJW7vsRNqvu3HJrnX+GIveMA1OgNmI5sYXnNLqXVD+t6tP9Xa3A==" />
        <input type="hidden" class="js-site-search-type-field" name="type" >
            <svg xmlns="http://www.w3.org/2000/svg" width="22" height="20" aria-hidden="true" class="mr-1 header-search-key-slash"><path fill="none" stroke="#979A9C" opacity=".4" d="M3.5.5h12c1.7 0 3 1.3 3 3v13c0 1.7-1.3 3-3 3h-12c-1.7 0-3-1.3-3-3v-13c0-1.7 1.3-3 3-3z"></path><path fill="#979A9C" d="M11.8 6L8 15.1h-.9L10.8 6h1z"></path></svg>


          <div class="Box position-absolute overflow-hidden d-none jump-to-suggestions js-jump-to-suggestions-container">
            
<ul class="d-none js-jump-to-suggestions-template-container">
  

<li class="d-flex flex-justify-start flex-items-center p-0 f5 navigation-item js-navigation-item js-jump-to-suggestion" role="option">
  <a tabindex="-1" class="no-underline d-flex flex-auto flex-items-center jump-to-suggestions-path js-jump-to-suggestion-path js-navigation-open p-2" href="" data-item-type="suggestion">
    <div class="jump-to-octicon js-jump-to-octicon flex-shrink-0 mr-2 text-center d-none">
      <svg title="Repository" aria-label="Repository" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-repo js-jump-to-octicon-repo d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M2 2.5A2.5 2.5 0 014.5 0h8.75a.75.75 0 01.75.75v12.5a.75.75 0 01-.75.75h-2.5a.75.75 0 110-1.5h1.75v-2h-8a1 1 0 00-.714 1.7.75.75 0 01-1.072 1.05A2.495 2.495 0 012 11.5v-9zm10.5-1V9h-8c-.356 0-.694.074-1 .208V2.5a1 1 0 011-1h8zM5 12.25v3.25a.25.25 0 00.4.2l1.45-1.087a.25.25 0 01.3 0L8.6 15.7a.25.25 0 00.4-.2v-3.25a.25.25 0 00-.25-.25h-3.5a.25.25 0 00-.25.25z"></path>
</svg>
      <svg title="Project" aria-label="Project" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-project js-jump-to-octicon-project d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M1.75 0A1.75 1.75 0 000 1.75v12.5C0 15.216.784 16 1.75 16h12.5A1.75 1.75 0 0016 14.25V1.75A1.75 1.75 0 0014.25 0H1.75zM1.5 1.75a.25.25 0 01.25-.25h12.5a.25.25 0 01.25.25v12.5a.25.25 0 01-.25.25H1.75a.25.25 0 01-.25-.25V1.75zM11.75 3a.75.75 0 00-.75.75v7.5a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75zm-8.25.75a.75.75 0 011.5 0v5.5a.75.75 0 01-1.5 0v-5.5zM8 3a.75.75 0 00-.75.75v3.5a.75.75 0 001.5 0v-3.5A.75.75 0 008 3z"></path>
</svg>
      <svg title="Search" aria-label="Search" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-search js-jump-to-octicon-search d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M11.5 7a4.499 4.499 0 11-8.998 0A4.499 4.499 0 0111.5 7zm-.82 4.74a6 6 0 111.06-1.06l3.04 3.04a.75.75 0 11-1.06 1.06l-3.04-3.04z"></path>
</svg>
    </div>

    <img class="avatar mr-2 flex-shrink-0 js-jump-to-suggestion-avatar d-none" alt="" aria-label="Team" src="" width="28" height="28">

    <div class="jump-to-suggestion-name js-jump-to-suggestion-name flex-auto overflow-hidden text-left no-wrap css-truncate css-truncate-target">
    </div>

    <div class="border rounded-1 flex-shrink-0 color-bg-subtle px-1 color-fg-muted ml-1 f6 d-none js-jump-to-badge-search">
      <span class="js-jump-to-badge-search-text-default d-none" aria-label="in this repository">
        In this repository
      </span>
      <span class="js-jump-to-badge-search-text-global d-none" aria-label="in all of GitHub">
        All GitHub
      </span>
      <span aria-hidden="true" class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>

    <div aria-hidden="true" class="border rounded-1 flex-shrink-0 color-bg-subtle px-1 color-fg-muted ml-1 f6 d-none d-on-nav-focus js-jump-to-badge-jump">
      Jump to
      <span class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>
  </a>
</li>

</ul>

<ul class="d-none js-jump-to-no-results-template-container">
  <li class="d-flex flex-justify-center flex-items-center f5 d-none js-jump-to-suggestion p-2">
    <span class="color-fg-muted">No suggested jump to results</span>
  </li>
</ul>

<ul id="jump-to-results" role="listbox" class="p-0 m-0 js-navigation-container jump-to-suggestions-results-container js-jump-to-suggestions-results-container">
  

<li class="d-flex flex-justify-start flex-items-center p-0 f5 navigation-item js-navigation-item js-jump-to-scoped-search d-none" role="option">
  <a tabindex="-1" class="no-underline d-flex flex-auto flex-items-center jump-to-suggestions-path js-jump-to-suggestion-path js-navigation-open p-2" href="" data-item-type="scoped_search">
    <div class="jump-to-octicon js-jump-to-octicon flex-shrink-0 mr-2 text-center d-none">
      <svg title="Repository" aria-label="Repository" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-repo js-jump-to-octicon-repo d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M2 2.5A2.5 2.5 0 014.5 0h8.75a.75.75 0 01.75.75v12.5a.75.75 0 01-.75.75h-2.5a.75.75 0 110-1.5h1.75v-2h-8a1 1 0 00-.714 1.7.75.75 0 01-1.072 1.05A2.495 2.495 0 012 11.5v-9zm10.5-1V9h-8c-.356 0-.694.074-1 .208V2.5a1 1 0 011-1h8zM5 12.25v3.25a.25.25 0 00.4.2l1.45-1.087a.25.25 0 01.3 0L8.6 15.7a.25.25 0 00.4-.2v-3.25a.25.25 0 00-.25-.25h-3.5a.25.25 0 00-.25.25z"></path>
</svg>
      <svg title="Project" aria-label="Project" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-project js-jump-to-octicon-project d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M1.75 0A1.75 1.75 0 000 1.75v12.5C0 15.216.784 16 1.75 16h12.5A1.75 1.75 0 0016 14.25V1.75A1.75 1.75 0 0014.25 0H1.75zM1.5 1.75a.25.25 0 01.25-.25h12.5a.25.25 0 01.25.25v12.5a.25.25 0 01-.25.25H1.75a.25.25 0 01-.25-.25V1.75zM11.75 3a.75.75 0 00-.75.75v7.5a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75zm-8.25.75a.75.75 0 011.5 0v5.5a.75.75 0 01-1.5 0v-5.5zM8 3a.75.75 0 00-.75.75v3.5a.75.75 0 001.5 0v-3.5A.75.75 0 008 3z"></path>
</svg>
      <svg title="Search" aria-label="Search" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-search js-jump-to-octicon-search d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M11.5 7a4.499 4.499 0 11-8.998 0A4.499 4.499 0 0111.5 7zm-.82 4.74a6 6 0 111.06-1.06l3.04 3.04a.75.75 0 11-1.06 1.06l-3.04-3.04z"></path>
</svg>
    </div>

    <img class="avatar mr-2 flex-shrink-0 js-jump-to-suggestion-avatar d-none" alt="" aria-label="Team" src="" width="28" height="28">

    <div class="jump-to-suggestion-name js-jump-to-suggestion-name flex-auto overflow-hidden text-left no-wrap css-truncate css-truncate-target">
    </div>

    <div class="border rounded-1 flex-shrink-0 color-bg-subtle px-1 color-fg-muted ml-1 f6 d-none js-jump-to-badge-search">
      <span class="js-jump-to-badge-search-text-default d-none" aria-label="in this repository">
        In this repository
      </span>
      <span class="js-jump-to-badge-search-text-global d-none" aria-label="in all of GitHub">
        All GitHub
      </span>
      <span aria-hidden="true" class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>

    <div aria-hidden="true" class="border rounded-1 flex-shrink-0 color-bg-subtle px-1 color-fg-muted ml-1 f6 d-none d-on-nav-focus js-jump-to-badge-jump">
      Jump to
      <span class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>
  </a>
</li>

  

<li class="d-flex flex-justify-start flex-items-center p-0 f5 navigation-item js-navigation-item js-jump-to-owner-scoped-search d-none" role="option">
  <a tabindex="-1" class="no-underline d-flex flex-auto flex-items-center jump-to-suggestions-path js-jump-to-suggestion-path js-navigation-open p-2" href="" data-item-type="owner_scoped_search">
    <div class="jump-to-octicon js-jump-to-octicon flex-shrink-0 mr-2 text-center d-none">
      <svg title="Repository" aria-label="Repository" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-repo js-jump-to-octicon-repo d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M2 2.5A2.5 2.5 0 014.5 0h8.75a.75.75 0 01.75.75v12.5a.75.75 0 01-.75.75h-2.5a.75.75 0 110-1.5h1.75v-2h-8a1 1 0 00-.714 1.7.75.75 0 01-1.072 1.05A2.495 2.495 0 012 11.5v-9zm10.5-1V9h-8c-.356 0-.694.074-1 .208V2.5a1 1 0 011-1h8zM5 12.25v3.25a.25.25 0 00.4.2l1.45-1.087a.25.25 0 01.3 0L8.6 15.7a.25.25 0 00.4-.2v-3.25a.25.25 0 00-.25-.25h-3.5a.25.25 0 00-.25.25z"></path>
</svg>
      <svg title="Project" aria-label="Project" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-project js-jump-to-octicon-project d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M1.75 0A1.75 1.75 0 000 1.75v12.5C0 15.216.784 16 1.75 16h12.5A1.75 1.75 0 0016 14.25V1.75A1.75 1.75 0 0014.25 0H1.75zM1.5 1.75a.25.25 0 01.25-.25h12.5a.25.25 0 01.25.25v12.5a.25.25 0 01-.25.25H1.75a.25.25 0 01-.25-.25V1.75zM11.75 3a.75.75 0 00-.75.75v7.5a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75zm-8.25.75a.75.75 0 011.5 0v5.5a.75.75 0 01-1.5 0v-5.5zM8 3a.75.75 0 00-.75.75v3.5a.75.75 0 001.5 0v-3.5A.75.75 0 008 3z"></path>
</svg>
      <svg title="Search" aria-label="Search" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-search js-jump-to-octicon-search d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M11.5 7a4.499 4.499 0 11-8.998 0A4.499 4.499 0 0111.5 7zm-.82 4.74a6 6 0 111.06-1.06l3.04 3.04a.75.75 0 11-1.06 1.06l-3.04-3.04z"></path>
</svg>
    </div>

    <img class="avatar mr-2 flex-shrink-0 js-jump-to-suggestion-avatar d-none" alt="" aria-label="Team" src="" width="28" height="28">

    <div class="jump-to-suggestion-name js-jump-to-suggestion-name flex-auto overflow-hidden text-left no-wrap css-truncate css-truncate-target">
    </div>

    <div class="border rounded-1 flex-shrink-0 color-bg-subtle px-1 color-fg-muted ml-1 f6 d-none js-jump-to-badge-search">
      <span class="js-jump-to-badge-search-text-default d-none" aria-label="in this user">
        In this user
      </span>
      <span class="js-jump-to-badge-search-text-global d-none" aria-label="in all of GitHub">
        All GitHub
      </span>
      <span aria-hidden="true" class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>

    <div aria-hidden="true" class="border rounded-1 flex-shrink-0 color-bg-subtle px-1 color-fg-muted ml-1 f6 d-none d-on-nav-focus js-jump-to-badge-jump">
      Jump to
      <span class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>
  </a>
</li>

  

<li class="d-flex flex-justify-start flex-items-center p-0 f5 navigation-item js-navigation-item js-jump-to-global-search d-none" role="option">
  <a tabindex="-1" class="no-underline d-flex flex-auto flex-items-center jump-to-suggestions-path js-jump-to-suggestion-path js-navigation-open p-2" href="" data-item-type="global_search">
    <div class="jump-to-octicon js-jump-to-octicon flex-shrink-0 mr-2 text-center d-none">
      <svg title="Repository" aria-label="Repository" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-repo js-jump-to-octicon-repo d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M2 2.5A2.5 2.5 0 014.5 0h8.75a.75.75 0 01.75.75v12.5a.75.75 0 01-.75.75h-2.5a.75.75 0 110-1.5h1.75v-2h-8a1 1 0 00-.714 1.7.75.75 0 01-1.072 1.05A2.495 2.495 0 012 11.5v-9zm10.5-1V9h-8c-.356 0-.694.074-1 .208V2.5a1 1 0 011-1h8zM5 12.25v3.25a.25.25 0 00.4.2l1.45-1.087a.25.25 0 01.3 0L8.6 15.7a.25.25 0 00.4-.2v-3.25a.25.25 0 00-.25-.25h-3.5a.25.25 0 00-.25.25z"></path>
</svg>
      <svg title="Project" aria-label="Project" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-project js-jump-to-octicon-project d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M1.75 0A1.75 1.75 0 000 1.75v12.5C0 15.216.784 16 1.75 16h12.5A1.75 1.75 0 0016 14.25V1.75A1.75 1.75 0 0014.25 0H1.75zM1.5 1.75a.25.25 0 01.25-.25h12.5a.25.25 0 01.25.25v12.5a.25.25 0 01-.25.25H1.75a.25.25 0 01-.25-.25V1.75zM11.75 3a.75.75 0 00-.75.75v7.5a.75.75 0 001.5 0v-7.5a.75.75 0 00-.75-.75zm-8.25.75a.75.75 0 011.5 0v5.5a.75.75 0 01-1.5 0v-5.5zM8 3a.75.75 0 00-.75.75v3.5a.75.75 0 001.5 0v-3.5A.75.75 0 008 3z"></path>
</svg>
      <svg title="Search" aria-label="Search" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-search js-jump-to-octicon-search d-none flex-shrink-0">
    <path fill-rule="evenodd" d="M11.5 7a4.499 4.499 0 11-8.998 0A4.499 4.499 0 0111.5 7zm-.82 4.74a6 6 0 111.06-1.06l3.04 3.04a.75.75 0 11-1.06 1.06l-3.04-3.04z"></path>
</svg>
    </div>

    <img class="avatar mr-2 flex-shrink-0 js-jump-to-suggestion-avatar d-none" alt="" aria-label="Team" src="" width="28" height="28">

    <div class="jump-to-suggestion-name js-jump-to-suggestion-name flex-auto overflow-hidden text-left no-wrap css-truncate css-truncate-target">
    </div>

    <div class="border rounded-1 flex-shrink-0 color-bg-subtle px-1 color-fg-muted ml-1 f6 d-none js-jump-to-badge-search">
      <span class="js-jump-to-badge-search-text-default d-none" aria-label="in this repository">
        In this repository
      </span>
      <span class="js-jump-to-badge-search-text-global d-none" aria-label="in all of GitHub">
        All GitHub
      </span>
      <span aria-hidden="true" class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>

    <div aria-hidden="true" class="border rounded-1 flex-shrink-0 color-bg-subtle px-1 color-fg-muted ml-1 f6 d-none d-on-nav-focus js-jump-to-badge-jump">
      Jump to
      <span class="d-inline-block ml-1 v-align-middle">↵</span>
    </div>
  </a>
</li>


</ul>

          </div>
      </label>
</form>  </div>
</div>

          </div>

        <div class="position-relative mr-3 mb-4 mb-lg-0 d-inline-block">
          <a href="/login?return_to=https%3A%2F%2Fgithub.com%2FRkatsiteli%2FVue-archive%2Fblob%2Fmaster%2F6%2520%25E4%25BD%258D%25E6%2595%25B0%25E5%25AF%2586%25E7%25A0%2581%25E8%25BE%2593%25E5%2585%25A5%25E6%25A1%2586%25EF%25BC%2588%25E4%25BB%25BF%25E7%2585%25A7%25E6%2594%25AF%25E4%25BB%2598%25E5%25AE%259D%25EF%25BC%2589%2Fpwd%2Fimg%2Fblink.gif"
            class="HeaderMenu-link flex-shrink-0 no-underline"
            data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="12ee292989e24cf45cfe27097a2e428f3d23e0370a729a9093abfe3b788b38d6"
            data-ga-click="(Logged out) Header, clicked Sign in, text:sign-in">
            Sign in
          </a>
        </div>

          <a href="/signup?ref_cta=Sign+up&amp;ref_loc=header+logged+out&amp;ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E%2Fblob%2Fshow&amp;source=header-repo&amp;source_repo=Rkatsiteli%2FVue-archive"
            class="HeaderMenu-link flex-shrink-0 d-inline-block no-underline border color-border-default rounded px-2 py-1"
            data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;site header menu&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;SIGN_UP&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="12ee292989e24cf45cfe27097a2e428f3d23e0370a729a9093abfe3b788b38d6"
            data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Sign up&quot;,&quot;action&quot;:&quot;click to sign up for account&quot;,&quot;label&quot;:&quot;ref_page:/&lt;user-name&gt;/&lt;repo-name&gt;/blob/show;ref_cta:Sign up;ref_loc:header logged out&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="091393582ba335bd174faf72ea58aa146967484f081ab66995ec35767c95ab1c" data-analytics-event="{&quot;category&quot;:&quot;Sign up&quot;,&quot;action&quot;:&quot;click to sign up for account&quot;,&quot;label&quot;:&quot;ref_page:/&lt;user-name&gt;/&lt;repo-name&gt;/blob/show;ref_cta:Sign up;ref_loc:header logged out&quot;}"
          >
            Sign up
          </a>
      </div>
    </div>
  </div>
</header>

    </div>

  <div id="start-of-content" class="show-on-focus"></div>







    <div data-pjax-replace id="js-flash-container">


  <template class="js-flash-template">
    <div class="flash flash-full  {{ className }}">
  <div class="px-2" >
    <button class="flash-close js-flash-close" type="button" aria-label="Dismiss this message">
      <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x">
    <path fill-rule="evenodd" d="M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"></path>
</svg>
    </button>
    
      <div>{{ message }}</div>

  </div>
</div>
  </template>
</div>


    

  <include-fragment class="js-notification-shelf-include-fragment" data-base-src="https://github.com/notifications/beta/shelf"></include-fragment>





  <div
    class="application-main "
    data-commit-hovercards-enabled
    data-discussion-hovercards-enabled
    data-issue-and-pr-hovercards-enabled
  >
        <div itemscope itemtype="http://schema.org/SoftwareSourceCode" class="">
    <main id="js-repo-pjax-container" data-pjax-container >
      

    






  <div id="repository-container-header" class="pt-3 hide-full-screen mb-5" style="background-color: var(--color-page-header-bg);" data-pjax-replace>

      <div class="d-flex mb-3 px-3 px-md-4 px-lg-5">

        <div class="flex-auto min-width-0 width-fit mr-3">
            <h1 class=" d-flex flex-wrap flex-items-center wb-break-word f3 text-normal">
    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-repo color-fg-muted mr-2">
    <path fill-rule="evenodd" d="M2 2.5A2.5 2.5 0 014.5 0h8.75a.75.75 0 01.75.75v12.5a.75.75 0 01-.75.75h-2.5a.75.75 0 110-1.5h1.75v-2h-8a1 1 0 00-.714 1.7.75.75 0 01-1.072 1.05A2.495 2.495 0 012 11.5v-9zm10.5-1V9h-8c-.356 0-.694.074-1 .208V2.5a1 1 0 011-1h8zM5 12.25v3.25a.25.25 0 00.4.2l1.45-1.087a.25.25 0 01.3 0L8.6 15.7a.25.25 0 00.4-.2v-3.25a.25.25 0 00-.25-.25h-3.5a.25.25 0 00-.25.25z"></path>
</svg>
  <span class="author flex-self-stretch" itemprop="author">
    <a class="url fn" rel="author" data-hovercard-type="user" data-hovercard-url="/users/Rkatsiteli/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/Rkatsiteli">Rkatsiteli</a>
  </span>
  <span class="mx-1 flex-self-stretch color-fg-muted">/</span>
  <strong itemprop="name" class="mr-2 flex-self-stretch">
    <a data-pjax="#repo-content-pjax-container" href="/Rkatsiteli/Vue-archive">Vue-archive</a>
  </strong>

  <span></span><span class="Label Label--secondary v-align-middle mr-1">Public</span>
</h1>

        </div>

          <ul class="pagehead-actions flex-shrink-0 d-none d-md-inline" style="padding: 2px 0;">

  

  <li>
      <a href="/login?return_to=%2FRkatsiteli%2FVue-archive" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;notification subscription menu watch&quot;,&quot;repository_id&quot;:null,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="eb38e5fbe63580e97043b5df03f09df536b9589892389dccc32f2cb39494ab4f" aria-label="You must be signed in to change notification settings" data-view-component="true" class="tooltipped tooltipped-s btn-sm btn">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-bell mr-2">
    <path d="M8 16a2 2 0 001.985-1.75c.017-.137-.097-.25-.235-.25h-3.5c-.138 0-.252.113-.235.25A2 2 0 008 16z"></path><path fill-rule="evenodd" d="M8 1.5A3.5 3.5 0 004.5 5v2.947c0 .346-.102.683-.294.97l-1.703 2.556a.018.018 0 00-.003.01l.001.006c0 .***************.006a.017.017 0 00.006.004l.007.001h10.964l.007-.001a.016.016 0 00.006-.004.016.016 0 00.004-.006l.001-.007a.017.017 0 00-.003-.01l-1.703-2.554a1.75 1.75 0 01-.294-.97V5A3.5 3.5 0 008 1.5zM3 5a5 5 0 0110 0v2.947c0 .**************.139l1.703 2.555A1.518 1.518 0 0113.482 13H2.518a1.518 1.518 0 01-1.263-2.36l1.703-2.554A.25.25 0 003 7.947V5z"></path>
</svg>Notifications
</a>
  </li>

  <li>
        <a href="/login?return_to=%2FRkatsiteli%2FVue-archive" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;repo details fork button&quot;,&quot;repository_id&quot;:*********,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="29a225e160379fd488652d5cf32561acd5cd6aef6ac70ca8af77571092f84cc3" aria-label="You must be signed in to fork a repository" data-view-component="true" class="tooltipped tooltipped-s btn-sm btn">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-repo-forked mr-2">
    <path fill-rule="evenodd" d="M5 3.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm0 2.122a2.25 2.25 0 10-1.5 0v.878A2.25 2.25 0 005.75 8.5h1.5v2.128a2.251 2.251 0 101.5 0V8.5h1.5a2.25 2.25 0 002.25-2.25v-.878a2.25 2.25 0 10-1.5 0v.878a.75.75 0 01-.75.75h-4.5A.75.75 0 015 6.25v-.878zm3.75 7.378a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm3-8.75a.75.75 0 100-********* 0 000 1.5z"></path>
</svg>Fork
    <span id="repo-network-counter" data-pjax-replace="true" title="65" data-view-component="true" class="Counter">65</span>
</a>
  </li>

  <li>
        <div data-view-component="true" class="BtnGroup d-flex">
      <a href="/login?return_to=%2FRkatsiteli%2FVue-archive" rel="nofollow" data-hydro-click="{&quot;event_type&quot;:&quot;authentication.click&quot;,&quot;payload&quot;:{&quot;location_in_page&quot;:&quot;star button&quot;,&quot;repository_id&quot;:*********,&quot;auth_type&quot;:&quot;LOG_IN&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="7a2e68f6202b3d84f282cf3dbb8ef16bc83716e994562459e7033f0cfa2e5924" aria-label="You must be signed in to star a repository" data-view-component="true" class="tooltipped tooltipped-s btn-sm btn BtnGroup-item">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-star v-align-text-bottom d-inline-block mr-2">
    <path fill-rule="evenodd" d="M8 .25a.75.75 0 01.673.418l1.882 3.815 4.21.612a.75.75 0 01.416 1.279l-3.046 2.97.719 4.192a.75.75 0 01-1.088.791L8 12.347l-3.766 1.98a.75.75 0 01-1.088-.79l.72-4.194L.818 6.374a.75.75 0 01.416-1.28l4.21-.611L7.327.668A.75.75 0 018 .25zm0 2.445L6.615 5.5a.75.75 0 01-.564.41l-3.097.45 2.24 2.184a.75.75 0 01.216.664l-.528 3.084 2.769-1.456a.75.75 0 01.698 0l2.77 1.456-.53-3.084a.75.75 0 01.216-.664l2.24-2.183-3.096-.45a.75.75 0 01-.564-.41L8 2.694v.001z"></path>
</svg><span data-view-component="true" class="d-inline">
          Star
</span>          <span id="repo-stars-counter-star" aria-label="40 users starred this repository" data-singular-suffix="user starred this repository" data-plural-suffix="users starred this repository" data-pjax-replace="true" title="40" data-view-component="true" class="Counter js-social-count">40</span>
</a>      <button disabled="disabled" aria-label="You must be signed in to add this repository to a list" type="button" data-view-component="true" class="btn-sm btn BtnGroup-item px-2">  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-triangle-down">
    <path d="M4.427 7.427l3.396 3.396a.25.25 0 00.354 0l3.396-3.396A.25.25 0 0011.396 7H4.604a.25.25 0 00-.177.427z"></path>
</svg>
</button></div>
  </li>

  <li>
    

  </li>
</ul>

      </div>

      <div id="responsive-meta-container" data-pjax-replace>
</div>


        
<nav data-pjax="#js-repo-pjax-container" aria-label="Repository" data-view-component="true" class="js-repo-nav js-sidenav-container-pjax js-responsive-underlinenav overflow-hidden UnderlineNav px-3 px-md-4 px-lg-5">

  <ul data-view-component="true" class="UnderlineNav-body list-style-none">
      <li data-view-component="true" class="d-inline-flex">
  <a id="code-tab" href="/Rkatsiteli/Vue-archive" data-tab-item="i0code-tab" data-selected-links="repo_source repo_downloads repo_commits repo_releases repo_tags repo_branches repo_packages repo_deployments /Rkatsiteli/Vue-archive" data-pjax="#repo-content-pjax-container" data-hotkey="g c" data-ga-click="Repository, Navigation click, Code tab" aria-current="page" data-view-component="true" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item selected">
    
                  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-code UnderlineNav-octicon d-none d-sm-inline">
    <path fill-rule="evenodd" d="M4.72 3.22a.75.75 0 011.06 1.06L2.06 8l3.72 3.72a.75.75 0 11-1.06 1.06L.47 8.53a.75.75 0 010-1.06l4.25-4.25zm6.56 0a.75.75 0 10-1.06 1.06L13.94 8l-3.72 3.72a.75.75 0 101.06 1.06l4.25-4.25a.75.75 0 000-1.06l-4.25-4.25z"></path>
</svg>
          <span data-content="Code">Code</span>
            <span id="code-repo-tab-count" data-pjax-replace="" title="Not available" data-view-component="true" class="Counter"></span>


    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="issues-tab" href="/Rkatsiteli/Vue-archive/issues" data-tab-item="i1issues-tab" data-selected-links="repo_issues repo_labels repo_milestones /Rkatsiteli/Vue-archive/issues" data-pjax="#repo-content-pjax-container" data-hotkey="g i" data-ga-click="Repository, Navigation click, Issues tab" data-view-component="true" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
                  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-issue-opened UnderlineNav-octicon d-none d-sm-inline">
    <path d="M8 9.5a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path><path fill-rule="evenodd" d="M8 0a8 8 0 100 16A8 8 0 008 0zM1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0z"></path>
</svg>
          <span data-content="Issues">Issues</span>
            <span id="issues-repo-tab-count" data-pjax-replace="" title="2" data-view-component="true" class="Counter">2</span>


    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="pull-requests-tab" href="/Rkatsiteli/Vue-archive/pulls" data-tab-item="i2pull-requests-tab" data-selected-links="repo_pulls checks /Rkatsiteli/Vue-archive/pulls" data-pjax="#repo-content-pjax-container" data-hotkey="g p" data-ga-click="Repository, Navigation click, Pull requests tab" data-view-component="true" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
                  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-git-pull-request UnderlineNav-octicon d-none d-sm-inline">
    <path fill-rule="evenodd" d="M7.177 3.073L9.573.677A.25.25 0 0110 .854v4.792a.25.25 0 01-.427.177L7.177 3.427a.25.25 0 010-.354zM3.75 2.5a.75.75 0 100 ********* 0 000-1.5zm-2.25.75a2.25 2.25 0 113 2.122v5.256a2.251 2.251 0 11-1.5 0V5.372A2.25 2.25 0 011.5 3.25zM11 2.5h-1V4h1a1 1 0 011 1v5.628a2.251 2.251 0 101.5 0V5A2.5 2.5 0 0011 2.5zm1 10.25a.75.75 0 111.5 0 .75.75 0 01-1.5 0zM3.75 12a.75.75 0 100 ********* 0 000-1.5z"></path>
</svg>
          <span data-content="Pull requests">Pull requests</span>
            <span id="pull-requests-repo-tab-count" data-pjax-replace="" title="1" data-view-component="true" class="Counter">1</span>


    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="actions-tab" href="/Rkatsiteli/Vue-archive/actions" data-tab-item="i3actions-tab" data-selected-links="repo_actions /Rkatsiteli/Vue-archive/actions" data-pjax="#repo-content-pjax-container" data-hotkey="g a" data-ga-click="Repository, Navigation click, Actions tab" data-view-component="true" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
                  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-play UnderlineNav-octicon d-none d-sm-inline">
    <path fill-rule="evenodd" d="M1.5 8a6.5 6.5 0 1113 0 6.5 6.5 0 01-13 0zM8 0a8 8 0 100 16A8 8 0 008 0zM6.379 5.227A.25.25 0 006 5.442v5.117a.25.25 0 00.379.214l4.264-2.559a.25.25 0 000-.428L6.379 5.227z"></path>
</svg>
          <span data-content="Actions">Actions</span>
            <span id="actions-repo-tab-count" data-pjax-replace="" title="Not available" data-view-component="true" class="Counter"></span>


    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="projects-tab" href="/Rkatsiteli/Vue-archive/projects?type=beta" data-tab-item="i4projects-tab" data-selected-links="repo_projects new_repo_project repo_project /Rkatsiteli/Vue-archive/projects?type=beta" data-pjax="#repo-content-pjax-container" data-hotkey="g b" data-ga-click="Repository, Navigation click, Projects tab" data-view-component="true" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
                  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-table UnderlineNav-octicon d-none d-sm-inline">
    <path fill-rule="evenodd" d="M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v3.585a.746.746 0 010 .83v8.085A1.75 1.75 0 0114.25 16H6.309a.748.748 0 01-1.118 0H1.75A1.75 1.75 0 010 14.25V6.165a.746.746 0 010-.83V1.75zM1.5 6.5v7.75c0 .*************.25H5v-8H1.5zM5 5H1.5V1.75a.25.25 0 01.25-.25H5V5zm1.5 1.5v8h7.75a.25.25 0 00.25-.25V6.5h-8zm8-1.5h-8V1.5h7.75a.25.25 0 01.25.25V5z"></path>
</svg>
          <span data-content="Projects">Projects</span>
            <span id="projects-repo-tab-count" data-pjax-replace="" title="0" hidden="hidden" data-view-component="true" class="Counter">0</span>


    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="wiki-tab" href="/Rkatsiteli/Vue-archive/wiki" data-tab-item="i5wiki-tab" data-selected-links="repo_wiki /Rkatsiteli/Vue-archive/wiki" data-pjax="#repo-content-pjax-container" data-hotkey="g w" data-ga-click="Repository, Navigation click, Wikis tab" data-view-component="true" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
                  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-book UnderlineNav-octicon d-none d-sm-inline">
    <path fill-rule="evenodd" d="M0 1.75A.75.75 0 01.75 1h4.253c1.227 0 2.317.59 3 1.501A3.744 3.744 0 0111.006 1h4.245a.75.75 0 01.75.75v10.5a.75.75 0 01-.75.75h-4.507a2.25 2.25 0 00-1.591.659l-.622.621a.75.75 0 01-1.06 0l-.622-.621A2.25 2.25 0 005.258 13H.75a.75.75 0 01-.75-.75V1.75zm8.755 3a2.25 2.25 0 012.25-2.25H14.5v9h-3.757c-.71 0-1.4.201-1.992.572l.004-7.322zm-1.504 7.324l.004-5.073-.002-2.253A2.25 2.25 0 005.003 2.5H1.5v9h3.757a3.75 3.75 0 011.994.574z"></path>
</svg>
          <span data-content="Wiki">Wiki</span>
            <span id="wiki-repo-tab-count" data-pjax-replace="" title="Not available" data-view-component="true" class="Counter"></span>


    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="security-tab" href="/Rkatsiteli/Vue-archive/security" data-tab-item="i6security-tab" data-selected-links="security overview alerts policy token_scanning code_scanning /Rkatsiteli/Vue-archive/security" data-pjax="#repo-content-pjax-container" data-hotkey="g s" data-ga-click="Repository, Navigation click, Security tab" data-view-component="true" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
                  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-shield UnderlineNav-octicon d-none d-sm-inline">
    <path fill-rule="evenodd" d="M7.467.133a1.75 1.75 0 011.066 0l5.25 1.68A1.75 1.75 0 0115 3.48V7c0 1.566-.32 3.182-1.303 4.682-.983 1.498-2.585 2.813-5.032 3.855a1.7 1.7 0 01-1.33 0c-2.447-1.042-4.049-2.357-5.032-3.855C1.32 10.182 1 8.566 1 7V3.48a1.75 1.75 0 011.217-1.667l5.25-1.68zm.61 1.429a.25.25 0 00-.153 0l-5.25 1.68a.25.25 0 00-.174.238V7c0 1.358.275 2.666 1.057 3.86.784 1.194 2.121 2.34 4.366 3.297a.2.2 0 00.154 0c2.245-.956 3.582-2.104 4.366-3.298C13.225 9.666 13.5 8.36 13.5 7V3.48a.25.25 0 00-.174-.237l-5.25-1.68zM9 10.5a1 1 0 11-2 0 1 1 0 012 0zm-.25-5.75a.75.75 0 10-1.5 0v3a.75.75 0 001.5 0v-3z"></path>
</svg>
          <span data-content="Security">Security</span>
            <include-fragment src="/Rkatsiteli/Vue-archive/security/overall-count" accept="text/fragment+html"></include-fragment>

    
</a></li>
      <li data-view-component="true" class="d-inline-flex">
  <a id="insights-tab" href="/Rkatsiteli/Vue-archive/pulse" data-tab-item="i7insights-tab" data-selected-links="repo_graphs repo_contributors dependency_graph dependabot_updates pulse people community /Rkatsiteli/Vue-archive/pulse" data-pjax="#repo-content-pjax-container" data-ga-click="Repository, Navigation click, Insights tab" data-view-component="true" class="UnderlineNav-item hx_underlinenav-item no-wrap js-responsive-underlinenav-item js-selected-navigation-item">
    
                  <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-graph UnderlineNav-octicon d-none d-sm-inline">
    <path fill-rule="evenodd" d="M1.5 1.75a.75.75 0 00-1.5 0v12.5c0 .414.336.75.75.75h14.5a.75.75 0 000-1.5H1.5V1.75zm14.28 2.53a.75.75 0 00-1.06-1.06L10 7.94 7.53 5.47a.75.75 0 00-1.06 0L3.22 8.72a.75.75 0 001.06 1.06L7 7.06l2.47 2.47a.75.75 0 001.06 0l5.25-5.25z"></path>
</svg>
          <span data-content="Insights">Insights</span>
            <span id="insights-repo-tab-count" data-pjax-replace="" title="Not available" data-view-component="true" class="Counter"></span>


    
</a></li>
</ul>
    <div style="visibility:hidden;" data-view-component="true" class="UnderlineNav-actions js-responsive-underlinenav-overflow position-absolute pr-3 pr-md-4 pr-lg-5 right-0">      <details data-view-component="true" class="details-overlay details-reset position-relative">
  <summary role="button" data-view-component="true">          <div class="UnderlineNav-item mr-0 border-0">
            <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-kebab-horizontal">
    <path d="M8 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zM1.5 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm13 0a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
</svg>
            <span class="sr-only">More</span>
          </div>
</summary>
  <div data-view-component="true">          <details-menu role="menu" data-view-component="true" class="dropdown-menu dropdown-menu-sw">
  
            <ul>
                <li data-menu-item="i0code-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item selected dropdown-item" aria-current="page" data-selected-links="repo_source repo_downloads repo_commits repo_releases repo_tags repo_branches repo_packages repo_deployments /Rkatsiteli/Vue-archive" href="/Rkatsiteli/Vue-archive">
                    Code
</a>                </li>
                <li data-menu-item="i1issues-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links="repo_issues repo_labels repo_milestones /Rkatsiteli/Vue-archive/issues" href="/Rkatsiteli/Vue-archive/issues">
                    Issues
</a>                </li>
                <li data-menu-item="i2pull-requests-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links="repo_pulls checks /Rkatsiteli/Vue-archive/pulls" href="/Rkatsiteli/Vue-archive/pulls">
                    Pull requests
</a>                </li>
                <li data-menu-item="i3actions-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links="repo_actions /Rkatsiteli/Vue-archive/actions" href="/Rkatsiteli/Vue-archive/actions">
                    Actions
</a>                </li>
                <li data-menu-item="i4projects-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links="repo_projects new_repo_project repo_project /Rkatsiteli/Vue-archive/projects?type=beta" href="/Rkatsiteli/Vue-archive/projects?type=beta">
                    Projects
</a>                </li>
                <li data-menu-item="i5wiki-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links="repo_wiki /Rkatsiteli/Vue-archive/wiki" href="/Rkatsiteli/Vue-archive/wiki">
                    Wiki
</a>                </li>
                <li data-menu-item="i6security-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links="security overview alerts policy token_scanning code_scanning /Rkatsiteli/Vue-archive/security" href="/Rkatsiteli/Vue-archive/security">
                    Security
</a>                </li>
                <li data-menu-item="i7insights-tab" hidden>
                  <a role="menuitem" class="js-selected-navigation-item dropdown-item" data-selected-links="repo_graphs repo_contributors dependency_graph dependabot_updates pulse people community /Rkatsiteli/Vue-archive/pulse" href="/Rkatsiteli/Vue-archive/pulse">
                    Insights
</a>                </li>
            </ul>

</details-menu></div>
</details></div>
</nav>
  </div>



<div class="clearfix new-discussion-timeline container-xl px-3 px-md-4 px-lg-5">
  <div id="repo-content-pjax-container" class="repository-content " >

    


    
      
  
  
<div>
  



    <a class="d-none js-permalink-shortcut" data-hotkey="y" href="/Rkatsiteli/Vue-archive/blob/445fa649abee779ac9b63f24d2473bdb7b804324/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif">Permalink</a>


    <div class="d-flex flex-items-start flex-shrink-0 pb-3 flex-wrap flex-md-nowrap flex-justify-between flex-md-justify-start">
      
<div class="position-relative">
  <details class="details-reset details-overlay mr-0 mb-0 " id="branch-select-menu">
    <summary class="btn css-truncate"
            data-hotkey="w"
            title="Switch branches or tags">
      <svg text="gray" aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-git-branch">
    <path fill-rule="evenodd" d="M11.75 2.5a.75.75 0 100 ********* 0 000-1.5zm-2.25.75a2.25 2.25 0 113 2.122V6A2.5 2.5 0 0110 8.5H6a1 1 0 00-1 1v1.128a2.251 2.251 0 11-1.5 0V5.372a2.25 2.25 0 111.5 0v1.836A2.492 2.492 0 016 7h4a1 1 0 001-1v-.628A2.25 2.25 0 019.5 3.25zM4.25 12a.75.75 0 100 ********* 0 000-1.5zM3.5 3.25a.75.75 0 111.5 0 .75.75 0 01-1.5 0z"></path>
</svg>
      <span class="css-truncate-target" data-menu-button>master</span>
      <span class="dropdown-caret"></span>
    </summary>

      
<div class="SelectMenu">
  <div class="SelectMenu-modal">
    <header class="SelectMenu-header">
      <span class="SelectMenu-title">Switch branches/tags</span>
      <button class="SelectMenu-closeButton" type="button" data-toggle-for="branch-select-menu"><svg aria-label="Close menu" aria-hidden="false" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x">
    <path fill-rule="evenodd" d="M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"></path>
</svg></button>
    </header>

    <input-demux data-action="tab-container-change:input-demux#storeInput tab-container-changed:input-demux#updateInput">
      <tab-container class="d-flex flex-column js-branches-tags-tabs" style="min-height: 0;">
        <div class="SelectMenu-filter">
          <input data-target="input-demux.source"
                 id="context-commitish-filter-field"
                 class="SelectMenu-input form-control"
                 aria-owns="ref-list-branches"
                 data-controls-ref-menu-id="ref-list-branches"
                 autofocus
                 autocomplete="off"
                 aria-label="Filter branches/tags"
                 placeholder="Filter branches/tags"
                 type="text"
          >
        </div>

        <div class="SelectMenu-tabs" role="tablist" data-target="input-demux.control" >
          <button class="SelectMenu-tab" type="button" role="tab" aria-selected="true">Branches</button>
          <button class="SelectMenu-tab" type="button" role="tab">Tags</button>
        </div>

        <div role="tabpanel" id="ref-list-branches" data-filter-placeholder="Filter branches/tags" tabindex="" class="d-flex flex-column flex-auto overflow-auto">
          <ref-selector
            type="branch"
            data-targets="input-demux.sinks"
            data-action="
              input-entered:ref-selector#inputEntered
              tab-selected:ref-selector#tabSelected
              focus-list:ref-selector#focusFirstListMember
            "
            query-endpoint="/Rkatsiteli/Vue-archive/refs"
            
            cache-key="v0:1519556005.0"
            current-committish="bWFzdGVy"
            default-branch="bWFzdGVy"
            name-with-owner="UmthdHNpdGVsaS9WdWUtYXJjaGl2ZQ=="
            prefetch-on-mouseover
          >

            <template data-target="ref-selector.fetchFailedTemplate">
              <div class="SelectMenu-message" data-index="{{ index }}">Could not load branches</div>
            </template>

              <template data-target="ref-selector.noMatchTemplate">
    <div class="SelectMenu-message">Nothing to show</div>
</template>


            <!-- TODO: this max-height is necessary or else the branch list won't scroll.  why? -->
            <div data-target="ref-selector.listContainer" role="menu" class="SelectMenu-list " style="max-height: 330px" data-pjax="#repo-content-pjax-container">
              <div class="SelectMenu-loading pt-3 pb-0 overflow-hidden" aria-label="Menu is loading">
                <svg style="box-sizing: content-box; color: var(--color-icon-primary);" width="32" height="32" viewBox="0 0 16 16" fill="none" data-view-component="true" class="anim-rotate">
  <circle cx="8" cy="8" r="7" stroke="currentColor" stroke-opacity="0.25" stroke-width="2" vector-effect="non-scaling-stroke" />
  <path d="M15 8a7.002 7.002 0 00-7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" vector-effect="non-scaling-stroke" />
</svg>
              </div>
            </div>

              <template data-target="ref-selector.itemTemplate">
  <a href="https://github.com/Rkatsiteli/Vue-archive/blob/{{ urlEncodedRefName }}/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif" class="SelectMenu-item" role="menuitemradio" rel="nofollow" aria-checked="{{ isCurrent }}" data-index="{{ index }}">
    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-check SelectMenu-icon SelectMenu-icon--check">
    <path fill-rule="evenodd" d="M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z"></path>
</svg>
    <span class="flex-1 css-truncate css-truncate-overflow {{ isFilteringClass }}">{{ refName }}</span>
    <span hidden="{{ isNotDefault }}" class="Label Label--secondary flex-self-start">default</span>
  </a>
</template>


              <footer class="SelectMenu-footer"><a href="/Rkatsiteli/Vue-archive/branches">View all branches</a></footer>
          </ref-selector>

        </div>

        <div role="tabpanel" id="tags-menu" data-filter-placeholder="Find a tag" tabindex="" hidden class="d-flex flex-column flex-auto overflow-auto">
          <ref-selector
            type="tag"
            data-action="
              input-entered:ref-selector#inputEntered
              tab-selected:ref-selector#tabSelected
              focus-list:ref-selector#focusFirstListMember
            "
            data-targets="input-demux.sinks"
            query-endpoint="/Rkatsiteli/Vue-archive/refs"
            cache-key="v0:1519556005.0"
            current-committish="bWFzdGVy"
            default-branch="bWFzdGVy"
            name-with-owner="UmthdHNpdGVsaS9WdWUtYXJjaGl2ZQ=="
          >

            <template data-target="ref-selector.fetchFailedTemplate">
              <div class="SelectMenu-message" data-index="{{ index }}">Could not load tags</div>
            </template>

            <template data-target="ref-selector.noMatchTemplate">
              <div class="SelectMenu-message" data-index="{{ index }}">Nothing to show</div>
            </template>

              <template data-target="ref-selector.itemTemplate">
  <a href="https://github.com/Rkatsiteli/Vue-archive/blob/{{ urlEncodedRefName }}/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif" class="SelectMenu-item" role="menuitemradio" rel="nofollow" aria-checked="{{ isCurrent }}" data-index="{{ index }}">
    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-check SelectMenu-icon SelectMenu-icon--check">
    <path fill-rule="evenodd" d="M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z"></path>
</svg>
    <span class="flex-1 css-truncate css-truncate-overflow {{ isFilteringClass }}">{{ refName }}</span>
    <span hidden="{{ isNotDefault }}" class="Label Label--secondary flex-self-start">default</span>
  </a>
</template>


            <div data-target="ref-selector.listContainer" role="menu" class="SelectMenu-list" style="max-height: 330px" data-pjax="#repo-content-pjax-container">
              <div class="SelectMenu-loading pt-3 pb-0 overflow-hidden" aria-label="Menu is loading">
                <svg style="box-sizing: content-box; color: var(--color-icon-primary);" width="32" height="32" viewBox="0 0 16 16" fill="none" data-view-component="true" class="anim-rotate">
  <circle cx="8" cy="8" r="7" stroke="currentColor" stroke-opacity="0.25" stroke-width="2" vector-effect="non-scaling-stroke" />
  <path d="M15 8a7.002 7.002 0 00-7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" vector-effect="non-scaling-stroke" />
</svg>
              </div>
            </div>
              <footer class="SelectMenu-footer"><a href="/Rkatsiteli/Vue-archive/tags">View all tags</a></footer>
          </ref-selector>
        </div>
      </tab-container>
    </input-demux>
  </div>
</div>

  </details>

</div>

      <h2 id="blob-path" class="breadcrumb flex-auto flex-self-center min-width-0 text-normal mx-2 width-full width-md-auto flex-order-1 flex-md-order-none mt-3 mt-md-0">
        <span class="js-repo-root text-bold"><span class="js-path-segment d-inline-block wb-break-all"><a data-pjax="#repo-content-pjax-container" href="/Rkatsiteli/Vue-archive"><span>Vue-archive</span></a></span></span><span class="separator">/</span><span class="js-path-segment d-inline-block wb-break-all"><a data-pjax="#repo-content-pjax-container" href="/Rkatsiteli/Vue-archive/tree/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89"><span>6 位数密码输入框（仿照支付宝）</span></a></span><span class="separator">/</span><span class="js-path-segment d-inline-block wb-break-all"><a data-pjax="#repo-content-pjax-container" href="/Rkatsiteli/Vue-archive/tree/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd"><span>pwd</span></a></span><span class="separator">/</span><span class="js-path-segment d-inline-block wb-break-all"><a data-pjax="#repo-content-pjax-container" href="/Rkatsiteli/Vue-archive/tree/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img"><span>img</span></a></span><span class="separator">/</span><strong class="final-path">blink.gif</strong>
      </h2>
      <a href="/Rkatsiteli/Vue-archive/find/master"
            class="js-pjax-capture-input btn mr-2 d-none d-md-block"
            data-pjax
            data-hotkey="t">
        Go to file
      </a>

      <details id="blob-more-options-details" data-view-component="true" class="details-overlay details-reset position-relative">
  <summary role="button" data-view-component="true" class="btn">  <svg aria-label="More options" role="img" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-kebab-horizontal">
    <path d="M8 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zM1.5 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm13 0a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
</svg>
</summary>
  <div data-view-component="true">          <ul class="dropdown-menu dropdown-menu-sw">
            <li class="d-block d-md-none">
              <a class="dropdown-item d-flex flex-items-baseline" data-hydro-click="{&quot;event_type&quot;:&quot;repository.click&quot;,&quot;payload&quot;:{&quot;target&quot;:&quot;FIND_FILE_BUTTON&quot;,&quot;repository_id&quot;:*********,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="711acea2ce26c2714ea47b4d28d6ea9689cb48a543d67c83e406a7413f68f31d" data-ga-click="Repository, find file, location:repo overview" data-hotkey="t" data-pjax="true" href="/Rkatsiteli/Vue-archive/find/master">
                <span class="flex-auto">Go to file</span>
                <span class="text-small color-fg-muted" aria-hidden="true">T</span>
</a>            </li>
            <li data-toggle-for="blob-more-options-details">
              <button data-toggle-for="jumpto-line-details-dialog" type="button" data-view-component="true" class="dropdown-item btn-link">  <span class="d-flex flex-items-baseline">
                  <span class="flex-auto">Go to line</span>
                  <span class="text-small color-fg-muted" aria-hidden="true">L</span>
                </span>
</button>            </li>
            <li class="dropdown-divider" role="none"></li>
            <li>
              <clipboard-copy data-toggle-for="blob-more-options-details" aria-label="Copy path" value="6 位数密码输入框（仿照支付宝）/pwd/img/blink.gif" data-view-component="true" class="dropdown-item cursor-pointer">
    
                Copy path

</clipboard-copy>            </li>
            <li>
              <clipboard-copy data-toggle-for="blob-more-options-details" aria-label="Copy permalink" value="https://github.com/Rkatsiteli/Vue-archive/blob/445fa649abee779ac9b63f24d2473bdb7b804324/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif" data-view-component="true" class="dropdown-item cursor-pointer">
    
                <span class="d-flex flex-items-baseline">
                  <span class="flex-auto">Copy permalink</span>
                </span>

</clipboard-copy>            </li>
          </ul>
</div>
</details>    </div>




    <div id="spoof-warning" class="mt-0 pb-3" hidden aria-hidden>
  <div data-view-component="true" class="flash flash-warn mt-0 clearfix">
  
  
    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-alert float-left mt-1">
    <path fill-rule="evenodd" d="M8.22 1.754a.25.25 0 00-.44 0L1.698 13.132a.25.25 0 00.22.368h12.164a.25.25 0 00.22-.368L8.22 1.754zm-1.763-.707c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0114.082 15H1.918a1.75 1.75 0 01-1.543-2.575L6.457 1.047zM9 11a1 1 0 11-2 0 1 1 0 012 0zm-.25-5.25a.75.75 0 00-1.5 0v2.5a.75.75 0 001.5 0v-2.5z"></path>
</svg>

      <div class="overflow-hidden">This commit does not belong to any branch on this repository, and may belong to a fork outside of the repository.</div>


  
</div></div>

    <include-fragment src="/Rkatsiteli/Vue-archive/spoofed_commit_check/445fa649abee779ac9b63f24d2473bdb7b804324" data-test-selector="spoofed-commit-check"></include-fragment>

    <div class="Box d-flex flex-column flex-shrink-0 mb-3">
      
  <div class="Box-header Details js-details-container">
      <div class="d-flex flex-items-center">
        <span class="flex-shrink-0 ml-n1 mr-n1 mt-n1 mb-n1">
          <a rel="author" data-skip-pjax="true" data-hovercard-type="user" data-hovercard-url="/users/Rkatsiteli/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/Rkatsiteli"><img class="avatar avatar-user" src="https://avatars.githubusercontent.com/u/16810179?s=48&amp;v=4" width="24" height="24" alt="@Rkatsiteli" /></a>
        </span>
        <div class="flex-1 d-flex flex-items-center ml-3 min-width-0">
          <div class="css-truncate css-truncate-overflow">
            <a class="text-bold Link--primary" rel="author" data-hovercard-type="user" data-hovercard-url="/users/Rkatsiteli/hovercard" data-octo-click="hovercard-link-click" data-octo-dimensions="link_type:self" href="/Rkatsiteli">Rkatsiteli</a>

              <span class="markdown-title">
                <a data-pjax="true" title="Vue 归档" class="Link--secondary" href="/Rkatsiteli/Vue-archive/commit/3c15f481acec8e81ff18bd5f75d4c23c80e4ff81">Vue 归档</a>
              </span>
          </div>


          <span class="ml-2">
            <include-fragment accept="text/fragment+html" src="/Rkatsiteli/Vue-archive/commit/3c15f481acec8e81ff18bd5f75d4c23c80e4ff81/rollup?direction=e" class="d-inline"></include-fragment>
          </span>
        </div>
        <div class="ml-3 d-flex flex-shrink-0 flex-items-center flex-justify-end color-fg-muted no-wrap">
          <span class="d-none d-md-inline">
            <span>Latest commit</span>
            <a class="text-small text-mono Link--secondary" href="/Rkatsiteli/Vue-archive/commit/3c15f481acec8e81ff18bd5f75d4c23c80e4ff81" data-pjax>3c15f48</a>
            <span itemprop="dateModified"><relative-time datetime="2018-02-25T10:53:12Z" class="no-wrap">Feb 25, 2018</relative-time></span>
          </span>

          <a data-pjax href="/Rkatsiteli/Vue-archive/commits/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif" class="ml-3 no-wrap Link--primary no-underline">
            <svg text="gray" aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-history">
    <path fill-rule="evenodd" d="M1.643 3.143L.427 1.927A.25.25 0 000 2.104V5.75c0 .*************.25h3.646a.25.25 0 00.177-.427L2.715 4.215a6.5 6.5 0 11-1.18 4.458.75.75 0 10-1.493.154 8.001 8.001 0 101.6-5.684zM7.75 4a.75.75 0 01.75.75v2.992l2.028.812a.75.75 0 01-.557 1.392l-2.5-1A.75.75 0 017 8.25v-3.5A.75.75 0 017.75 4z"></path>
</svg>
            <span class="d-none d-sm-inline">
              <strong>History</strong>
            </span>
          </a>
        </div>
      </div>

  </div>

  <div class="Box-body d-flex flex-items-center flex-auto border-bottom-0 flex-wrap" >
    <details class="details-reset details-overlay details-overlay-dark lh-default color-fg-default float-left mr-3" id="blob_contributors_box">
      <summary class="Link--primary">
        <svg text="gray" aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-people">
    <path fill-rule="evenodd" d="M5.5 3.5a2 2 0 100 4 2 2 0 000-4zM2 5.5a3.5 3.5 0 115.898 2.549 5.507 5.507 0 013.034 4.084.75.75 0 11-1.482.235 4.001 4.001 0 00-7.9 0 .75.75 0 01-1.482-.236A5.507 5.507 0 013.102 8.05 3.49 3.49 0 012 5.5zM11 4a.75.75 0 100 1.5 1.5 1.5 0 01.666 2.844.75.75 0 00-.416.672v.352a.75.75 0 00.574.73c1.2.289 2.162 1.2 2.522 2.372a.75.75 0 101.434-.44 5.01 5.01 0 00-2.56-3.012A3 3 0 0011 4z"></path>
</svg>
        <strong>1</strong>
        
        contributor
      </summary>
      <details-dialog
        class="Box Box--overlay d-flex flex-column anim-fade-in fast"
        aria-label="Users who have contributed to this file"
        src="/Rkatsiteli/Vue-archive/contributors-list/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif" preload>
        <div class="Box-header">
          <button class="Box-btn-octicon btn-octicon float-right" type="button" aria-label="Close dialog" data-close-dialog>
            <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x">
    <path fill-rule="evenodd" d="M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"></path>
</svg>
          </button>
          <h3 class="Box-title">
            Users who have contributed to this file
          </h3>
        </div>
        <include-fragment>
          <svg style="box-sizing: content-box; color: var(--color-icon-primary);" width="32" height="32" viewBox="0 0 16 16" fill="none" data-view-component="true" class="my-3 mx-auto d-block anim-rotate">
  <circle cx="8" cy="8" r="7" stroke="currentColor" stroke-opacity="0.25" stroke-width="2" vector-effect="non-scaling-stroke" />
  <path d="M15 8a7.002 7.002 0 00-7-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" vector-effect="non-scaling-stroke" />
</svg>
        </include-fragment>
      </details-dialog>
    </details>
  </div>
    </div>






  
    <div data-target="readme-toc.content" class="Box mt-3 position-relative">
      
  <div
    class="Box-header js-blob-header py-2 pr-2 d-flex flex-shrink-0 flex-md-row flex-items-center"
    
  >


  <div class="text-mono f6 flex-auto pr-3 flex-order-2 flex-md-order-1">

    1.14 KB
  </div>

  <div class="d-flex py-1 py-md-0 flex-auto flex-order-1 flex-md-order-2 flex-sm-grow-0 flex-justify-between hide-sm hide-md">
      

    <div class="BtnGroup">
      <a data-permalink-href="/Rkatsiteli/Vue-archive/raw/445fa649abee779ac9b63f24d2473bdb7b804324/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif" href="/Rkatsiteli/Vue-archive/raw/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif" id="raw-url" data-view-component="true" class="js-permalink-replaceable-link btn-sm btn BtnGroup-item">  Download
</a>    </div>

    <div>
          <a class="btn-octicon tooltipped tooltipped-nw js-remove-unless-platform"
             data-platforms="windows,mac"
             href="https://desktop.github.com"
             aria-label="Open this file in GitHub Desktop"
             data-ga-click="Repository, open with desktop">
              <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-device-desktop">
    <path fill-rule="evenodd" d="M1.75 2.5h12.5a.25.25 0 01.25.25v7.5a.25.25 0 01-.25.25H1.75a.25.25 0 01-.25-.25v-7.5a.25.25 0 01.25-.25zM14.25 1H1.75A1.75 1.75 0 000 2.75v7.5C0 11.216.784 12 1.75 12h3.727c-.1 1.041-.52 1.872-1.292 2.757A.75.75 0 004.75 16h6.5a.75.75 0 00.565-1.243c-.772-.885-1.193-1.716-1.292-2.757h3.727A1.75 1.75 0 0016 10.25v-7.5A1.75 1.75 0 0014.25 1zM9.018 12H6.982a5.72 5.72 0 01-.765 2.5h3.566a5.72 5.72 0 01-.765-2.5z"></path>
</svg>
          </a>

          <!-- '"` --><!-- </textarea></xmp> --></option></form><form class="inline-form" action="/Rkatsiteli/Vue-archive/delete/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif" accept-charset="UTF-8" method="post"><input type="hidden" data-csrf="true" name="authenticity_token" value="u8J92KI8YM0SfjtNc0xAkFpVoXDuqP4eMIyqosgE6fF/XxmUEMdzExLbee57E+awYeYa0jKc8mr6r+3nKrc1NQ==" />
            <button class="btn-octicon btn-octicon-danger tooltipped tooltipped-nw" type="submit"
              aria-label="You must be signed in to make or propose changes" data-disable-with>
              <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-trash">
    <path fill-rule="evenodd" d="M6.5 1.75a.25.25 0 01.25-.25h2.5a.25.25 0 01.25.25V3h-3V1.75zm4.5 0V3h2.25a.75.75 0 010 1.5H2.75a.75.75 0 010-1.5H5V1.75C5 .784 5.784 0 6.75 0h2.5C10.216 0 11 .784 11 1.75zM4.496 6.675a.75.75 0 10-1.492.15l.66 6.6A1.75 1.75 0 005.405 15h5.19c.9 0 1.652-.681 1.741-1.576l.66-6.6a.75.75 0 00-1.492-.149l-.66 6.6a.25.25 0 01-.249.225h-5.19a.25.25 0 01-.249-.225l-.66-6.6z"></path>
</svg>
            </button>
</form>    </div>
  </div>

    <div class="d-flex hide-lg hide-xl flex-order-2 flex-grow-0">
      <details class="dropdown details-reset details-overlay d-inline-block">
        <summary class="btn-octicon" aria-haspopup="true" aria-label="possible actions">
          <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-kebab-horizontal">
    <path d="M8 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zM1.5 9a1.5 1.5 0 100-3 1.5 1.5 0 000 3zm13 0a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
</svg>
        </summary>

        <ul class="dropdown-menu dropdown-menu-sw" style="width: 175px">
            <li>
                <a class="dropdown-item tooltipped tooltipped-nw js-remove-unless-platform"
                   data-platforms="windows,mac"
                   href="https://desktop.github.com"
                   data-ga-click="Repository, open with desktop">
                  Open with Desktop
                </a>
            </li>
          <li>
            <a class="dropdown-item" href="/Rkatsiteli/Vue-archive/raw/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif">
              Download
            </a>
          </li>

        </ul>
      </details>
    </div>
</div>


      
  <div itemprop="text" class="Box-body p-0 blob-wrapper data type-text  gist-border-0">

      <div class="text-center p-3">
          <span class="border-wrap"><img src="/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif?raw=true" alt="blink.gif"></span>
      </div>
  </div>

    </div>


  

  <details class="details-reset details-overlay details-overlay-dark" id="jumpto-line-details-dialog">
    <summary data-hotkey="l" aria-label="Jump to line"></summary>
    <details-dialog class="Box Box--overlay d-flex flex-column anim-fade-in fast linejump" aria-label="Jump to line">
      <!-- '"` --><!-- </textarea></xmp> --></option></form><form class="js-jump-to-line-form Box-body d-flex" action="" accept-charset="UTF-8" method="get">
        <input class="form-control flex-auto mr-3 linejump-input js-jump-to-line-field" type="text" placeholder="Jump to line&hellip;" aria-label="Jump to line" autofocus>
        <button data-close-dialog="" type="submit" data-view-component="true" class="btn">  Go
</button>
</form>    </details-dialog>
  </details>


</div>



  </div>
</div>

    </main>
  </div>

  </div>

          <footer class="footer width-full container-xl p-responsive" role="contentinfo">


  <div class="position-relative d-flex flex-items-center pb-2 f6 color-fg-muted border-top color-border-muted flex-column-reverse flex-lg-row flex-wrap flex-lg-nowrap mt-6 pt-6">
    <ul class="list-style-none d-flex flex-wrap col-0 col-lg-2 flex-justify-start flex-lg-justify-between mb-2 mb-lg-0">
      <li class="mt-2 mt-lg-0 d-flex flex-items-center">
        <a aria-label="Homepage" title="GitHub" class="footer-octicon mr-2" href="https://github.com">
          <svg aria-hidden="true" height="24" viewBox="0 0 16 16" version="1.1" width="24" data-view-component="true" class="octicon octicon-mark-github">
    <path fill-rule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 *********.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path>
</svg>
</a>        <span>
        &copy; 2022 GitHub, Inc.
        </span>
      </li>
    </ul>
    <ul class="list-style-none d-flex flex-wrap col-12 col-lg-8 flex-justify-center flex-lg-justify-between mb-2 mb-lg-0">
        <li class="mr-3 mr-lg-0"><a href="https://docs.github.com/en/github/site-policy/github-terms-of-service" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to terms&quot;,&quot;label&quot;:&quot;text:terms&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="c7a3b791f11b99aa2e3b7903fcfd8be2c14178248c2f751d49c08322e4e3128f" data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to terms&quot;,&quot;label&quot;:&quot;text:terms&quot;}">Terms</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://docs.github.com/en/github/site-policy/github-privacy-statement" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to privacy&quot;,&quot;label&quot;:&quot;text:privacy&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="e37ad78050624cf6fa0d4e4506d7e4d6c50534060c4a6fd97d6cc9891d9a026f" data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to privacy&quot;,&quot;label&quot;:&quot;text:privacy&quot;}">Privacy</a></li>
        <li class="mr-3 mr-lg-0"><a data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to security&quot;,&quot;label&quot;:&quot;text:security&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="9b364b9682ff6fea768e10898cec9f4ec80208457a61901157b1808bd4a52409" data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to security&quot;,&quot;label&quot;:&quot;text:security&quot;}" href="https://github.com/security">Security</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://www.githubstatus.com/" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to status&quot;,&quot;label&quot;:&quot;text:status&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="f21d4ac02ddf5e4afcd57ce64d1cf1e07935f125beca30da2c77fb96dd6c7db9" data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to status&quot;,&quot;label&quot;:&quot;text:status&quot;}">Status</a></li>
        <li class="mr-3 mr-lg-0"><a data-ga-click="Footer, go to help, text:Docs" href="https://docs.github.com">Docs</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://support.github.com?tags=dotcom-footer" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to contact&quot;,&quot;label&quot;:&quot;text:contact&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="1a264e25212821d0cf089315d1f6583ebb93176fa397b7d3c40ab1af24f1925d" data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to contact&quot;,&quot;label&quot;:&quot;text:contact&quot;}">Contact GitHub</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://github.com/pricing" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to Pricing&quot;,&quot;label&quot;:&quot;text:Pricing&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="e23ded6dc511ca94e41b132e28d5ad22c23878e42d05d2c2cb75b51468afb9e3" data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to Pricing&quot;,&quot;label&quot;:&quot;text:Pricing&quot;}">Pricing</a></li>
      <li class="mr-3 mr-lg-0"><a href="https://docs.github.com" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to api&quot;,&quot;label&quot;:&quot;text:api&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="ec0f84ade7455ccaa1f4bb16907835137fc27ef8b78e50d53aa987ad0834d1e4" data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to api&quot;,&quot;label&quot;:&quot;text:api&quot;}">API</a></li>
      <li class="mr-3 mr-lg-0"><a href="https://services.github.com" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to training&quot;,&quot;label&quot;:&quot;text:training&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="a9d9325402bb0b9c6fc8b8a993c2fdc7a5515669fe1b10bdd6b72fae57ddc737" data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to training&quot;,&quot;label&quot;:&quot;text:training&quot;}">Training</a></li>
        <li class="mr-3 mr-lg-0"><a href="https://github.blog" data-hydro-click="{&quot;event_type&quot;:&quot;analytics.event&quot;,&quot;payload&quot;:{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to blog&quot;,&quot;label&quot;:&quot;text:blog&quot;,&quot;originating_url&quot;:&quot;https://github.com/Rkatsiteli/Vue-archive/blob/master/6%20%E4%BD%8D%E6%95%B0%E5%AF%86%E7%A0%81%E8%BE%93%E5%85%A5%E6%A1%86%EF%BC%88%E4%BB%BF%E7%85%A7%E6%94%AF%E4%BB%98%E5%AE%9D%EF%BC%89/pwd/img/blink.gif&quot;,&quot;user_id&quot;:null}}" data-hydro-click-hmac="e6185547324092097d388e025a0feb8ba6ad89d870dabab9e72e3c85e103c5be" data-analytics-event="{&quot;category&quot;:&quot;Footer&quot;,&quot;action&quot;:&quot;go to blog&quot;,&quot;label&quot;:&quot;text:blog&quot;}">Blog</a></li>
        <li><a data-ga-click="Footer, go to about, text:about" href="https://github.com/about">About</a></li>
    </ul>
  </div>
  <div class="d-flex flex-justify-center pb-6">
    <span class="f6 color-fg-muted"></span>
  </div>
</footer>




  <div id="ajax-error-message" class="ajax-error-message flash flash-error" hidden>
    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-alert">
    <path fill-rule="evenodd" d="M8.22 1.754a.25.25 0 00-.44 0L1.698 13.132a.25.25 0 00.22.368h12.164a.25.25 0 00.22-.368L8.22 1.754zm-1.763-.707c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0114.082 15H1.918a1.75 1.75 0 01-1.543-2.575L6.457 1.047zM9 11a1 1 0 11-2 0 1 1 0 012 0zm-.25-5.25a.75.75 0 00-1.5 0v2.5a.75.75 0 001.5 0v-2.5z"></path>
</svg>
    <button type="button" class="flash-close js-ajax-error-dismiss" aria-label="Dismiss error">
      <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x">
    <path fill-rule="evenodd" d="M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"></path>
</svg>
    </button>
    You can’t perform that action at this time.
  </div>

  <div class="js-stale-session-flash flash flash-warn flash-banner" hidden
    >
    <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-alert">
    <path fill-rule="evenodd" d="M8.22 1.754a.25.25 0 00-.44 0L1.698 13.132a.25.25 0 00.22.368h12.164a.25.25 0 00.22-.368L8.22 1.754zm-1.763-.707c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0114.082 15H1.918a1.75 1.75 0 01-1.543-2.575L6.457 1.047zM9 11a1 1 0 11-2 0 1 1 0 012 0zm-.25-5.25a.75.75 0 00-1.5 0v2.5a.75.75 0 001.5 0v-2.5z"></path>
</svg>
    <span class="js-stale-session-flash-signed-in" hidden>You signed in with another tab or window. <a href="">Reload</a> to refresh your session.</span>
    <span class="js-stale-session-flash-signed-out" hidden>You signed out in another tab or window. <a href="">Reload</a> to refresh your session.</span>
  </div>
    <template id="site-details-dialog">
  <details class="details-reset details-overlay details-overlay-dark lh-default color-fg-default hx_rsm" open>
    <summary role="button" aria-label="Close dialog"></summary>
    <details-dialog class="Box Box--overlay d-flex flex-column anim-fade-in fast hx_rsm-dialog hx_rsm-modal">
      <button class="Box-btn-octicon m-0 btn-octicon position-absolute right-0 top-0" type="button" aria-label="Close dialog" data-close-dialog>
        <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-x">
    <path fill-rule="evenodd" d="M3.72 3.72a.75.75 0 011.06 0L8 6.94l3.22-3.22a.75.75 0 111.06 1.06L9.06 8l3.22 3.22a.75.75 0 11-1.06 1.06L8 9.06l-3.22 3.22a.75.75 0 01-1.06-1.06L6.94 8 3.72 4.78a.75.75 0 010-1.06z"></path>
</svg>
      </button>
      <div class="octocat-spinner my-6 js-details-dialog-spinner"></div>
    </details-dialog>
  </details>
</template>

    <div class="Popover js-hovercard-content position-absolute" style="display: none; outline: none;" tabindex="0">
  <div class="Popover-message Popover-message--bottom-left Popover-message--large Box color-shadow-large" style="width:360px;">
  </div>
</div>

    <template id="snippet-clipboard-copy-button">
  <div class="zeroclipboard-container position-absolute right-0 top-0">
    <clipboard-copy aria-label="Copy" class="ClipboardButton btn js-clipboard-copy m-2 p-0 tooltipped-no-delay" data-copy-feedback="Copied!" data-tooltip-direction="w">
      <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-copy js-clipboard-copy-icon m-2">
    <path fill-rule="evenodd" d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 010 1.5h-1.5a.25.25 0 00-.25.25v7.5c0 .*************.25h7.5a.25.25 0 00.25-.25v-1.5a.75.75 0 011.5 0v1.5A1.75 1.75 0 019.25 16h-7.5A1.75 1.75 0 010 14.25v-7.5z"></path><path fill-rule="evenodd" d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0114.25 11h-7.5A1.75 1.75 0 015 9.25v-7.5zm1.75-.25a.25.25 0 00-.25.25v7.5c0 .*************.25h7.5a.25.25 0 00.25-.25v-7.5a.25.25 0 00-.25-.25h-7.5z"></path>
</svg>
      <svg aria-hidden="true" height="16" viewBox="0 0 16 16" version="1.1" width="16" data-view-component="true" class="octicon octicon-check js-clipboard-check-icon color-fg-success d-none m-2">
    <path fill-rule="evenodd" d="M13.78 4.22a.75.75 0 010 1.06l-7.25 7.25a.75.75 0 01-1.06 0L2.22 9.28a.75.75 0 011.06-1.06L6 10.94l6.72-6.72a.75.75 0 011.06 0z"></path>
</svg>
    </clipboard-copy>
  </div>
</template>




  </body>
</html>

