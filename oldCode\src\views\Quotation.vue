<template>
  <div style="background-color: #f7f7f7">
    <Headers />
    <div style="width:1200px;margin:0 auto;padding-top: 20px; box-sizing: border-box">
      <img class="banner" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/images/gangjiaban.jpg" alt="">
      <div class="all_price_top">
        <div class="all-price">
          <div class="flex_row">
            <div class="flex_row_title">全国平均价格</div>
            <div class="flex_row_search">
              <el-date-picker
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  v-model="daytime"
                  type="date"
                  placeholder="选择日期时间"
                  :picker-options="pickerOptions"
                  @change="dateChange"
                />
            </div>
          </div>
          <div class="all_price_title">
            <div class="">
              <div class="all_price_title_date" style="color:#999;font-size: 12px;">
                {{datetime}}
              </div>
              <div class="all_price_title_box">
                <div class="left">{{pingjun}}</div>
                <div class="right">
                  <!-- <p class="risingPrice" style="color: #F93A4A"><span>↑</span><span>2</span></p> -->
                  <!-- <p class="risingPrice" style="color: #02C619"><span>↑</span><span>2</span></p> -->
                  <p class="danwei">元/吨</p>
                </div>
              </div>
              
            </div>
            <div class="priceHightAndLow">
              <div class="height">最高：<span class="max">{{maxgangjia.price}}元 {{maxgangjia.address}}</span></div>
              <div class="low">最低：<span class="min">{{mingangjia.price}}元 {{mingangjia.address}}</span></div>
            </div>
          </div>
          <div class="echartss">
            <chart width="100%" height="230px" ref="charts" />
          </div>
        </div>
        <div class="price_code">
          <img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/images/gangjiama.png" alt="" srcset="">
        </div>
      </div>
      <div class="main-city">
        <div class="main-city-title">全国主要城市价格汇总</div>
        <div class="main-city-area">
          <div v-for="(temp, index) in allList" :key="index">
            <div class="main-city-area-title">
              {{ temp.title }}
              <span>{{ temp.desc }}</span>
            </div>
            <!-- <div class="bottom_one_titles" style="color:#000">单位(元/吨)</div> -->
            <div class="main-city-area-list">
              <div class="main-city-area-list-cont" v-for="(item, i) in temp.list" :key="i">
                <!-- <img src="@/assets/two1.png" /> -->
                <div class="main-city-area-list-title">
                  {{ item.address }}
                </div>
                <div class="main-city-area-list-detail">
                  <div class="left">
                    <p>均价</p>
                    <p class="w-price">
                      {{ item.price }}
                      <span>元</span>
                    </p>
                  </div>
                  <div class="right">
                    <p>涨跌</p>
                    <div class="change" v-if="item.increase == 0">
                      --
                    </div>
                    <div class="change increase" v-if="item.increase > 0">
                      <!-- <img src="@/assets/three1.png" /> -->
                      <span style="font-size:16px;margin-top:-2px">↑</span>
                      <div>{{ item.increase }}<span>元</span></div>
                    </div>
                    <div class="change unincrease" v-if="item.increase < 0">
                      <!-- <img src="@/assets/four.png" /> -->
                      <span style="font-size:16px;margin-top:-2px">↓</span>
                      <div>{{ item.increase * -1 }}<span>元</span></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="statement">
        免责声明：灰谷网钢价行情报价提供的行情信息仅供参考，可能和市场行情略有偏差，请结合实际情况合理决策，并不构成对客户决策的直接建议，客户不应以此取代自己的独立判断，客户做出的任何决策与灰谷网无关。
      </div>
    </div>
    <FootersBottom />
    <homeRight />
  </div>
</template>

<script>
import Headers from "@/components/Headers.vue";
import FootersBottom from "@/components/FootersBottom.vue";
import homeRight from "@/components/homeRight.vue";
import Chart from "@/components/Charts";
import ajax from "../store/Ajax";
export default {
  name: "News",
  components: {
    Headers,
    FootersBottom,
    homeRight,
    Chart,
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      maxgangjia:{
        price:'',
        address:''
      },
      mingangjia:{
        price:'',
        address:''
      },
      pingjun:'',
      datetime:'',
      list: null,
      daytime: "",
      allList: [
        {
          type: 4,
          title: "华东地区",
          desc: "（江苏,上海,浙江,安徽,山东,江西,福建）",
          list: [
            {
              address: "江苏张家港",
              price: "3220",
              increase: 50,
            },
            {
              address: "江苏南京",
              price: "3210",
              increase: -20,
            },
            {
              address: "江苏无锡",
              price: "3180",
              increase: 0,
            },
            {
              address: "江苏江阴",
              price: "3180",
              increase: 0,
            },
            {
              address: "上海",
              price: "3170",
              increase: 0,
            },
            {
              address: "浙江杭州",
              price: "3180",
              increase: 0,
            },
            {
              address: "浙江陶庄",
              price: "3240",
              increase: 0,
            },
            {
              address: "浙江宁波",
              price: "3150",
              increase: 0,
            },
            {
              address: "安徽合肥",
              price: "3050",
              increase: 0,
            },
            {
              address: "安徽马鞍山",
              price: "3180",
              increase: 0,
            },
            {
              address: "山东济南",
              price: "3230",
              increase: 0,
            },
            {
              address: "山东聊城",
              price: "3195",
              increase: 0,
            },
            {
              address: "山东临沂",
              price: "3175",
              increase: 0,
            },
            {
              address: "江西南昌",
              price: "3140",
              increase: 0,
            },
            {
              address: "福建福州",
              price: "3130",
              increase: 0,
            },
          ],
        },
        {
          type: 1,
          title: "华南地区",
          desc: "（广东,广西）",
          list: [
            {
              address: "广东广州",
              price: "3080",
              increase: 0,
            },
            {
              address: "广东佛山",
              price: "3070",
              increase: 0,
            },
            {
              address: "广东中山",
              price: "3060",
              increase: 0,
            },
            {
              address: "广西南宁",
              price: "3010",
              increase: 0,
            },
          ],
        },
        {
          type: 2,
          title: "华中地区",
          desc: "（湖北,湖南,河南）",
          list: [
            {
              address: "湖北武汉",
              price: "3070",
              increase: 0,
            },
            {
              address: "湖南长沙",
              price: "3020",
              increase: 0,
            },
            {
              address: "湖南娄底",
              price: "3100",
              increase: 0,
            },
            {
              address: "河南郑州",
              price: "3130",
              increase: 0,
            },
            {
              address: "河南安阳",
              price: "3200",
              increase: 0,
            },
          ],
        },
        {
          type: 2,
          title: "华北地区",
          desc: "（北京,天津,河北,山西,内蒙古）",
          list: [
            {
              address: "北京",
              price: "3310",
              increase: 0,
            },
            {
              address: "天津",
              price: "3330",
              increase: 0,
            },
            {
              address: "河北唐山",
              price: "3445",
              increase: 0,
            },
            {
              address: "河北石家庄",
              price: "3300",
              increase: 0,
            },
            {
              address: "河北邢台",
              price: "3360",
              increase: 0,
            },
            {
              address: "山西太原",
              price: "3225",
              increase: 0,
            },
            {
              address: "山西临汾",
              price: "3270",
              increase: 0,
            },
            {
              address: "内蒙古包头",
              price: "3270",
              increase: 0,
            },
          ],
        },
        {
          type: 2,
          title: "西北地区",
          desc: "（甘肃,青海,宁夏,新疆,陕西）",
          list: [
            {
              address: "陕西西安",
              price: "3110",
              increase: 0,
            },
            {
              address: "青海西宁",
              price: "3090",
              increase: 0,
            },
            {
              address: "甘肃兰州",
              price: "2980",
              increase: 0,
            },
            {
              address: "宁夏银川",
              price: "2950",
              increase: 0,
            },
            {
              address: "新疆乌鲁木齐",
              price: "2760",
              increase: 0,
            },
          ],
        },
        {
          type: 1,
          title: "西南地区",
          desc: "（四川,云南,贵州,重庆）",
          list: [
            {
              address: "云南昆明",
              price: "3050",
              increase: 0,
            },
            {
              address: "四川成都",
              price: "3030",
              increase: 0,
            },
            {
              address: "重庆",
              price: "3070",
              increase: 0,
            },
            {
              address: "贵州贵阳",
              price: "3050",
              increase: 0,
            },
          ],
        },
        {
          type: 1,
          title: "东北地区",
          desc: "（辽宁,吉林,黑龙江）",
          list: [
            {
              address: "辽宁辽阳",
              price: "3440",
              increase: 0,
            },
            {
              address: "黑龙江哈尔滨",
              price: "3240",
              increase: 0,
            },
            {
              address: "吉林长春",
              price: "3300",
              increase: 20,
            },
            {
              address: "辽宁沈阳",
              price: "3460",
              increase: 0,
            },
          ],
        },
      ],
    };
  },
  created() {
    this.getinfo(this.setTime(new Date()));
  },
  methods: {
    setTime(value) {
      // value = value * 1000
      let date = new Date(value);
      // console.log(date)
      let y = date.getFullYear();
      // console.log(y)
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? "0" + MM : MM;
      let d = date.getDate();
      d = d < 10 ? "0" + d : d;
      return y + "-" + MM + "-" + d;
    },
    setTime0(value) {
      // value = value * 1000
      let date = new Date(value);
      // console.log(date)
      let y = date.getFullYear();
      // console.log(y)
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? "0" + MM : MM;
      let d = date.getDate();
      d = d < 10 ? "0" + d : d;
      return y + "年" + MM + "月" + d + "日";
    },
    screenPrice(data){
      let AS = data.sort((a, b) => Number(a.price) - Number(b.price)) 
      AS[data.length-1]  // 获取最大值：100
      // console.log(AS[0])
      this.maxgangjia = {
        price:AS[data.length-1].price,
        address:AS[data.length-1].address
      }
      this.mingangjia = {
        price:AS[0].price,
        address:AS[0].address
      }
    },
    getinfo(time) {
      this.daytime = time
      // 新闻列表
      ajax
        .gangjiainfo({
          daytime: time,
        })
        .then((response) => {
          if (response.data == "") return this.$message.success("暂无最新数据");
          // this.daytime = response.data.daytime;
          // console.log(response.data,'data')
          this.pingjun = response.data.pingjun
          this.datetime = response.data.daytime
          this.$refs.charts.initChart(
            response.data.daytimeArr,
            response.data.pingjunArr
          );
          response.data.gedi.forEach((ela) => {
            this.allList.forEach((el) => {
              el.list.forEach((els) => {
                if (ela.address == els.address) {
                  els.price = ela.price;
                  els.increase = ela.fudu;
                }
              });
            });
          });
          this.screenPrice(response.data.gedi)
        })
        .catch((err) => {});
    },
    dateChange(val){
      if(!val){
        this.getinfo(this.setTime(new Date()));
      }else{
        this.getinfo(val);
      }
    }
  },
};
</script>

<style type="text/css" scoped="scoped">
.banner{
    display: block;
    margin:  0 auto 16px;
}
.all_price_top{
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.all-price{
  /* width: 1200px; */
  width: 864px;
  padding: 24px;
  background: #fff;
  box-sizing: border-box;
}
.price_code{
  width: 323px;
}
.price_code img{
  display: block;
  width: 100%;
}
.flex_row{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 14px;
  border-bottom: 1px solid #F0F0F0;
  box-sizing: border-box;
}
.flex_row_title{
  font-size: 20px;
  font-family: PingFang SC;
  font-weight: bold;
  color: #333333;
  line-height: 28px;
}
.all_price_title{
  margin-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.all_price_title_box{
  display: flex;
  align-items: baseline;
}
.all_price_title_box .left{
  margin-right: 14px;
  font-size: 38px;
  font-family: PingFang SC;
  font-weight: bold;
  color: #333333;
  line-height: 53px;
}
.all_price_title_box .risingPrice{
  font-size: 18px;
  font-family: PingFang SC;
  font-weight: bold;
  color: #F93A4A;
  line-height: 25px;
}
.all_price_title_box .danwei{
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 20px;
}
.priceHightAndLow{
  font-size: 14px;
  line-height: 20px;
  color: #656565;
}
.priceHightAndLow .height{
  margin-bottom: 5px;
}
.priceHightAndLow .height>span{
  color: #F93A4A;
}
.priceHightAndLow .low>span{
  color: #02C619;
}
.main-city{
  width: 100%;
  margin: 16px 0 0;
  padding: 24px;
  background: #fff;
  box-sizing: border-box;
}
.main-city-title{
  margin-bottom: 16px;
  padding-bottom: 24px;
  font-size: 20px;
  font-family: PingFang SC;
  font-weight: bold;
  color: #333333;
  line-height: 28px;
  border-bottom: 1px solid #F0F0F0;
}
.main-city-area-title{
  margin-bottom: 16px;
  font-size: 18px;
  font-family: PingFang SC;
  font-weight: bold;
  color: #333333;
  line-height: 25px;
}
.main-city-area-title span{
  font-size: 14px;
  font-weight: normal;
}
.main-city-area-list{
  display: grid;
  grid-template-columns: repeat(6, 178px);
  grid-column-gap: 16px;
}
.main-city-area-list-cont{
  margin-right: 16px;
  margin-bottom: 16px;
  box-sizing: border-box;
  padding: 8px;
  width: 178px;
  height: 117px;
  background: #FFFFFF;
  border: 1px solid #F5F5F5;
}
.main-city-area-list-title{
  padding: 8px 8px 8px 10px;
  font-size: 15px;
  font-family: PingFang SC;
  font-weight: bold;
  color: #323232;
  line-height: 22px;
}
.main-city-area-list-detail{
  box-sizing: border-box;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  width: 162px;
  height: 63px;
  background: #FAFAFA;
  border: 1px solid #FAFAFA;
  font-size: 12px;
  line-height: 17px;
}
.main-city-area-list-detail>div{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.main-city-area-list-detail p:first-child{
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 17px;
  text-align: center;
}
.main-city-area-list-detail .w-price{
  color: #4A4A4A;
  font-weight: bold;
  font-size: 16px;
}
.main-city-area-list-detail .w-price>span{
  font-size: 12px;
}
.main-city-area-list-detail .change{
  color: #323232;
  font-weight: bold;
  font-size: 16px;
}
.main-city-area-list-detail .increase{
  display: flex;
  align-items: center;
  color: #e13a4d;
}
.main-city-area-list-detail .increase img{
  display: block;
  width: 8px;
}
.main-city-area-list-detail .unincrease{
  display: flex;
  align-items: center;
  color: #07b435;
}
.main-city-area-list-detail .unincrease img{
  display: block;
  width: 8px;
}
.main-city-area-list-detail .increase span,.main-city-area-list-detail .unincrease span{
  font-size: 14px;
}
.statement{
  margin: 32px 0 50px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #999999;
  line-height: 20px;
}

.echartss {
  width: 100%;
}
</style>
