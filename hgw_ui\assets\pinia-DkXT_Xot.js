import{aj as t,r as e,ai as n,Q as s,j as o,at as a,a2 as c,au as r,ag as i,t as u,z as f,a4 as p,c as l,i as h,av as y}from"./vue-vendor-D6tHD5lA.js";
/*!
 * pinia v3.0.3
 * (c) 2025 <PERSON>
 * @license MIT
 */let d;const b=t=>d=t,v=Symbol();function _(t){return t&&"object"==typeof t&&"[object Object]"===Object.prototype.toString.call(t)&&"function"!=typeof t.toJSON}var j,O;function $(){const s=t(!0),o=s.run(()=>e({}));let a=[],c=[];const r=n({install(t){b(r),r._a=t,t.provide(v,r),t.config.globalProperties.$pinia=r,c.forEach(t=>a.push(t)),c=[]},use(t){return this._a?a.push(t):c.push(t),this},_p:a,_a:null,_e:s,_s:new Map,state:o});return r}(O=j||(j={})).direct="direct",O.patchObject="patch object",O.patchFunction="patch function";const m=()=>{};function g(t,e,n,s=m){t.push(e);const o=()=>{const n=t.indexOf(e);n>-1&&(t.splice(n,1),s())};return!n&&r()&&i(o),o}function S(t,...e){t.slice().forEach(t=>{t(...e)})}const P=t=>t(),E=Symbol(),w=Symbol();function x(t,e){t instanceof Map&&e instanceof Map?e.forEach((e,n)=>t.set(n,e)):t instanceof Set&&e instanceof Set&&e.forEach(t.add,t);for(const n in e){if(!e.hasOwnProperty(n))continue;const s=e[n],c=t[n];_(c)&&_(s)&&t.hasOwnProperty(n)&&!o(s)&&!a(s)?t[n]=x(c,s):t[n]=s}return t}const I=Symbol();function M(t){return!_(t)||!Object.prototype.hasOwnProperty.call(t,I)}const{assign:A}=Object;function F(t){return!(!o(t)||!t.effect)}function k(n,r,i={},p,l,h){let y;const d=A({actions:{}},i),v={deep:!0};let _,O,$,I=[],k=[];const z=p.state.value[n];let C;function J(t){let e;_=O=!1,"function"==typeof t?(t(p.state.value[n]),e={type:j.patchFunction,storeId:n,events:$}):(x(p.state.value[n],t),e={type:j.patchObject,payload:t,storeId:n,events:$});const s=C=Symbol();f().then(()=>{C===s&&(_=!0)}),O=!0,S(I,e,p.state.value[n])}h||z||(p.state.value[n]={}),e({});const N=h?function(){const{state:t}=i,e=t?t():{};this.$patch(t=>{A(t,e)})}:m;const Q=(t,e="")=>{if(E in t)return t[w]=e,t;const s=function(){b(p);const e=Array.from(arguments),o=[],a=[];let c;S(k,{args:e,name:s[w],store:q,after:function(t){o.push(t)},onError:function(t){a.push(t)}});try{c=t.apply(this&&this.$id===n?this:q,e)}catch(r){throw S(a,r),r}return c instanceof Promise?c.then(t=>(S(o,t),t)).catch(t=>(S(a,t),Promise.reject(t))):(S(o,c),c)};return s[E]=!0,s[w]=e,s},W={_p:p,$id:n,$onAction:g.bind(null,k),$patch:J,$reset:N,$subscribe(t,e={}){const s=g(I,t,e.detached,()=>o()),o=y.run(()=>u(()=>p.state.value[n],s=>{("sync"===e.flush?O:_)&&t({storeId:n,type:j.direct,events:$},s)},A({},v,e)));return s},$dispose:function(){y.stop(),I=[],k=[],p._s.delete(n)}},q=s(W);p._s.set(n,q);const B=(p._a&&p._a.runWithContext||P)(()=>p._e.run(()=>(y=t()).run(()=>r({action:Q}))));for(const t in B){const e=B[t];if(o(e)&&!F(e)||a(e))h||(z&&M(e)&&(o(e)?e.value=z[t]:x(e,z[t])),p.state.value[n][t]=e);else if("function"==typeof e){const n=Q(e,t);B[t]=n,d.actions[t]=e}}return A(q,B),A(c(q),B),Object.defineProperty(q,"$state",{get:()=>p.state.value[n],set:t=>{J(e=>{A(e,t)})}}),p._p.forEach(t=>{A(q,y.run(()=>t({store:q,app:p._a,pinia:p,options:d})))}),z&&h&&i.hydrate&&i.hydrate(q.$state,z),_=!0,O=!0,q}
/*! #__NO_SIDE_EFFECTS__ */function z(t,e,s){let o;const a="function"==typeof e;function c(s,c){const r=y();(s=s||(r?h(v,null):null))&&b(s),(s=d)._s.has(t)||(a?k(t,e,o,s):function(t,e,s){const{state:o,actions:a,getters:c}=e,r=s.state.value[t];let i;i=k(t,function(){r||(s.state.value[t]=o?o():{});const e=p(s.state.value[t]);return A(e,a,Object.keys(c||{}).reduce((e,o)=>(e[o]=n(l(()=>{b(s);const e=s._s.get(t);return c[o].call(e,e)})),e),{}))},e,s,0,!0)}(t,o,s));return s._s.get(t)}return o=a?s:e,c.$id=t,c}export{$ as c,z as d};
