<template>
	<div class="min_wrapper_1500 " style="background-color: #F7F7F7;">
		<Headers />
        <div class="newsTitle">
            当前位置：首页 | {{info ? info.cate_name :''}}
        </div>
		<div class="About_one" v-if="info">
			<div class="About_one_title">{{info.title}}</div>
			<div class="About_one_time">发布时间：{{info.addtime}}</div>
			<div class="About_one_title_view" v-html="info.content"></div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import ajax from '../../store/Ajax'
	export default {
		name: 'about',
		components: {
			Headers,
			FootersBottom,
			homeRight
		},
		data() {
			return {
				info: null,
			}
		},
		created() {
			this.gerInfo()
		},
		methods: {
			gerInfo() {
				ajax.zhaobiaoinfo({
                    id: this.$route.query.id
                })
                .then(res => {
                    if (res.code == 1) this.info = res.data
                })
			}
		},
	}
</script>

<style scoped="scoped">
    .newsTitle {
        display: flex;
        align-items: center;
        width: 1200px;
        height: 60px;
        margin: 0px auto 0;
        /* border-bottom: 1px solid #eee; */
    }
	.About_one {
		width: 1200px;
		min-height: 800px;
		margin: 0px auto 20px;
		padding: 20px 10px;
		box-sizing: border-box;
		background-color: #FFFFFF;
	}

	.About_one_title {
        margin: 20px auto 20px;
		text-align: center;
		/* line-height: 100px; */
		font-size: 600;
		font-size: 30px;
	}
    .About_one_time{
        margin-bottom: 50px;
        padding-bottom: 10px;
        text-align: center;
		font-size: 18px;
        color: #999;
        border-bottom: 1px solid #ccc;
    }
	.About_one_title_view{
		width: 90%;
		margin: 0 auto;
	}
</style>
