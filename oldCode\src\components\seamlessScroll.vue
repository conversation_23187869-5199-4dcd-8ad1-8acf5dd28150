<template>
  <!-- <client-only> -->
  <vue-seamless-scroll
    :data="list"
    :class-option="optionSingleHeight"
    class="seamless-warp"
  >
    <div class="industry_list">
        <div class="industry_item" v-for="item,i in list" :key="i">
            <div class="item_status">[{{item.label}}]</div>
            <div class="item_text">{{item.text}}</div>
        </div>
    </div>
  </vue-seamless-scroll>
  <!-- </client-only> -->
</template>
<script>
import vueSeamlessScroll from "vue-seamless-scroll";
export default {
  props: ["list"],
  components: {
    vueSeamlessScroll,
  },
  data() {
    return {
      listData: [{}],
      time: 1,
    };
  },
  computed: {
    optionSingleHeight() {
      return {
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 41,
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 0,
        step: this.time,
        limitMoveNum: 7,
      };
    },
  },
  mounted() {
    // setTimeout(() => {
    //   this.time = 1;
    // }, 3000);
  },
  methods:{
    navTo(item){
      this.$router.push({ path: "/plan/statistics" ,query:{
        fengongsi:item.fengongsi,
        ju:item.ju,
        id:item.id,
        level:item.level
      }});
    }
  }
};
</script>
<style lang="scss" scoped>
.seamless-warp {
  margin: 10px 0;
  height: 287px;
  overflow: hidden;
}

.industry_list{
  .industry_item{
      display: flex;
      align-items: center;
      width: 100%;
      height: 41px;
      padding: 0 20px;
      box-sizing: border-box;
      border-bottom: 1px solid #D8D8D8;
      cursor: pointer;
      .item_status{
          min-width: 45px;
          margin-right: 10px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #A80012;
          line-height: 20px;
      }
      .item_text{
          width: 270px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          font-size: 12px;
          color: #2A2A2A;
          line-height: 20px;
          overflow: hidden;    
          text-overflow: ellipsis;    
          white-space: nowrap;
      }
      // &:last-child{
      //     border: 0;
      // }
  }
}
</style>
