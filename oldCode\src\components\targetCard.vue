<template>
  <div class="homeCentet_four_centet_view" @click="goDetails(data)">
    <div class="homeCentet_four_centet_imgview">
      <img :src="url + data.bd_url" @error="(e)=>e.target.src = data.bd_url ? 'https://oss.yunpaiwang.com/'+data.bd_url : ''" />
      <div class="homeCentet_four_centet_item_type">
        <div
          v-if="data.bd_status == 1"
          class="homeCentet_four_centet_view_enters"
        >
          即将开始
        </div>
        <div
          v-else-if="data.bd_status == 2 || data.bd_status == 3"
          class="homeCentet_four_centet_view_enters homeCentet_four_centet_view_enterss"
        >
          正在竞价
        </div>
        <div
          v-else-if="data.bd_status == 5"
          class="homeCentet_four_centet_view_enters homeCentet_four_centet_view_enterss"
        >
          已成交
        </div>
        <div
          v-else
          class="homeCentet_four_centet_view_enters homeCentet_four_centet_view_entersss"
        >
          {{ data.bd_status_name }}
        </div>
      </div>
    </div>
    <div class="homeCentet_four_centet_view_top">
      <div class="homeCentet_four_centet_view_title">{{ data.bd_title }}</div>
      <div class="homeCentet_four_centet_view_num">
        <img src="@/assets/images/index/weiguan.png" alt="" />
        <span>{{ data.bd_weiguan }}</span
        >次围观
      </div>
    </div>
    <div class="homeCentet_four_centet_view_two">
      <div
        :class="[
          'homeCentet_four_centet_view_two_left',
          data.bd_status == 1
            ? 'homeCentet_four_centet_view_two_left_green'
            : 'homeCentet_four_centet_view_two_left_red',
        ]"
      >
        <div v-if="data.bd_status != 5">
          起拍价 <span> {{ data.bd_qipaijia }} </span>{{ data.qpj_danwie }}
        </div>
        <div v-if="data.bd_status == 5">
          成交价 <span> {{ data.bd_chengjiaojia }} </span> {{ data.cjj_danwie }}
        </div>
      </div>
      <div class="homeCentet_four_centet_view_two_right">
        <span
          v-if="
            data.bd_status != 2 && data.bd_status != 3 && data.bd_status != 1
          "
          >查看详情</span
        >
        <span v-else>立即报名</span>
      </div>
    </div>
    <div class="homeCentet_four_centet_view_line"></div>
    <div v-if="data.bd_status == 1" class="homeCentet_four_centet_view_three">
      预 计 <span>{{ data.start_time | formatDate }}</span> 开始
    </div>
    <div
      v-if="data.bd_status == 2 || data.bd_status == 3"
      class="homeCentet_four_centet_view_three"
    >
      预 计 <span>{{ data.end_time | formatDate }}</span> 结束
    </div>
    <div
      v-if="data.bd_status == 5 || data.bd_status == 4"
      class="homeCentet_four_centet_view_three"
    >
      结束时间 <span>{{ data.end_time | formatDate }}</span>
    </div>
    <div v-if="data.bd_status == 8" class="homeCentet_four_centet_view_three">
      结束时间 <span>{{ data.start_time | formatDate }}</span>
    </div>
    <div v-if="data.bd_status == 6" class="homeCentet_four_centet_view_three">
      预 计 <span>{{ data.end_time | formatDate }}</span> 结束
    </div>
    <div v-if="data.bd_status == 7" class="homeCentet_four_centet_view_three">
      预 计 <span>{{ data.end_time | formatDate }}</span> 结束
    </div>
    <div v-if="data.bd_status == 5 || data.bd_status == 4 || data.bd_status == 8" class="homeCentet_four_centet_view_three" style="margin-top:8px">
      截止报名 <span>{{ data.bm_time_name }}</span>
    </div>
    <div v-else class="homeCentet_four_centet_view_three" style="margin-top:8px">
      截 止 <span>{{ data.bm_time_name }}</span> 报名
    </div>
  </div>
</template>
<script>
// import ajax from "@/store/Ajax";
export default {
  props: {
    data: {
      type: Object,
      default: {},
    },
    // key: {
    //   type: Number,
    //   default: 0,
    // },
  },
  data() {
    return {
      url: this.$Pc.ossUrl,
      // USER_INFO: null,
    };
  },
  filters: {
    formatDate: function (value) {
      value = value * 1000;
      let date = new Date(value);
      // console.log(date)
      let y = date.getFullYear();
      // console.log(y)
      let MM = date.getMonth() + 1;
      MM = MM < 10 ? "0" + MM : MM;
      let d = date.getDate();
      d = d < 10 ? "0" + d : d;
      let h = date.getHours();
      h = h < 10 ? "0" + h : h;
      let m = date.getMinutes();
      m = m < 10 ? "0" + m : m;
      let s = date.getSeconds();
      s = s < 10 ? "0" + s : s;
      return MM + "月" + d + "日 " + h + ":" + m + ":" + s;
    },
  },
  created() {
    // if (localStorage.getItem("userInfo")) {
    //   this.USER_INFO = JSON.parse(localStorage.getItem("userInfo"));
    // }
  },
  methods: {
    goDetails(item) {
      let routeData = this.$router.resolve({
        //核心语句
        path: "/auctionDetail/" + item.id, //跳转的路径
        query: {
          //路由传参时push和query搭配使用 ，作用时传递参数
          id: item.id,
          ispaimaih: false,
        },
      });
      window.open(routeData.href, "_blank");
    },
  },
};
</script>
<style lang="scss" scoped>
.homeCentet_four_centet_view {
  position: relative;
  width: 285px;
  height: 350px;
  margin-bottom: 20px;
  border-radius: 8px;
  background: #ffffff;
  cursor: pointer;
  transition: all 0.25s ease-in;
  overflow: hidden;
}
.homeCentet_four_centet_view:hover {
  transform: translateY(-8px);
  /* transform: skewX(45deg) translateX(-150px); */
  box-shadow: 0px 2px 12px rgba(0, 0, 0, 0.15);
}
.homeCentet_four_centet_imgview {
  display: flex;
  align-items: center;
  width: 285px;
  height: 200px;
  background: #e5e5e5;
  overflow: hidden;
}
.homeCentet_four_centet_item_type {
  position: absolute;
  left: 0;
  top: 13px;
}

.homeCentet_four_centet_view_enters {
  width: 80px;
  height: 30px;
  background: #03a081;
  border-radius: 0px 15px 15px 0px;
  font-size: 14px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #ffffff;
  line-height: 30px;
  text-align: center;
}
.homeCentet_four_centet_view_enterss {
  background: #a80012;
}

.homeCentet_four_centet_view_entersss {
  background: #bfbfbf;
}
.homeCentet_four_centet_views {
  margin-left: 20px;
}

.homeCentet_four_centet_imgview > img {
  width: 285px;
}

.homeCentet_four_centet_view_top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 18px 15px 15px;
  box-sizing: border-box;
}
.homeCentet_four_centet_view_title {
  flex: 1;
  margin-right: 5px;
  font-size: 16px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 16px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.homeCentet_four_centet_view_num {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 12px;
}
.homeCentet_four_centet_view_num span {
  font-size: 18px;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 500;
  color: #333333;
  line-height: 18px;
}
.homeCentet_four_centet_view_num img {
  display: block;
  width: 10px;
  height: 10px;
  margin-right: 2px;
}

.homeCentet_four_centet_view_two {
  position: relative;
  z-index: 0;
  display: flex;
  align-items: center;
  width: 265px;
  margin: 0 auto;
}

.homeCentet_four_centet_view_two_left {
  width: 176px;
  height: 30px;
  margin-left: -10px;
  padding-left: 15px;
  /* background: linear-gradient(90deg, #FFFFFF 3%, #03A081 100%); */
  border-radius: 0px 15px 15px 0px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 30px;
  box-sizing: border-box;
}
.homeCentet_four_centet_view_two_left_green {
  color: #666666;
  background: linear-gradient(90deg, #ffffff 1%, #03a081 100%);
}
.homeCentet_four_centet_view_two_left_red {
  color: #fff;
  background: linear-gradient(90deg, #fb6952 3%, #cc120d 100%);
}

.homeCentet_four_centet_view_two_right {
  position: absolute;
  right: 0;
  top: 0;
  z-index: -1;
  width: 187px;
  height: 30px;
  padding-right: 28px;
  background: linear-gradient(90deg, #fff6f7 3%, #ffc78f 100%);
  border-radius: 0px 15px 15px 0px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 600;
  text-align: right;
  color: #cc0319;
  line-height: 30px;
  box-sizing: border-box;
  cursor: pointer;
}

.homeCentet_four_centet_view_line {
  width: 266px;
  height: 1px;
  margin: 10px auto 10px;
  background: #e9e9e9;
}
.homeCentet_four_centet_view_three {
  margin-left: 16px;
  font-size: 12px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
  line-height: 14px;
}

.homeCentet_four_centet_view_three span {
  color: #333;
  font-size: 14px;
  font-weight: 600;
}
</style>