<template>
	<div>
		<Headers />
		<div style="background-color: #EEEEEE;padding-top: 20px;box-sizing: border-box;">
			<div class="auction">
				<div class="fl">
					<div class="menu_z">行业资讯</div>
					<div class="flf" style="margin: 15px 0 30px;">
						<ul class="flfg">
							<li>
								<p :class='selectIndex == 2?"flfgaa":""'
									style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">行业资讯</p>
							</li>
							<li>
								<p :class='selectIndex == 1?"flfgaa":""'
									style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">常见问题</p>
							</li>
							<li>
								<p :class='selectIndex == 3?"flfgaa":""'
									style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">竞价流程</p>
							</li>
							<li>
								<p :class='selectIndex == 4?"flfgaa":""'
									style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">竞价规则</p>
							</li>
							<li>
								<p :class='selectIndex == 5?"flfgaa":""'
									style="white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">注册流程</p>
							</li>
						</ul>
					</div>
					<div><img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/images/lxwm.jpg" width="221" height="44"></div>
					<div class="menu_k">
						<div class="menu_rx">咨询热线</div>
						<div class="menu_dh">400-999-0233</div>
						<!-- <div class="menu_wz">https://www.yunpaiwang.com/</div> -->
					</div>
				</div>


				<div class="clear"></div>
			</div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	import ajax from '../store/Ajax'
	export default {
		name: 'News_info',
		components: {
			Headers,
			FootersBottom,
			homeRight
		},
		data() {
			return {
				selectIndex: 0,
			}
		},
		created() {
			console.log(this.$route.query.cateid)
			this.selectIndex = this.$route.query.cateid

		}

	}
</script>

<style type="text/css" scoped="scoped">
	.auction {
		width: 1160px;
		min-height: 1000px;
		padding-bottom: 50px;
		box-sizing: border-box;
		margin: 0 auto;
		background: #ffffff;
		padding: 0 20px 20px;
	}

	.fl {
		float: left;
		display: inline;
	}

	.fr {
		float: right;
	}

	.clear,
	.cl {
		clear: both;
		height: 0;
		overflow: hidden;
	}


	p,
	ul,
	li {
		list-style: none;
		margin: 0px;
		padding: 0px;
	}

	.rel {
		position: relative;
	}

	a {
		color: #000000;
	}

	.xuan {
		position: fixed;
		top: 60%;
		right: 45px;
		z-index: 99;
		display: flex;
		-js-display: flex;
		flex-direction: column;
	}

	.xuan li {
		width: 55px;
		height: 55px;
		border-radius: 50%;
		background: #949494;
		margin-top: 20px;
		display: flex;
		align-items: center;
		-js-display: flex;
		justify-content: center;
	}

	.xuan li:hover {
		background: #d6363b;
	}

	.xuan li img {
		width: 38px;
		height: 38px;
	}







	/* .menu { padding-top:15px;} */
	.menu {
		padding-bottom: 30px;
	}

	.menu {
		padding-bottom: 0px;
	}

	.wz {
		border-bottom: #e5e5e5 1px solid;
		color: #a4a4a4;
		line-height: 30px;
	}

	.menu_z {
		font-size: 26px;
		color: #384368;
		padding-top: 10px;
		font-weight: bold;
	}

	.menu_z span {
		font-size: 20px;
		color: #d0d0d0;
	}

	.menu_k {
		width: 300px;
		height: 76px;
		border: #e5e5e5 1px solid;
		padding: 18px 15px;
	}

	.menu_rx {
		font-size: 16px;
		color: #a1a1a1;
	}

	.menu_dh {
		font-size: 26px;
		color: #d6363b;
		font-weight: bold;
	}

	.menu_wz {
		font-size: 14px;
		color: #d8d8d8;
	}

	.wz_l a {
		color: #fe0000;
		font-size: 14px;
		font-weight: bolder;
	}

	.wz_jl {
		margin-top: 30px;
	}


	.flf {
		border-top: #d6363b 3px solid;
		margin: 30px 0;
		width: 330px;
	}

	.flfg {
		border-right: #c9c9c9 1px solid;
		border-bottom: #c9c9c9 1px solid;
		border-left: #c9c9c9 1px solid;
		width: 328px;
	}

	.flfg li {
		border-bottom: #c9c9c9 1px solid;
		text-align: center;
		height: 60px;
		line-height: 60px;
	}

	.flfg li:hover {
		text-align: center;
		height: 60px;
		line-height: 60px;
	}
	.flfg li p {
		display: block;
		font-size: 16px;
		width: 100%;
		height: 60px;
		text-align: center;
		color: #999;
	}

	.flfg>li>p:hover {
		background: #d6363b;
		color: #FFF;
	}

	.flfgaa {
		background: #d6363b !important;
		color: #FFF !important;
	}
</style>
