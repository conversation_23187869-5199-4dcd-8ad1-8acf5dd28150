import{a as e}from"./http-vendor-ztdpVPaQ.js";import{E as r}from"./element-plus-BiAL0NdQ.js";const s=e.create({baseURL:"http://39.101.72.34:80",timeout:1e4,withCredentials:!1});s.interceptors.response.use(e=>("Microsoft Internet Explorer"===navigator.appName&&parseInt(navigator.appVersion.split(";")[1].replace(/[ ]/g,"").replace("MSIE",""))<=9&&200===e.status&&e.request&&"json"===e.request.responseType&&e.request.responseText&&(e.data=JSON.parse(e.request.responseText)),e),e=>{if(e.response)switch(e.response.status){case 401:r.error("未授权，请重新登录");break;case 403:r.error("拒绝访问");break;case 404:r.error("请求的资源不存在");break;case 500:r.error("服务器内部错误");break;default:r.error("网络错误，请稍后重试")}else e.request?r.error("网络连接超时，请检查网络"):r.error("请求配置错误");return Promise.reject(e)}),s.interceptors.request.use(e=>e,e=>Promise.reject(e));const t=e.create({baseURL:"http://39.101.72.34:18080/jeecgboot",timeout:1e4,withCredentials:!1});t.interceptors.response.use(e=>("Microsoft Internet Explorer"===navigator.appName&&parseInt(navigator.appVersion.split(";")[1].replace(/[ ]/g,"").replace("MSIE",""))<=9&&200===e.status&&e.request&&"json"===e.request.responseType&&e.request.responseText&&(e.data=JSON.parse(e.request.responseText)),e),e=>{if(e.response)switch(e.response.status){case 401:r.error("未授权，请重新登录");break;case 403:r.error("拒绝访问");break;case 404:r.error("请求的资源不存在");break;case 500:r.error("服务器内部错误");break;default:r.error("网络错误，请稍后重试")}else e.request?r.error("网络连接超时，请检查网络"):r.error("请求配置错误");return Promise.reject(e)}),t.interceptors.request.use(e=>{var r,s;const t=localStorage.getItem("freedomToken")||sessionStorage.getItem("freedomToken");t&&(e.headers["x-access-token"]=t);const o=(new Date).getTime();if("get"===e.method){const s=(null==(r=e.url)?void 0:r.includes("?"))?"&":"?";e.url=`${e.url}${s}_t=${o}`}else{const r=(null==(s=e.url)?void 0:s.includes("?"))?"&":"?";e.url=`${e.url}${r}_t=${o}`}return e},e=>Promise.reject(e));export{t as a,s as h};
