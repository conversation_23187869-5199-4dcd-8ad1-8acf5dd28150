<template>
  <div class="demand-step2-container">
    <h3 class="step-title">第二步：详细信息</h3>
    <div class="step-content">
      <!-- 这里将放置第二步的表单内容 -->
      <div class="placeholder">
        <p>求购信息详细信息表单</p>
        <p>包含：预算范围、需求数量、交货期、联系方式等详细信息</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 第二步组件逻辑
</script>

<style scoped lang="scss">
.demand-step2-container {
  .step-title {
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    margin-bottom: 20px;
  }

  .step-content {
    .placeholder {
      padding: 40px;
      background-color: #f8f9fa;
      border-radius: 8px;
      text-align: center;
      border: 1px dashed #dee2e6;

      p {
        margin: 8px 0;
        color: #6c757d;
        font-size: 14px;
      }
    }
  }
}
</style>