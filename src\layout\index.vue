<template>
  <div class="layout">
    <!-- 根据路由条件渲染不同的头部组件 -->
    <Header :isRecommendPage="currentRoute === '/'" />
    <main class="main-content">
      <router-view v-slot="{ Component, route }">
        <!-- 只对特定路由应用页面过渡动画，避免影响Bidding和Freedom子页面 -->
        <transition
          :name="shouldApplyTransition(route.path) ? 'page-transition' : ''"
          mode="out-in"
        >
          <component
            :is="Component"
            :key="needsForceRerender(route.path) ? route.path : undefined"
          />
        </transition>
      </router-view>
    </main>
    <Footer />

    <!-- 自定义滚动条组件 - 排除登录页面 -->
    <CustomScrollbar v-if="!isLoginPage" />
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from "vue";
import { useRoute } from "vue-router";
import Header from "./components/Header.vue";
import Footer from "./components/Footer.vue";
import CustomScrollbar from "../components/CustomScrollbar.vue";

const route = useRoute();
const currentRoute = computed(() => route.path);

// 判断是否为登录页面
const isLoginPage = computed(() => route.path === "/login");

// 用于跟踪之前的路由路径
const previousPath = ref(route.path);

// 监听路由变化，更新之前的路径
watch(
  () => route.path,
  (newPath, oldPath) => {
    previousPath.value = oldPath || "/";
  },
  { immediate: false }
);

/**
 * 判断是否应该对当前路由应用页面过渡动画
 * @param path 当前路由路径
 * @returns 是否应用过渡动画
 */
const shouldApplyTransition = (path: string): boolean => {
  // 获取当前路由和之前的路由
  const currentPath = path;
  const prevPath = previousPath.value;

  // 定义不需要过渡动画的页面路由
  const noTransitionRoutes = ["/about", "/aboutus"];

  // 如果当前路径是关于页面，不应用动画
  if (noTransitionRoutes.some((route) => currentPath.startsWith(route))) {
    return false;
  }

  // 定义有独立布局的页面路由前缀
  const independentLayoutRoutes = ["/bidding", "/freedom"];

  // 检查当前路径和之前路径是否都在同一个独立布局页面内
  const currentInIndependentLayout = independentLayoutRoutes.some((prefix) =>
    currentPath.startsWith(prefix)
  );
  const previousInIndependentLayout = independentLayoutRoutes.some((prefix) =>
    prevPath.startsWith(prefix)
  );

  // 如果当前路径和之前路径都在同一个独立布局页面内，则不应用动画
  if (currentInIndependentLayout && previousInIndependentLayout) {
    // 进一步检查是否在同一个页面组内（bidding或freedom）
    const currentPageGroup = independentLayoutRoutes.find((prefix) =>
      currentPath.startsWith(prefix)
    );
    const previousPageGroup = independentLayoutRoutes.find((prefix) =>
      prevPath.startsWith(prefix)
    );

    // 如果在同一个页面组内切换子路由，不应用动画
    if (currentPageGroup === previousPageGroup) {
      return false;
    }
  }

  // 其他情况都应用动画（包括从其他页面切换到bidding/freedom，或从bidding/freedom切换到其他页面）
  return true;
};

/**
 * 判断是否需要强制重新渲染
 * @param path 当前路由路径
 * @returns 是否需要强制渲染
 */
const needsForceRerender = (path: string): boolean => {
  // 只在从其他页面切换到 bidding/freedom 时强制渲染
  // 在 bidding/freedom 内部切换时不强制渲染
  const currentInBiddingOrFreedom =
    path.startsWith("/bidding") || path.startsWith("/freedom");
  const previousInBiddingOrFreedom =
    previousPath.value.startsWith("/bidding") ||
    previousPath.value.startsWith("/freedom");

  // 如果当前和之前都在 bidding/freedom 内，不强制渲染
  if (currentInBiddingOrFreedom && previousInBiddingOrFreedom) {
    return false;
  }

  // 其他情况可能需要强制渲染
  return true;
};
</script>

<style lang="scss" scoped>
.layout {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.main-content {
  flex: 1;
  margin: 0 auto;
  background-color: rgb(242, 242, 242);
  width: 100vw;
  position: relative;
  overflow: hidden;
  padding-top: 0; // 去掉上边距
}

/* 页面过渡动画 - 稳重的动画效果 */
.page-transition-enter-active {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition-delay: 0.05s;
}

.page-transition-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 进入动画：轻微淡入效果，减少位移和缩放 */
.page-transition-enter-from {
  opacity: 0;
  transform: translateX(5px) scale(0.995);
}

/* 离开动画：轻微淡出效果，减少位移和缩放 */
.page-transition-leave-to {
  opacity: 0;
  // transform: translateX(-3px) scale(0.995);
}

/* 正常状态 */
.page-transition-enter-to,
.page-transition-leave-from {
  opacity: 1;
  transform: translateX(0) scale(1);
}

/* 确保过渡期间页面布局稳定 */
.page-transition-enter-active,
.page-transition-leave-active {
  position: relative;
  width: 100%;
  overflow: hidden;
}

/* 避免过渡期间的布局跳动 */
.main-content {
  position: relative;
  /* 根据不同页面动态调整最小高度，避免高度跳动 */
  /* min-height: calc(100vh - var(--header-height, 195px)); */
  transition: min-height 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}


</style>

<style lang="scss">
/* 全局样式 */

/* 确保主内容区域的padding-top变化有平滑过渡动画 */
.layout .main-content {
  transition: padding-top 0.5s cubic-bezier(0.4, 0, 0.2, 1) !important;
  will-change: padding-top;
}
</style>
