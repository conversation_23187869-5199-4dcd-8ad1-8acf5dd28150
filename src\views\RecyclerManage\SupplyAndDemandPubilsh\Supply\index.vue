<template>
  <div class="publish-supply">
    <!-- 步骤条 -->
    <div class="steps-wrapper">
      <Steps :current="currentStep" :steps="stepsList" @change="handleStepChange" />
    </div>

    <!-- 步骤内容 -->
    <div class="step-content">
      <!-- 第一步：编辑供应信息 -->
      <div v-if="currentStep === 0">
        <Step1
          ref="step1Ref"
          v-model="stepOneData"
          :location-loading="locationLoading"
          :is-edit-mode="isEditMode"
          @area-change="handleAreaChange"
          @get-current-location="getCurrentLocation"
        />

        <!-- 操作按钮 -->
        <div class="step-actions">
          <el-button type="primary" size="large" class="next-btn" @click="nextStep">
            下一步
          </el-button>
        </div>
      </div>

      <!-- 第二步：发布供应信息 -->
      <div v-if="currentStep === 1">
        <Step2 ref="step2Ref" v-model="stepTwoData" />

        <!-- 操作按钮 -->
        <div class="step-actions">
          <!-- 编辑模式下根据状态显示不同按钮 -->
          <template v-if="isEditMode">
            <!-- 如果是草稿状态，显示保存草稿和确认发布按钮 -->
            <template v-if="currentStatus === 1">
              <el-button size="large" class="draft-btn" @click="submitForm(1)">
                保存至草稿
              </el-button>
              <el-button
                type="primary"
                size="large"
                class="submit-btn"
                :loading="submitting"
                @click="submitForm(2)"
              >
                确认发布
              </el-button>
            </template>
            <!-- 如果不是草稿状态，显示保存修改按钮 -->
            <template v-else>
              <el-button
                type="primary"
                size="large"
                class="submit-btn"
                :loading="submitting"
                @click="submitForm(2)"
              >
                保存修改
              </el-button>
            </template>
          </template>
          <!-- 新增模式下显示原有按钮 -->
          <template v-else>
            <el-button size="large" class="draft-btn" @click="submitForm(1)">
              保存至草稿
            </el-button>
            <el-button
              type="primary"
              size="large"
              class="submit-btn"
              :loading="submitting"
              @click="submitForm(2)"
            >
              确认发布
            </el-button>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import Steps from '@/components/Steps/index.vue'
import Step1 from './components/step1.vue'
import Step2 from './components/step2.vue'
// 引入供应需求相关API接口和类型定义
import {
  saveOrderAndSupplyDemand,
  updateSupplyDemand,
  querySupplyDemandById
} from '@/api/supplyDemand'
import type { SaveSupplyDemandParams } from '@/api/supplyDemand'

// Step1组件引用
const step1Ref = ref<InstanceType<typeof Step1>>()
// Step2组件引用
const step2Ref = ref<InstanceType<typeof Step2>>()

// 路由实例
const router = useRouter()
const route = useRoute()

// 当前步骤
const currentStep = ref(0)

// 提交状态
const submitting = ref(false)

// 定位加载状态
const locationLoading = ref(false)

// 编辑状态
const isEditMode = ref(false)
const editId = ref<string>('')
const currentStatus = ref<number>(2) // 当前记录状态：1-草稿，2-已提交

// 步骤列表
const stepsList = [
  {
    title: '编辑供应信息',
    description: '填写供应的基本信息'
  },
  {
    title: '发布供应信息',
    description: '填写联系人信息并发布'
  }
]

// 第一步表单数据
let stepOneData = reactive({
  // 供应物资类型
  materialType: '', // 物资类型ID

  // 基本供应信息
  basicInfo: {
    infoTitle: '', // 信息标题
    brand: '', // 物资品牌
    model: '', // 物资型号
    depreciationDegree: 9 // 新旧程度（1-9对应一成新到九成新）
  },

  // 规格与存放
  specification: {
    province: '110000', // 省份
    city: '110100', // 城市
    area: '110101', // 区域
    storageMethod: 1, // 存放方式
    quantity: '', // 物资数量
    unit: '台', // 物资单位
    price: '' // 物资价格
  },

  // 亮点与展示
  display: {
    images: '', // 物资照片
    videos: '', // 物资视频
    highlights: '', // 供应亮点（输入框）
    materialDesc: '' // 物资详细描述（富文本）
  },

  // 附件列表（用于回显）
  attachmentList: [] as any[]
})

// 第二步表单数据
let stepTwoData = reactive({
  // 信息有效性
  validity: {
    validDate: '' // 有效期时间
  },

  // 联系人信息
  contactInfo: {
    contactName: '', // 联系姓名
    contactPhone: '' // 联系电话
  }
})

// 处理步骤变化
const handleStepChange = (step: number) => {
  // 只允许点击当前步骤之前的步骤返回，当前步骤之后的步骤不能通过点击步骤条跳转
  if (step < currentStep.value) {
    currentStep.value = step
  }
}

// 省市区变化处理
const handleAreaChange = (value: any) => {
  console.log('省市区变化:', value)
}

// 获取当前位置
const getCurrentLocation = () => {
  locationLoading.value = true

  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        // 模拟根据经纬度获取地址信息
        setTimeout(() => {
          locationLoading.value = false
          ElMessage.success('位置获取成功')
        }, 1000)
      },
      (error) => {
        locationLoading.value = false
        ElMessage.error('位置获取失败，请手动输入地址')
      }
    )
  } else {
    locationLoading.value = false
    ElMessage.error('浏览器不支持地理位置获取')
  }
}

// 获取供应需求详情数据
const fetchSupplyDemandDetail = async (id: string) => {
  try {
    const result = await querySupplyDemandById({ id })
    console.log('获取到的供应需求详情:', result)

    // 回显数据到表单
    if (result && result.success) {
      const { hgySupplyDemand, hgyEntrustOrder, attachmentList } = result.result

      // 回显第一步数据
      if (hgySupplyDemand) {
        stepOneData.materialType = hgySupplyDemand.materialType || ''
        stepOneData.basicInfo.infoTitle = hgySupplyDemand.infoTitle || ''
        stepOneData.basicInfo.brand = hgySupplyDemand.brand || ''
        stepOneData.basicInfo.model = hgySupplyDemand.model || ''
        stepOneData.basicInfo.depreciationDegree = hgySupplyDemand.depreciationDegree || 9

        stepOneData.specification.province = hgySupplyDemand.province || ''
        stepOneData.specification.city = hgySupplyDemand.city || ''
        stepOneData.specification.area = hgySupplyDemand.district || ''
        stepOneData.specification.storageMethod = hgySupplyDemand.storageMethod || 1
        stepOneData.specification.quantity = hgySupplyDemand.quantity?.toString() || ''
        stepOneData.specification.unit = hgySupplyDemand.unit || ''
        stepOneData.specification.price = hgySupplyDemand.price?.toString() || ''

        stepOneData.display.highlights = hgySupplyDemand.highlights || ''
        stepOneData.display.materialDesc = hgySupplyDemand.materialDesc || ''

        // 设置当前记录状态
        currentStatus.value = hgySupplyDemand.status || 2
      }

      // 回显第二步数据
      if (hgySupplyDemand) {
        stepTwoData.validity.validDate = hgySupplyDemand.validDate || ''
        stepTwoData.contactInfo.contactName = hgySupplyDemand.relationUser || ''
        stepTwoData.contactInfo.contactPhone = hgySupplyDemand.relationPhone || ''
      }

      // 回显附件信息
      if (attachmentList && Array.isArray(attachmentList) && attachmentList.length > 0) {
        stepOneData.attachmentList = attachmentList
      }
    }
  } catch (error) {
    console.error('获取供应需求详情失败:', error)
    ElMessage.error('获取详情失败')
  }
}

// 组件挂载时处理编辑模式
onMounted(async () => {
  // 检查是否为编辑模式
  const id = route.query.id as string

  if (id) {
    isEditMode.value = true
    editId.value = id

    // 获取供应需求详情
    fetchSupplyDemandDetail(id)
  }
})

// 根据文件扩展名判断文件类型的函数
const getFileTypeByExtension = (filePath: string): string => {
  if (!filePath) return 'other'

  const extension = filePath.toLowerCase().split('.').pop()

  switch (extension) {
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'webp':
      return 'image'
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
    case 'flv':
    case 'webm':
      return 'video'
    case 'mp3':
    case 'wav':
    case 'flac':
    case 'aac':
      return 'mp3'
    case 'pdf':
      return 'pdf'
    case 'doc':
    case 'docx':
      return 'word'
    case 'xls':
    case 'xlsx':
      return 'excel'
    case 'ppt':
    case 'pptx':
      return 'ppt'
    case 'zip':
    case 'rar':
    case '7z':
      return 'zip'
    default:
      return 'other'
  }
}

// 构造附件列表的通用函数
const buildAttachmentList = (bizType: string) => {
  return [
    // 图片附件
    ...(() => {
      try {
        const images = typeof stepOneData.display.images === 'string'
          ? JSON.parse(stepOneData.display.images)
          : stepOneData.display.images
        return (Array.isArray(images) ? images : []).map((file: any) => ({
          bizType,
          fileName: file.fileName,
          filePath: file.filePath,
          fileSize: file.fileSize || 0,
          fileType: getFileTypeByExtension(file.filePath)
        }))
      } catch (e) {
        console.warn('解析图片附件数据失败:', e)
        return []
      }
    })(),
    // 视频附件
    ...(() => {
      try {
        const videos = typeof stepOneData.display.videos === 'string'
          ? JSON.parse(stepOneData.display.videos)
          : stepOneData.display.videos
        return (Array.isArray(videos) ? videos : []).map((file: any) => ({
          bizType,
          fileName: file.fileName,
          filePath: file.filePath,
          fileSize: file.fileSize,
          fileType: getFileTypeByExtension(file.filePath)
        }))
      } catch (e) {
        console.warn('解析视频附件数据失败:', e)
        return []
      }
    })()
  ]
}

// 下一步
const nextStep = async () => {
  // 验证当前步骤的表单数据
  if (currentStep.value === 0) {
    // 第一步：调用Step1组件的表单校验
    const isValid = await step1Ref.value?.validateForm()
    if (!isValid) {
      return
    }
  }

  if (currentStep.value < stepsList.length - 1) {
    currentStep.value++
  }
}

// 提交表单
const submitForm = async (status: number) => {
  // 验证Step2表单数据
  const isStep2Valid = await step2Ref.value?.validateForm()
  if (!isStep2Valid) {
    return
  }

  submitting.value = true

  try {
    // 合并两步的数据
    const submitData = {
      ...stepOneData,
      ...stepTwoData
    }

    // 构造提交参数
    const apiData: SaveSupplyDemandParams = {
      hgySupplyDemand: {
        id: isEditMode.value ? editId.value : undefined,
        type: '4', // 供应类型
        infoTitle: submitData.basicInfo.infoTitle,
        materialType: Array.isArray(submitData.materialType)
          ? submitData.materialType[submitData.materialType.length - 1]
          : submitData.materialType,
        brand: submitData.basicInfo.brand,
        model: submitData.basicInfo.model,
        depreciationDegree: submitData.basicInfo.depreciationDegree,
        province: submitData.specification.province,
        city: submitData.specification.city,
        district: submitData.specification.area,
        storageMethod: submitData.specification.storageMethod,
        quantity: Number(submitData.specification.quantity),
        unit: submitData.specification.unit,
        price: Number(submitData.specification.price),
        highlights: submitData.display.highlights,
        materialDesc: submitData.display.materialDesc,
        validDate: submitData.validity.validDate,
        relationUser: submitData.contactInfo.contactName,
        relationPhone: submitData.contactInfo.contactPhone,
        status: status, // 状态(1-草稿 2-提交)
        attachmentList: buildAttachmentList('HGGY') // 供应需求的附件，使用HGGY作为业务类型
      },
      hgyEntrustOrder: {
        relationUser: submitData.contactInfo.contactName,
        relationPhone: submitData.contactInfo.contactPhone,
        entrustType: 3, // 委托类型(1-增值 2-自主 3-供需)
        serviceType: 4, // 服务类型(1-竞价委托 2-资产处置 3-采购信息 4-供应 5-求购)
        status: status // 状态(1-草稿 2-提交)
      }
    }

    console.log('提交供应需求数据:', apiData)

    let result
    if (isEditMode.value) {
      result = await updateSupplyDemand(apiData.hgySupplyDemand)
    } else {
      result = await saveOrderAndSupplyDemand(apiData)
    }

    // 提交成功后的处理
    if (result && result.success) {
      const message = status === 1 ? '保存草稿成功' : '发布成功'
      ElMessage.success(message)

      // 只有在确认发布(status=2)时才跳转，保存至草稿(status=1)不跳转
      if (status === 2) {
        // 跳转到回收商管理页面
        router.push('/recyclerManage')
      }
    } else {
      ElMessage.error(result?.message || '操作失败')
    }
  } catch (error) {
    console.error('供应需求发布失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped lang="scss">
// 主容器样式
.publish-supply {
  background-color: #fff;
  height: calc(100vh - 168px); // 减去头部和底部的高度
  margin: 0 20px 20px 20px;
  border-radius: 10px;
}

// 步骤条包装器
.steps-wrapper {
  margin-bottom: 32px;
  padding: 20px;
}

// 步骤内容
.step-content {
  margin: 0 auto; // 居中显示
  padding: 20px;
}

// 操作按钮区域
.step-actions {
  display: flex;
  justify-content: center;
  gap: 16px; // 按钮之间的间距
  margin-top: 48px; // 与表单内容的间距
  padding-top: 24px;

  .draft-btn,
  .next-btn,
  .submit-btn {
    width: 434px; // 按钮最小宽度
    height: 48px; // 按钮高度
    font-size: 16px; // 字体大小
    border-radius: 6px; // 圆角
  }

  .next-btn,
  .submit-btn {
    background-color: #004c66; // 主色调
    border-color: #004c66;

    &:hover:not(:disabled) {
      background: rgba(0, 76, 102, 0.9);
    }
  }

  .draft-btn {
    background-color: #fff;
    color: #666;
    border-color: #d9d9d9;

    &:hover {
      color: #004c66;
      border-color: #004c66;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .step-content {
    max-width: 100%;
    padding: 0 16px;
  }
}

@media (max-width: 768px) {

  .steps-wrapper {
    padding: 0 8px;
    margin-bottom: 24px;
  }

  .step-content {
    padding: 0 8px;
  }

  .step-actions {
    flex-direction: column; // 小屏幕下垂直排列
    gap: 12px;
    margin-top: 32px;

    .draft-btn,
    .next-btn,
    .submit-btn {
      width: 100%; // 小屏幕下占满宽度
    }
  }
}
</style>