<template>
  <div class="supply-publish-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">发布供应信息</h2>
      <p class="page-description">请填写您的供应信息，我们将帮助您找到合适的买家</p>
    </div>

    <!-- 发布表单内容 -->
    <div class="publish-content">
      <!-- 这里将放置具体的发布表单组件 -->
      <div class="coming-soon">
        <p>供应信息发布表单即将上线...</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 这里将添加具体的逻辑
</script>

<style scoped lang="scss">
.supply-publish-container {
  padding: 30px;
  height: 100%;

  .page-header {
    margin-bottom: 30px;
    border-bottom: 1px solid #e5e5e5;
    padding-bottom: 20px;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      color: #333333;
      margin: 0 0 8px 0;
    }

    .page-description {
      font-size: 14px;
      color: #666666;
      margin: 0;
    }
  }

  .publish-content {
    .coming-soon {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 300px;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 2px dashed #dee2e6;

      p {
        font-size: 16px;
        color: #6c757d;
        margin: 0;
      }
    }
  }
}
</style>