var e=Object.defineProperty,t=(t,s,r)=>((t,s,r)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[s]=r)(t,"symbol"!=typeof s?s+"":s,r);const s=1800,r=2500,i=100,o=.1,n=3e3;class a{constructor(){t(this,"metrics",{}),t(this,"observers",[]),t(this,"startTime"),this.startTime=performance.now(),this.initObservers(),this.monitorPageLoad()}initObservers(){"PerformanceObserver"in window&&(this.observePaintMetrics(),this.observeLayoutShift(),this.observeResourceTiming())}observePaintMetrics(){try{const e=new PerformanceObserver(e=>{for(const t of e.getEntries())"first-contentful-paint"===t.name&&(this.metrics.firstContentfulPaint=t.startTime,this.checkThreshold("FCP",t.startTime,s))});e.observe({entryTypes:["paint"]}),this.observers.push(e)}catch(e){}try{const e=new PerformanceObserver(e=>{const t=e.getEntries(),s=t[t.length-1];this.metrics.largestContentfulPaint=s.startTime,this.checkThreshold("LCP",s.startTime,r)});e.observe({entryTypes:["largest-contentful-paint"]}),this.observers.push(e)}catch(e){}}observeLayoutShift(){try{let e=0;const t=new PerformanceObserver(t=>{for(const s of t.getEntries())s.hadRecentInput||(e+=s.value);this.metrics.cumulativeLayoutShift=e,this.checkThreshold("CLS",e,o)});t.observe({entryTypes:["layout-shift"]}),this.observers.push(t)}catch(e){}}observeResourceTiming(){try{const e=new PerformanceObserver(e=>{const t=[];for(const s of e.getEntries()){const e={name:s.name,type:this.getResourceType(s.name),startTime:s.startTime,duration:s.duration,size:s.transferSize||0};t.push(e),s.duration}this.metrics.resourceLoadTimes=[...this.metrics.resourceLoadTimes||[],...t]});e.observe({entryTypes:["resource"]}),this.observers.push(e)}catch(e){}}monitorPageLoad(){document.addEventListener("DOMContentLoaded",()=>{this.metrics.domContentLoaded=performance.now()-this.startTime}),window.addEventListener("load",()=>{this.metrics.loadComplete=performance.now()-this.startTime,setTimeout(()=>{this.generatePerformanceReport()},1e3)}),this.monitorFirstInputDelay()}monitorFirstInputDelay(){try{const e=new PerformanceObserver(e=>{for(const t of e.getEntries())this.metrics.firstInputDelay=t.processingStart-t.startTime,this.checkThreshold("FID",this.metrics.firstInputDelay,i)});e.observe({entryTypes:["first-input"]}),this.observers.push(e)}catch(e){}}getResourceType(e){return e.includes(".js")?"script":e.includes(".css")?"stylesheet":e.match(/\.(png|jpg|jpeg|gif|svg|webp)$/i)?"image":e.match(/\.(woff|woff2|ttf|otf)$/i)?"font":"other"}checkThreshold(e,t,s){}generatePerformanceReport(){this.metrics.firstContentfulPaint,this.metrics.largestContentfulPaint,this.metrics.firstInputDelay,this.metrics.cumulativeLayoutShift,this.metrics.domContentLoaded,this.metrics.loadComplete,this.analyzeSlowResources()}analyzeSlowResources(){if(!this.metrics.resourceLoadTimes)return;const e=this.metrics.resourceLoadTimes.filter(e=>e.duration>n).sort((e,t)=>t.duration-e.duration).slice(0,5);e.length>0&&e.forEach((e,t)=>{})}formatBytes(e){if(0===e)return"0 Bytes";const t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(2))+" "+["Bytes","KB","MB","GB"][t]}getMetrics(){return{...this.metrics}}destroy(){this.observers.forEach(e=>{e.disconnect()}),this.observers=[]}}let c=null;function h(){return c||(c=new a),c}export{a as PerformanceMonitor,h as initPerformanceMonitor};
