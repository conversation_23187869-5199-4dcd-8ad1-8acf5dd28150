<template>
	<div class="home  min_wrapper_1500">
		<headers-index />
		<div class="home_top_back">
			<div class="home_top">
				<div class="home_left">
					<div class="home_left_top">
						<div class="home_user_left">
							<div class="home_user">
								<div class="user_top">
									<img src="@/assets/images/index/kefu.png" alt="" srcset="">
									联系客服
								</div>
								<div class="user_telphone">************</div>
								<div class="user_login" v-if="!userInfo">
									<div class="login_btn" @click="goLogin(true)">
										登录
									</div>
									<div class="login_btn register" @click="goLogin(false)">
										注册
									</div>
								</div>
								<div class="user_login" v-else>
									<div class="login_btn" @click="nextUserInfo">
										个人中心
									</div>
								</div>
								<div class="advantage">
									<div class="advantage_item">
										<img class="advantage_img" src="@/assets/images/index/baozhang.png" alt="" srcset="">
										<div class="advantage_text">资源保障</div>
									</div>
									<div class="advantage_item">
										<img class="advantage_img" src="@/assets/images/index/pipei.png" alt="" srcset="">
										<div class="advantage_text">精准匹配</div>
									</div>
									<div class="advantage_item">
										<img class="advantage_img" src="@/assets/images/index/gongkai.png" alt="" srcset="">
										<div class="advantage_text">公开透明</div>
									</div>
								</div>
								<div class="shortcuts">
									<div class="shortcuts_cont" @click="goDetailNew('1','126')">
										<img src="@/assets/images/index/liucheng.png" alt="" srcset="">
										注册流程
									</div>
									<div class="shortcuts_cont" @click="goDetailNew('1','129')">
										<img src="@/assets/images/index/baozhengjin.png" alt="" srcset="">
										保证金缴纳
									</div>
									<div class="shortcuts_cont" @click="goDetailNew('1','155')">
										<img src="@/assets/images/index/jingpai.png" alt="" srcset="">
										竞拍规则
									</div>
									<div class="shortcuts_cont" @click="goDetailNew('1','130')">
										<img src="@/assets/images/index/fuwufei.png" alt="" srcset="">
										服务费标准
									</div>
								</div>
							</div>
							<div class="home_classify">
								<div class="classify_cont">
									<div class="classify_top">
										<img class="classify_icon" src="@/assets/images/index/lei.png" alt="" srcset="">
										<div class="classify_title">标的类型</div>
									</div>
									<div class="classify_list">
										<template  v-for="item,i in typeList">
											<div class="calssify_item" :key="i" v-if="!!item.value" @click="BiaodiType(i)">{{ item.text}}</div>
										</template>
									</div>
								</div>
								<div class="classify_cont classify_address">
									<div class="classify_top">
										<div style="display:flex;">
											<img class="classify_icon" src="@/assets/images/index/diqu.png" alt="" srcset="">
											<div class="classify_title">所属地区</div>
										</div>
										<div class="news_more" @click="goBiado">
											更多
											<img src="@/assets/images/index/more.png" alt="" srcset="">
										</div>
									</div>
									<div class="classify_list">
										<div class="calssify_item" v-for="item,i in areaList" :key="i" @click="BiaodiArea(i)" v-if="i < 8">{{ item.name}}</div>
									</div>
								</div>
							</div>
						</div>
						<div class="home_banner">
							<el-carousel height="428px" :interval="4000">
								<el-carousel-item v-for="item,i in bannerList" :key="i">
									<img :src="url+item.image" alt="" @click="bannerSkip(item.url)">
								</el-carousel-item>
							</el-carousel>
							<!-- <img src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/images/banner.png" alt=""> -->
						</div>
					</div>
					<div class="home_process">
						<img class="home_process_title" src="@/assets/images/index/process.png" alt="" srcset="">
						<div class="process_list">
							<div class="process_cont" @click="goDetailNew('1','210')">
								<img class="process_cont_img" src="@/assets/images/index/process-icon1.png" alt="" srcset="">
								<div class="process_cont_right">
									<div class="process_cont_right_title">注册认证</div>
									<div class="process_cont_right_tips">第一步</div>
								</div>
							</div>
							<img class="process_cont_right_next" src="@/assets/images/index/next.png" alt="">
							<div class="process_cont" @click="goDetailNew('1','211')">
								<img class="process_cont_img" src="@/assets/images/index/process-icon2.png" alt="" srcset="">
								<div class="process_cont_right">
									<div class="process_cont_right_title">拍前准备</div>
									<div class="process_cont_right_tips">第二步</div>
								</div>
							</div>
							<img class="process_cont_right_next" src="@/assets/images/index/next.png" alt="">
							<div class="process_cont" @click="goDetailNew('1','212')">
								<img class="process_cont_img" src="@/assets/images/index/process-icon3.png" alt="" srcset="">
								<div class="process_cont_right">
									<div class="process_cont_right_title">报名交保</div>
									<div class="process_cont_right_tips">第三步</div>
								</div>
							</div>
							<img class="process_cont_right_next" src="@/assets/images/index/next.png" alt="">
							<div class="process_cont" @click="goDetailNew('1','213')">
								<img class="process_cont_img" src="@/assets/images/index/process-icon4.png" alt="" srcset="">
								<div class="process_cont_right">
									<div class="process_cont_right_title">出价竞价</div>
									<div class="process_cont_right_tips">第四步</div>
								</div>
							</div>
							<img class="process_cont_right_next" src="@/assets/images/index/next.png" alt="">
							<div class="process_cont" @click="goDetailNew('1','214')">
								<img class="process_cont_img" src="@/assets/images/index/process-icon5.png" alt="" srcset="">
								<div class="process_cont_right">
									<div class="process_cont_right_title">竞价成功</div>
									<div class="process_cont_right_tips">第五步</div>
								</div>
							</div>
							<img class="process_cont_right_next" src="@/assets/images/index/next.png" alt="">
							<div class="process_cont" @click="goDetailNew('1','215')">
								<img class="process_cont_img" src="@/assets/images/index/process-icon6.png" alt="" srcset="">
								<div class="process_cont_right" style="width:60px">
									<div class="process_cont_right_title">办理交割</div>
									<div class="process_cont_right_tips">第六步</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="home_right">
					<div class="home_news">
						<div class="news_head">
							<div class="news_label">竞价公告</div>
							<div class="news_more" @click="toNotice">
								更多
								<img src="@/assets/images/index/more.png" alt="" srcset="">
							</div>
						</div>
						<seamless-scroll-news ref="scroll" :list="paimaihuiList" title="pmh_name" date="start_time_name"></seamless-scroll-news>
					</div>
					<div class="home_news">
						<div class="news_head">
							<div class="news_label">采购信息</div>
							<div class="news_more" @click="toAnnouncement(29)">
								更多
								<img src="@/assets/images/index/more.png" alt="" srcset="">
							</div>
						</div>
						<seamless-scroll-news ref="scroll" :list="zhaobiaolist" title="title" date="addtime"></seamless-scroll-news>
					</div>
					<div class="home_news" style="margin-bottom:0">
						<div class="news_head">
							<div class="news_label">销售信息</div>
							<div class="news_more" @click="toAnnouncement(30)">
								更多
								<img src="@/assets/images/index/more.png" alt="" srcset="">
							</div>
						</div>
						<seamless-scroll-news ref="scroll" :list="xiaoshoulist" title="title" date="addtime"></seamless-scroll-news>
					</div>
				</div>
			</div>
		</div>
		<!-- <div class="home_settle_in">
			<img @click="goEnter" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/images/settle1.png">
			<img @click="goEnter" src="https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/images/settle2.png">
		</div> -->
		<div class="homeCentetTop">
			<div class="homeCentet_four_title">
				<img class="homeCentet_four_title_left" src="@/assets/images/index/aution.png" alt="">
				<div class="homeCentet_four_title_right" @click="goPaimaihui()">
					<span>查看全部</span>	
					<img src="@/assets/images/index/xiayibu.png">
				</div>
			</div>
			<div class="homeCentet_three">
				<div class="homeCentet_three_left">
					<template v-for="(item,i) in jinqiList">
						<!-- 标的列表 -->
						<div class="homeCentet_three_left_two" :key="i"
							@click="goDetail(item)">
							<div class="homeCentet_three_left_two_img">
								<div v-if="item.pmh_xingshi == 2" class="homeCentet_three_left_two_ri_backRight" style="background:#316ccb">
									<div>线上竞价</div>
								</div>
								<div v-if="item.pmh_xingshi == 1" class="homeCentet_three_left_two_ri_backRight">
									<div>同步竞价</div>
								</div>
								<img :src="url + item.pmh_pic" @error="(e)=>e.target.src = item.pmh_pic ? 'https://oss.yunpaiwang.com/'+item.pmh_pic : ''">
							</div>
							<div class="homeCentet_three_left_two_ri">
								<!-- <div class="home_aution_info"> -->
								<!-- <div class="home_aution_info_top"> -->
								<div class="homeCentet_three_left_two_ri_one">
									{{item.pmh_name}}
								</div>
									<!-- <div class="homeCentet_three_left_two_ri_ones">
										拍卖公司： <span>{{item.parentname}}</span>
									</div>
									<div class="homeCentet_three_left_two_ri_ones">
										截止报名： <span>{{item.bm_time_name}}</span>
									</div> -->
								<!-- </div> -->
								<!-- </div> -->
								<div class="homeCentet_three_left_two_ri_bottom">
									<div class="home_aution_time">
										<div v-if="item.pmh_status==2">
											结束时间：{{item.end_time_name}}
										</div>

										<div v-else>
											开拍时间：{{item.start_time}}
										</div>
									</div>
									<div class="home_aution_detail">
										查看详情
									</div>
								</div>
								<div class="homeCentet_three_left_two_ri_two">
									<div class="biaodiMulu" @click.stop="biaodiInfo(item)">
										标的目录
									</div>
									<!-- <div class="homeCentet_three_left_two_ri_two_cont">
										<img
											src="@/assets/images/index/weiguan.png">
										<div><span>{{item.pmh_weiguan}}</span>次围观</div>
									</div> -->
									<div class="homeCentet_three_left_two_ri_two_cont">
										<img
											src="@/assets/images/index/redu.png">
										<div><span>{{item.biaodinums}}</span>个标的</div>
									</div>
								</div>
							</div>
						</div>
					</template>
				</div>
				<!-- 右侧公告 -->
				<!-- <div class="homeCentet_three_right">
					<div class="homeCentet_three_right_div">
						<div class="homeCentet_three_right_div_one">
							<div>竞价公告</div>
							<div @click="toNotice">更多> </div>
						</div>
						<div class="homeCentet_three_right_div_three">
							<template v-for="(item,i) in paimaihuiList">
								<div class="homeCentet_three_right_div_three_cont" v-if="i < 6" :key="i" @click="toNoticeDetail(item)">
									<div class="homeCentet_three_right_div_three_red"></div>
									<div class="homeCentet_three_right_div_three_right">
										<div class="homeCentet_three_right_div_three_txt">{{item.pmh_name}}</div>
										<div class="homeCentet_three_right_div_three_time">{{item.start_time_name}}</div>
									</div>
								</div>
							</template>
						</div>
					</div>
					<div class="homeCentet_three_right_div">
						<div class="homeCentet_three_right_div_one">
							<div>行业资讯</div>
							<div @click="goHangye">更多> </div>
						</div>
						<div class="homeCentet_three_right_div_three">
							<template v-for="(item,i) in hangyeList">
								<div class="homeCentet_three_right_div_three_cont" v-if="i < 6" :key="i" @click="goDetailNew('2',item.id)">
									<div class="homeCentet_three_right_div_three_red"></div>
									<div class="homeCentet_three_right_div_three_right">
										<div class="homeCentet_three_right_div_three_txt">{{item.title}}</div>
										<div class="homeCentet_three_right_div_three_time">{{item.addtime}}</div>
									</div>
								</div>
							</template>
						</div>
					</div>
				</div> -->
			</div>
			<div class="home_hot" v-if="false">
				<div class="home_hot_cont">
					<div class="homeCentet_four_title" style="margin-top:46px">
						<img class="homeCentet_four_title_left" src="@/assets/images/index/hot_title.png" alt="">
					</div>
					<div class="home_hot_item">
						<div class="home_hot_item_statistics">
							<div class="home_hot_item_statistics_num">
								<span>3674</span>
								<br/>
								热点标的
							</div>
							<div class="home_hot_item_statistics_more" @click="goBiado()">
								进入热点标的
								<img src="@/assets/images/index/hot_next.png" alt="">
								<!-- <div class="home_hot_item_statistics_circle"></div> -->
							</div>
						</div>
						<div class="homeCentet_four_centet">
							<div class="homeCentet_four_centet_item" @click="gobiaodiInfo(item)"
								v-for="(item,i) in DayBiaoList" v-if="i<3">
								<div class="homeCentet_four_centet_imgview">
									<img :src="url+item.bd_url" @error="(e)=>e.target.src = item.bd_url ? 'https://oss.yunpaiwang.com/'+item.bd_url :''">
									<div class="homeCentet_four_centet_item_type">
										<div v-if="item.bd_status == 1  " class="homeCentet_four_centet_view_enters">
											即将开始
										</div>
										<div v-else-if="item.bd_status == 2 ||item.bd_status == 3 "
											class="homeCentet_four_centet_view_enters homeCentet_four_centet_view_enterss">
											正在竞价
										</div>
										<div v-else-if="item.bd_status == 5 "
											class="homeCentet_four_centet_view_enters homeCentet_four_centet_view_enterss">
											已成交
										</div>
										<div v-else
											class="homeCentet_four_centet_view_enters homeCentet_four_centet_view_entersss">
											{{item.bd_status_name}}
										</div>
									</div>
								</div>
								<div class="homeCentet_four_centet_view_top">
									<div class="homeCentet_four_centet_view_title">{{item.bd_title}}</div>
									<div class="homeCentet_four_centet_view_num">
										<img src="@/assets/images/index/weiguan.png" alt="">
										<span>{{item.bd_weiguan}}</span>次围观
									</div>
								</div>
								<div class="homeCentet_four_centet_view_price">
									<!-- <template v-if="userInfo"> -->
									<div class="homeCentet_four_centet_view_price_left homeCentet_four_centet_view_price_left2" v-if="item.bd_status == 5">
											成交价 
												<span> {{item.jiage}} </span>{{item.cjj_danwie}}
									</div>
									<div class="homeCentet_four_centet_view_price_left" v-else>
										起拍价 <span> {{item.jiage}}
											</span>{{item.qpj_danwie}}
									</div>
									<!-- </template>
									<template v-else>
										<div class="homeCentet_four_centet_view_price_left">
											登录后查看价格
										</div>
									</template> -->
									<div class="homeCentet_four_centet_view_price_right">
										<span v-if="item.bd_status != 2 && item.bd_status != 3 &&  item.bd_status != 1">查看详情</span>
										<span v-else>立即报名</span>
									</div>
								</div>
								<div class="homeCentet_four_centet_view_line"></div>
								<div v-if="item.bd_status != 5 && item.bd_status != 4 && item.bd_status != 8"
									class="homeCentet_four_centet_view_three">
									预 计 <span>{{item.start_time}}</span> 开始
								</div>
								<div v-else class="homeCentet_four_centet_view_three">
									结束时间 <span>{{item.end_time | formatDate}}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 成交案例 -->
			<div class="homeCentet_four" style="padding: 1px 0;margin-bottom:0px">
				<div class="homeCentet_four_title">
					<img class="homeCentet_four_title_left" src="@/assets/images/index/deal.png" alt="">
					<div class="homeCentet_four_title_right" @click="goChengjiao()">
						<span>查看全部</span>	
						<img src="@/assets/images/index/xiayibu.png">
					</div>
				</div>
				<!-- <div class="homeCentet_four_title">
					<img class="homeCentet_four_title_left" style="width:236px" src="@/assets/images/index/deal.png" alt="">
					<div class="homeCentet_four_title_right" @click="goChengjiao()">
							累计成交
							<span v-for="item,i in leijichengjao" :key="i" class="leiji_jianxi">{{item}}</span>
							个标的
						<img src="../assets/index/hot_next.png">
					</div>
				</div> -->
				
				<div class="homeCentet_four_centet1">
					<div class="homeCentet_four_centet_item" @click="gobiaodiInfo(item)"
						v-for="item,i in daycjbiaodiList" :key="i">
						<div class="homeCentet_four_centet_imgview">
							<img :src="url+item.bd_url" @error="(e)=>e.target.src = item.bd_url ? 'https://oss.yunpaiwang.com/'+item.bd_url : ''">
							<div class="homeCentet_four_centet_item_type">
								<div class="homeCentet_four_centet_view_enters" style="background:#A80012">
									已成交
								</div>
							</div>
						</div>
						<div class="homeCentet_four_centet_view_top">
							<div class="homeCentet_four_centet_view_title">{{item.bd_title}}</div>
							<div class="homeCentet_four_centet_view_num">
								<img src="@/assets/images/index/weiguan.png" alt="">
								<span>{{item.bd_weiguan}}</span>次围观
							</div>
						</div>
						<div class="homeCentet_four_centet_view_price">
							<!-- <template v-if="userInfo"> -->
							<div class="homeCentet_four_centet_view_price_left homeCentet_four_centet_view_price_left2">
									成交价 <span> {{item.bd_chengjiaojia}} </span>{{item.cjj_danwie}}
							</div>
							<!-- </template> -->
							<!-- <template v-else>
							<div class="homeCentet_four_centet_view_price_left homeCentet_four_centet_view_price_left2">
									登录后查看价格
							</div>
							</template> -->
							<div class="homeCentet_four_centet_view_price_right">
								<span>查看详情</span>
							</div>
						</div>
						<div class="homeCentet_four_centet_view_line"></div>
						<div class="homeCentet_four_centet_view_three">
							结束时间 <span>{{item.end_time}}</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 合作企业 -->
		<div class="homeCentet_four">
			<div class="homeCentet_four_title">
				<img class="homeCentet_four_title_left" src="@/assets/images/index/enter.png" alt="">
				<div class="homeCentet_four_title_right" @click="goPrise()">
					<span>查看全部</span>	
					<img src="@/assets/images/index/xiayibu.png">
				</div>
			</div>
			<div class="homeCentet_four_centets">
				<template v-for="item,i in hzqiyelist">
					<div class="homeCentet_four_centetsQi"
						:key="i" v-if="i < 5">
						<img :src=" url + item.image">
					</div>
				</template>
			</div>
		</div>
		<footers-bottom />
		<home-right />
	</div>
</template>

<script>
	// import homeRight from '@/components/homeRight.vue'
	import area from '../assets/js/area.js'
	import ajax from '../store/Ajax'
	import seamlessScrollNews from '@/components/seamlessScrollNews'
	export default {
		name: 'Home',
		components:{
			seamlessScrollNews
		},
		data() {
			return {
				areaList: [],
				typeList:[],
				bannerList:[],
				userInfo:null,
				selectIndex: 0,
				// 近期竞价会
				jinqiList: [],
				// 首页竞价会公告
				paimaihuiList: [],
				// 首页行业信息
				hangyeList: [],
				// 今日拍卖标的
				DayBiaoList: [],
				// 首页成交案例
				daycjbiaodiList: [],
				leijichengjao:0,
				// 平台合作企业列表
				hzqiyelist: [],
				zhaobiaolist:[],//招标公告列表
				xiaoshoulist:[],//销售公告列表
			}
		},
		filters: {
			formatDate: function(value) {
				value = value * 1000
				let date = new Date(value);
				// console.log(date)
				let y = date.getFullYear();
				// console.log(y)
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return MM + '月' + d + '日' + h + ':' + m + ':' + s;
			}
		},
		created() {
			if (localStorage.getItem('userInfo')) {
				this.userInfo = JSON.parse(localStorage.getItem('userInfo'))
			}
			this.url = this.$Pc.ossUrl
			// 所属省份
			let arae = area.area.province_list
			for (var key in arae) {
				this.areaList.push({
					id: key,
					name: arae[key].replace('省', '').replace('市', '')
				})
			}
			this.areaList.sort((a, b) => {
				return a.name.length - b.name.length
			});
			this.getbdtype() // 获取标的类型
			this.getbannerList() //banner图
			this.getjinqi()
			this.getActionList()
			// this.getNews()
			this.getToday()
			this.getChengjiao()
			this.getHezuo()
			this.getGonggao()
		},
		methods: {
			getGonggao(){
				// 招标
				ajax.zhaobiaolist({
                    cateid:29,
                    limit:20,
					page: 1
				}).then(res => {
					this.zhaobiaolist = res.data.data
				})
				// 销售
				ajax.zhaobiaolist({
                    cateid:30,
                    limit:20,
					page: 1
				}).then(res => {
					this.xiaoshoulist = res.data.data
				})
			},
			goDetailNew(cateid, id) {
				this.$router.push({
					path: '/news',
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						cateid: cateid,
						id: id
					}
				})
			},
			goPrise(){
				this.$router.push({
					path: '/enterprise',
				})
			},
			bannerSkip(url){
				if(url){
					window.open(url, '_blank');
				}
			},
			getbannerList(){
				ajax.HomeBanner().then(res => {
					this.bannerList = res.data.data
				})
			},
			// 点击标的类型
			BiaodiType(index){
				this.$router.push({ //核心语句
					path: '/biaodi', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						typeIndex: index
					}
				})
			},
			// 点击所属地区
			BiaodiArea(index){
				this.$router.push({ //核心语句
					path: '/biaodi', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						areaIndex: index+1
					}
				})
			},
			// 获取标的类型
			getbdtype(){
				// 标的参数
				ajax.biaodicanshu().then(res => {
					this.typeList = res.data.bd_type
					// this.starttime = res.data.starttime
					// this.status = res.data.zhuangtai
				})
			},
			// 登录注册页面
			goLogin(islogin){
				if(islogin)
					this.$router.push({
						//核心语句
						path: "/login", //跳转的路径
						query: {
						//路由传参时push和query搭配使用 ，作用时传递参数
						isReject: false,
						},
					});
				else
					this.$router.push("/login");
			},
			// 个人中心
			nextUserInfo() {
				if (localStorage.getItem("userInfo")) {
					this.$router.push("/userInfo");
				} else {
					this.$router.push({
					//核心语句
					path: "/login", //跳转的路径
					query: {
						//路由传参时push和query搭配使用 ，作用时传递参数
						isReject: false,
					},
					});
				}
			},
			getHezuo(){
				// 平台合作企业列表
				ajax.hzqiyelist({page:1}).then(res => {
					this.hzqiyelist = res.data.data
				}).catch(err => {

				})
			},
			getChengjiao(){
				// 首页成交案例
				ajax.HomeDaycjbiaodio().then(res => {
					this.daycjbiaodiList = res.data.data
					this.leijichengjao = res.count
				}).catch(err => {

				})
			},
			getToday(){
				// 今日拍卖标的
				ajax.daybiaodi().then(res => {
					this.DayBiaoList = res.data.data
				}).catch(err => {

				})
			},
			// 近期竞价会
			getjinqi(){
				ajax.HomeJinqipaimaihui({
					day: this.selectIndex + 1
				}).then(res => {
					this.jinqiList = res.data
				}).catch(err => {

				})
			},
			getActionList(){
				// 首页竞价会公告
				ajax.paimaihui().then(res => {
					this.paimaihuiList = res.data.data
				}).catch(err => {

				})
			},
			getNews(){
				// 首页新闻列表展示
				ajax.newsindex({
					cateid: 2
				}).then(res => {
					this.hangyeList = res.data.data
				}).catch(err => {

				})
			},
			goDetailNew(cateid, id) {
				this.$router.push({
					path: '/news',
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						cateid: cateid,
						id: id
					}
				})
			},
			goEnter() {
				this.$router.push({
					path: '/enter'
				})
			},
			goPaimaihui(){
				this.$router.push({ //核心语句
					path: '/auction', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						ac_status: 1
					}
				})
			},
			goDetail(item) {
				let routeData = this.$router.resolve({ //核心语句
					path: '/auctionDetail/' + item.id, //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: item.id,
						ispaimaih: true
					}
				})
				window.open(routeData.href, '_blank');
			},
			biaodiInfo(item) {
				let routeData = this.$router.resolve({ //核心语句
					path: '/targetmulu', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: item.id,
					}
				})
				window.open(routeData.href, '_blank');
			},
			toAnnouncement(id){
				this.$router.push({ //核心语句
					path: '/announcement', //跳转的路径
					query: {
						id
					}
				})
			},
			toNotice() {
				this.$router.push({ //核心语句
					path: '/notice', //跳转的路径
				})
			},
			toNoticeDetail(item) {
				this.$router.push({ //核心语句
					path: '/noticeDetail', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: item.id
					}
				})
			},
			goHangye() {
				this.$router.push({
					path: '/news',
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						cateid: 2
					}
				})
			},
			goBiado(i) {
				this.$router.push({ //核心语句
					path: '/biaodi', //跳转的路径
				})
			},
			gobiaodiInfo(item) {
				let routeData = this.$router.resolve({ //核心语句
					path: '/auctionDetail/' + item.id, //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						id: item.id,
						ispaimaih: false
					}
				})
				window.open(routeData.href, '_blank');
			},
			goChengjiao(i) {
				this.$router.push({ //核心语句
					path: '/biaodi', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						bd_status: 5
					}
				})
			},
		}
	}
</script>
<style lang="scss" scoped="scoped">
	.home_top_back{
		width: 100%;
		// background: #F2F2F2;
		overflow: hidden;
		.home_top{
			display: flex;
			justify-content: space-between;
			width: 1200px;
			margin: 0px auto;
			overflow: hidden;
			.home_left_top{
				display: flex;
				justify-content: space-between;
				.home_user_left{
					.home_user{
						width: 238px;
						// height: 218px;
						padding: 0 10px;
						overflow: hidden;
						border: 1px solid #E7E7E7;
						box-sizing: border-box;
						.user_top{
							display: flex;
							align-items: center;
							justify-content: center;
							margin-top: 10px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 14px;
							color: #666666;
							line-height: 20px;
							text-align: left;
							font-style: normal;
							img{
								display: block;
								width: 16px;
								margin-right: 10px;
							}
						}
						.user_telphone{
							margin-top: 8px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 16px;
							color: #2A2A2A;
							line-height: 22px;
							text-align: center;
						}
						.user_login{
							display: flex;
							align-items: center;
							justify-content: center;
							margin-top: 10px;
							.login_btn{
								display: flex;
								justify-content: center;
								align-items: center;
								min-width: 52px;
								height: 26px;
								border-radius: 4px;
								padding: 0 10px;
								border: 1px solid #D37F88;
								box-sizing: border-box;
								font-family: PingFangSC, PingFang SC;
								font-weight: 400;
								font-size: 14px;
								line-height: 24px;
								box-sizing: border-box;
								color: #A80012;
								cursor: pointer;
								&.register{
									margin-left: 20px;
									background: #A80012;
									color: #fff;
								}
							}
						}
					}
					.advantage{
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
						margin-top: 20px;
						padding: 0 2px 10px;
						border-bottom: 1px solid #E7E7E7;
						box-sizing: border-box;
						.advantage_item{
							display: flex;
							align-items: center;
							// margin-right: 17px;
							.advantage_img{
								display: block;
								width: 14px;
								margin-right: 3px;
							}
							.advantage_text{
								font-family: PingFangSC, PingFang SC;
								font-weight: 400;
								font-size: 12px;
								color: #2A2A2A;
								line-height: 17px;
							}
							&:last-child{
								margin-right: 0;
							}
						}
					}
					.shortcuts{
						display: flex;
						flex-wrap: wrap;
						justify-content: space-between;
						width: 100%;
						margin-top: 10px;
						padding: 0 10px;
						box-sizing: border-box;
						.shortcuts_cont{
							display: flex;
							align-items: center;
							margin-bottom: 10px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 12px;
							color: #666666;
							line-height: 17px;
							cursor: pointer;
							img{
								display: block;
								width: 16px;
								margin-right: 8px;
							}
						}
					}
				}
			}
			.home_classify{
				width: 238px;
				// height: 428px;
				height: fit-content;
				margin-right: 10px;
				margin-top: 10px;
				background: #FFFFFF;
				border: 1px solid #DDDDDD;
				box-sizing: border-box;
				.classify_cont{
					margin: 18px 0px 0px 20px;
					.classify_top{
						display: flex;
						align-items: center;
						.classify_icon{
							display: block;
							width: 20px;
							margin-right: 8px;
						}
						.classify_title{
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 16px;
							color: #2A2A2A;
							line-height: 20px;
							text-align: left;
							font-style: normal;
						}
						.news_more{
							display: flex;
							align-items: center;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 12px;
							color: #666666;
							line-height: 16px;
							cursor: pointer;
							img{
								display: block;
								width: 12px;
								margin-left: 0px;
							}
						}
					}
					.classify_list{
						display: flex;
						flex-wrap: wrap;
						// display: grid;
						// grid-template-columns: repeat(4,25%);
						// grid-row-gap: 10px;
						margin-top: 14px;
						.calssify_item{
							margin-right: 21px;
							margin-bottom: 10px;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 12px;
							color: #333333;
							line-height: 17px;
							text-align: left;
							cursor: pointer;
						}
					}
					&.classify_address{
						margin: 3px 0px 10px 20px;
						.classify_top{
							justify-content: space-between;
							.news_more{
								margin-right: 14px;
							}
						}
						.calssify_item{
							width: 57px;
							margin-right: 0;
							&:nth-child(4n){
								width: 44px;
								// margin-right: 20px;
							}
						}
					}
				}
			}
			.home_banner{
				width: 652px;
				height: 428px;
				overflow: hidden;
				img{
					display: block;
					width: 100%;
					cursor: pointer;
				}
			}
			.home_right{
				width: 290px;
				.home_news{
					width: 290px;
					height: 183px;
					margin-bottom: 10px;
					padding: 0 10px;
					border: 1px solid #DDDDDD;
					box-sizing: border-box;
					overflow: hidden;
					.news_head{
						display: flex;
						align-items: center;
						justify-content: space-between;
						width: 100%;
						height: 30px;
						margin-top: 4px;
						.news_label{
							font-family: PingFangSC, PingFang SC;
							font-weight: 600;
							font-size: 16px;
							color: #343434;
							line-height: 20px;
						}
						.news_more{
							display: flex;
							align-items: center;
							font-family: PingFangSC, PingFang SC;
							font-weight: 400;
							font-size: 12px;
							color: #666666;
							line-height: 16px;
							cursor: pointer;
							img{
								display: block;
								width: 12px;
								margin-left: 4px;
							}
						}
					}
					.news_list{
						width: 100%;
						margin-top: 5px;
						.news_item{
							position: relative;
							width: 100%;
							margin-bottom: 10px;
							padding-left: 13px;
							box-sizing: border-box;
							.news_red{
								position: absolute;
								left: 0;
								width: 5px;
								height: 5px;
								margin-top: 8px;
								margin-right: 8px;
								background: #E24555;
								border-radius: 50%;
							}
							.news_cont{
								width: 100%;
								.news_title{
									font-family: PingFangSC, PingFang SC;
									font-weight: 400;
									font-size: 13px;
									color: #343434;
									line-height: 20px;
									overflow: hidden;
									text-overflow: ellipsis;
									white-space: nowrap;
								}
								.news_time{
									margin-top: 0px;
									font-family: PingFangSC, PingFang SC;
									font-weight: 400;
									font-size: 12px;
									color: #777777;
									line-height: 16px;
								}
							}
						}
					}
				}
			}
		}
	}
	.home_process{
		width: 100%;
		margin: 0 auto;
	}
	.home_process_title{
		display: block;
		// width: 240px;
		// height: 58px;
		margin-top: 20px;
	}
	.process_list{
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 10px;
	}
	.process_cont{
		display: flex;
		align-items: center;
		cursor: pointer;
	}
	.process_cont_img{
		display: block;
		width: 46px;
		height: 46px;
		margin-right: 6px;
	}
	.process_cont_right_title{
		font-size: 14px;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 400;
		color: #333333;
		line-height: 20px;
	}
	.process_cont_right_tips{
		margin-top: 5px;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666;
		line-height: 17px;
	}
	.process_cont_right_next{
		display: block;
		width: 16px;
		height: 16px;
	}
	.home_settle_in{
		display: flex;
		width: 1200px;
		margin: 48px auto 0;
		img{
			display: block;
			width: 50%;
			cursor: pointer;
		}
	}
	.homeCentet_four{
		// margin-top: 96px;
		overflow: hidden;
	}
	.homeCentetTop{
		margin-top: 25px;
		background: #f2f2f2;
		overflow: hidden;
	}
	.homeCentet_four_title {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		width: 1200px;
		margin: 20px auto 10px;
	}
	.homeCentet_four_title_left {
		display: block;
	}
	.homeCentet_four_title_right {
		display: flex;
		-js-display: flex;
		align-items: center;
		font-size: 18px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 30px;
		cursor: pointer;
	}
	.homeCentet_four_title_right>img{
		width: 20px;
		height: 20px;
		margin-left: 5px;
	}
	.homeCentet_three {
		// display: flex;
		// justify-content: space-between;
		margin: 14px auto 0;
		width: 1200px;
		.homeCentet_three_left{
			display: flex;
			flex-wrap: wrap;
			width: 100%;
		}
	}
	.homeCentet_three_left_two {
		position: relative;
		width: 290px;
		height: 350px;
		margin-right: 12px;
		background: #FFFFFF;
		border-radius: 8px;
		margin-bottom: 16px;
		transition: all .2s;
		overflow: hidden;
		cursor: pointer;
		&:nth-child(4n){
			margin-right: 0;
		}
	}
	.homeCentet_four_centet_item_span{
		width: 100%;
		height: 100%;
		border-radius: 20px;
		display: flex;
		-js-display: flex;
		align-items: center;
		padding: 16px;
		overflow: hidden;
		/* border: 2px solid #F5F5F5; */
		cursor: pointer;
		box-sizing: border-box;
		transition: all .2s;
	}
	.homeCentet_three_left_two_img {
		position: relative;
		display: flex;
		align-items: center;
		width: 290px;
		height: 196px;
		overflow: hidden;
	}
	.homeCentet_three_left_two_img img{
		width: 100%;
	}
	.homeCentet_three_left_two_ri_backRight {
		position: absolute;
		left: 0;
		top: 15px;
		width: 80px;
		height: 30px;
		background: #A80012;
		border-radius: 0px 100px 100px 0px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 14px;
		color: #FFFFFF;
		line-height: 30px;
		text-align: center;
	}
	.homeCentet_three_left_two_ri {
		position: relative;
		// width: 470px;
		// height: 236px;
		// margin-left: 16px;
		box-sizing: border-box;
	}
	.home_aution_info{
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding-top: 8px;
		padding-bottom: 18px;
		border-bottom: 1px solid #EDEDED;
	}
	.homeCentet_three_left_two_ri_one {
		// width: 100%;
		margin: 15px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 16px;
		color: #333333;
		line-height: 25px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.homeCentet_three_left_two_ri_ones {
		margin-top: 15px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 16px;
		color: #666666;
		line-height: 20px;
		span{
			color: #2A2A2A;
		}
	}
	.biaodiMulu {
		// position: absolute;
		// right: 0px;
		// top: 0px;
		width: 86px;
		height: 30px;
		background: #F9664F;
		border-radius: 4px;
		font-size: 14px;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 400;
		color: #FFFFFF;
		text-align: center;
		line-height: 30px;
		cursor: pointer;
	}
	.homeCentet_three_left_two_ri_two {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		width: 266px;
		height: 48px;
		margin: 20px auto 0;
		border-top: 1px solid #E9E9E9;
		font-size: 14px;
		font-weight: 400;
		color: #7A7A7A;
		box-sizing: border-box;
	}
	.homeCentet_three_left_two_ri_two_cont {
		display: flex;
		-js-display: flex;
		align-items: center;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 17px;
	}
	.homeCentet_three_left_two_ri_two_cont img{
		width: 16px;
		height: 16px;
		margin-right: 3px;
	}
	.homeCentet_three_left_two_ri_two_cont span{
		font-size: 16px;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 400;
		color: #333333;
		line-height: 25px;
	}
	.homeCentet_three_left_two_ri_bottom {
		position: relative;
		display: flex;
		-js-display: flex;
		align-items: center;
		width: 280px;
		height: 30px;
	}
	.home_aution_time{
		position: absolute;
		z-index: 1;
		left: 0px;
		top: 0;
		width: 200px;
		height: 30px;
		padding-left: 15px;
		background: linear-gradient(90deg, #FB6952 3%, #CC120D 100%);
		border-radius: 0px 22px 22px 0px;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #fff;
		line-height: 30px;
		box-sizing: border-box;
	}
	.home_aution_detail{
		position: absolute;
		z-index: 0;
		right: 0px;
		top: 0;
		width: 314px;
		height: 30px;
		padding-right: 20px;
		background: linear-gradient(118deg, #F9F9F9 0%, #FFC993 100%);
		border-radius: 0px 22px 22px 0px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 600;
		font-size: 12px;
		color: #CC0319;
		text-align: right;
		line-height: 30px;
		box-sizing: border-box;
	}

	.homeCentet_three_left_two_ri_bottom img {
		height: 36px;
	}

	.homeCentet_three_left_two_ri_bottom_pp {
		position: absolute;
		width: 244px;
		height: 36px;
		/* text-align: center; */
		font-size: 14px;
		font-weight: 400;
		color: #FFFFFF;
		top: 0;
		line-height: 36px;
		padding-left: 15px;
		box-sizing: border-box;
	}

	//鼠标悬浮特效开始
	// .homeCentet_three_left_two::before, .homeCentet_three_left_two::after{
	// 	content:"";
	// 	width: 0;
	// 	height: 2px;
	// 	border-radius: 20px;
	// 	position: absolute;
	// 	transition: all 0.2s linear;
	// 	background: #A80012;
	// }

	// .homeCentet_four_centet_item_span::before, .homeCentet_four_centet_item_span::after{
	// 	content:"";
	// 	width:2px;
	// 	height:0;
	// 	border-radius: 20px;
	// 	position: absolute;
	// 	transition: all 0.2s linear;
	// 	background: #A80012;
	// }
	// .homeCentet_three_left_two:hover::before, .homeCentet_three_left_two:hover::after{
	// 	width: 100%;
	// }
	// .homeCentet_three_left_two:hover .homeCentet_four_centet_item_span::before, .homeCentet_three_left_two:hover .homeCentet_four_centet_item_span::after{
	// 	height: 100%;
	// }
	// /*----- button 6 -----*/
	// .homeCentet_three_left_two::before{
	// 	left: 50%;
	// 	top: 0;
	// 	transition-duration: 0.4s;
	// }
	// .homeCentet_three_left_two::after{
	// 	left: 50%;
	// 	bottom: 0;
	// 	transition-duration: 0.4s;
	// }
	// .homeCentet_three_left_two .homeCentet_four_centet_item_span::before{
	// 	left: 0;
	// 	top: 50%;
	// 	transition-duration: 0.4s;
	// }
	// .homeCentet_three_left_two .homeCentet_four_centet_item_span::after{
	// 	right: 0;
	// 	top: 50%;
	// 	transition-duration: 0.4s;
	// }
	// .homeCentet_three_left_two:hover::before, .homeCentet_three_left_two:hover::after{
	// 	left: 0;
	// }
	// .homeCentet_three_left_two:hover .homeCentet_four_centet_item_span::before, .homeCentet_three_left_two:hover .homeCentet_four_centet_item_span::after{
	// 	top: 0;
	// }
	// .homeCentet_four_centet_item_span:hover{
	// 	background: #fff;
	// }
	// 鼠标悬浮特效end
	
	.homeCentet_three_right_div {
		width: 288px;
		background: #F5F5F5;
		border-radius: 14px;
		// padding: 20px 15px 10px;
		box-sizing: border-box;
		margin-bottom: 12px!important;
	}

	.homeCentet_three_right_div_one {
		width: 100%;
		height: 66px;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 24px;
		border-bottom: 1px solid #E7E9EC;
		font-size: 16px;
		font-family: PingFangSC-Semibold, PingFang SC;
		font-weight: 600;
		color: #343434;
		line-height: 20px;
		box-sizing: border-box;
	}

	.homeCentet_three_right_div_one>div:nth-child(2) {
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 16px;
		cursor: pointer;
	}
	.homeCentet_three_right_div_three {
		width: 240px;
		font-size: 12px;
		font-weight: 400;
		color: #333333;
		margin: 12px auto 0;
		overflow: hidden;
	}
	.homeCentet_three_right_div_three_cont{
		display: flex;
		// align-items: center;
		margin-bottom: 24px;
		cursor: pointer;
	}
	.homeCentet_three_right_div_three_txt{
		width: 100%;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 13px;
		color: #343434;
		line-height: 20px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.homeCentet_three_right_div_three_red{
		width: 5px;
		height: 5px;
		margin-top: 8px;
		margin-right: 8px;
		background: #E24555;
		border-radius: 50%;
	}
	.homeCentet_three_right_div_three_right{
		// flex: 1;
		width: calc(100% - 15px);
	}
	.homeCentet_three_right_div_three_time{
		margin-top: 8px;
		font-family: PingFangSC, PingFang SC;
		font-weight: 400;
		font-size: 12px;
		color: #777777;
		line-height: 16px;
	}

	.homeCentet_three_right_div_three>div {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.home_hot{
		width: 100%;
		height: 584px;
		margin-top: 36px;
		background: url('https://huigupaimai.oss-cn-beijing.aliyuncs.com/uploads/images/hot_back.png') no-repeat;
		background-size: 1920px ;
	}
	.home_hot_cont{
		width: 1200px;
		margin: 0 auto;
		padding-top: 1px;
	}
	.home_hot_item{
		display: flex;
		align-items: center;
		width: 100%;
		margin-top: 64px;
	}
	.home_hot_item_statistics{
		color: #fff;
	}
	.home_hot_item_statistics_num{
		font-size: 22px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255,255,255,0.9);
		line-height: 34px;
	}
	.home_hot_item_statistics_num span{
		font-size: 60px;
		line-height: 60px;
	}
	.home_hot_item_statistics_more{
		position: relative;
		z-index: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 206px;
		height: 64px;
		margin-top: 95px;
		margin-right: 98px;
		background: #CD0016;
		border-radius: 12px;
		font-size: 20px;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #fff;
		line-height: 28px;
		cursor: pointer;
		overflow: hidden;
		transition: all .25s ease-in;
	}
	.home_hot_item_statistics_more img{
		display: block;
		width: 22px;
		height: 22px;
		margin-left: 16px;
		// background: #fff;
		border-radius: 20px;
		transition: all .1s ease-in;
	}
	.home_hot_item_statistics_circle{
		position: absolute;
		z-index: -1;
		right: 24px;
		width: 22px;
		height: 22px;
		border-radius: 22px;
		background: #03A081;
		transition: all .25s ease-in;
	}
	// .home_hot_item_statistics_more:hover{
	// 	color: #fff;
	// }
	// .home_hot_item_statistics_more:hover .home_hot_item_statistics_circle{
	// 	transform: scale(20);
	// }
	// .home_hot_item_statistics_more:hover img{
	// 	transform: translateX(80px);
	// 	width: 0px;
	// 	margin: 0;
	// 	background: unset;
	// }
	// .home_hot_item_statistics_more:hover{
	// 	text-align: center;
	// }


	.homeCentet_four_centet {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		width: 896px;
	}
	.homeCentet_four_centet1 {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		width: 1200px;
		margin: 0 auto;
	}
	.homeCentet_four_centet_item{
		position: relative;
		width: 288px;
		height: 350px;
		background: #FFFFFF;
		border-radius: 8px;
		transition: all .2s ease-in;
		cursor: pointer;
		overflow: hidden;
		box-sizing: border-box;
	}
	.homeCentet_four_centet_item:hover{
		transform: translateY(-8px);
		box-shadow: 0px 2px 12px rgba(0,0,0,.15);
	}
	.homeCentet_four .homeCentet_four_centet_item{
		border: 1px solid #f1f1f1;
	}

	.homeCentet_four_centet_view {
		width: 285px;
		height: 374px;
		background: #FFFFFF;
		position: relative;
		margin-bottom: 18px;
	}

	.homeCentet_four_centet_imgview {
		display: flex;
		align-items: center;
		position: relative;
		width: 100%;
		height: 198px;
		border-radius: 8px 8px 0px 0px;
		overflow: hidden;
		background: #e5e5e5;
		img{
			width: 100%;
		}
	}

	.homeCentet_four_centet_views {
		margin-left: 20px;
	}

	.homeCentet_four_centet_view>img {
		width: 285px;

	}

	.homeCentet_four_centet_view_top{
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 18px 15px 15px;
		box-sizing: border-box;
	}
	.homeCentet_four_centet_view_title{
		flex: 1;
		margin-right: 5px;
		font-size: 16px;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #333333;
		line-height: 16px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
	.homeCentet_four_centet_view_num{
		display: flex;
		align-items: center;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 12px;
	}
	.homeCentet_four_centet_view_num span{
		font-size: 18px;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #333333;
		line-height: 18px;
	}
	.homeCentet_four_centet_view_num  img{
		display: block;
		width: 10px;
		height: 10px;
		margin-right: 2px;
	}
	.homeCentet_four_centet_view_price{
		position: relative;
		z-index: 0;
		display: flex;
		align-items: center;
		width: 265px;
		margin: 0 auto;
	}
	.homeCentet_four_centet_view_price_left{
		width: 176px;
		height: 30px;
		margin-left: -11px;
		padding-left: 15px;
		background: linear-gradient(90deg, #FFFFFF 1%, #03A081 100%);
		border-radius: 0px 15px 15px 0px;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 30px;
		box-sizing: border-box;
	}
	.homeCentet_four_centet_view_price_left1{
		background: linear-gradient(90deg, #FFFFFF 3%, #D6363B 100%);
	}
	.homeCentet_four_centet_view_price_left2{
		color: #fff;
		background: linear-gradient(90deg, #FB6952 3%, #CC120D 100%);
	}
	.homeCentet_four_centet_view_price_right{
		position: absolute;
		right: 0;
		top: 0;
		z-index: -1;
		width: 187px;
		height: 30px;
		padding-right: 28px;
		background: linear-gradient(90deg, #FFF6F7 3%, #FFC78F 100%);
		border-radius: 0px 15px 15px 0px;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 600;
		text-align: right;
		color: #D6363B;
		line-height: 30px;
		box-sizing: border-box;
		cursor: pointer;
	}
	.homeCentet_four_centet_view_line{
		width: 266px;
		height: 1px;
		margin: 20px auto 17px;
		background: #E9E9E9;
	}
	.homeCentet_four_centet1 .homeCentet_four_centet_item{
		margin-right: 16px;
		margin-bottom: 20px;
	}
	.homeCentet_four_centet1 .homeCentet_four_centet_item:nth-child(4n){
		margin-right: 0px;
	}
	.homeCentet_four_centet_view_ti {
		width: 260px;
		height: 37px;
		font-size: 14px;
		font-weight: 400;
		color: #333333;
		text-overflow: -o-ellipsis-lastline;
		overflow: hidden;
		text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 2;
		-webkit-box-orient: vertical;
		margin: 11px auto 11px;
	}

	.homeCentet_four_centet_view_two {
		width: 255px;
		height: 30px;
		margin-left: 10px;
		display: flex;
		-js-display: flex;
		align-items: center;
	}

	.homeCentet_four_centet_view_two_left {
		width: 194px;
		height: 30px;
		position: relative;
	}

	.homeCentet_four_centet_view_two_left img {
		width: 194px;
		height: 30px;
	}

	.homeCentet_four_centet_view_two_left div {
		width: 194px;
		height: 30px;
		position: absolute;
		left: 0;
		top: 0;
		font-size: 12px;
		font-weight: bold;
		color: #FFFFFF;
		/* text-align: center; */
		padding: 0 8px;
		box-sizing: border-box;
	}

	.homeCentet_four_centet_view_two_left>div span {
		height: 15px;
		font-size: 20px;
		font-weight: bold;
		color: #FEFEFE;
		position: relative;
		top: 2px;
	}

	.homeCentet_four_centet_view_two_right {
		width: 81px !important;
		height: 30px !important;
		position: relative;
		left: -12px;
	}

	.homeCentet_four_centet_view_three {
		margin-left: 16px;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: #666666;
		line-height: 14px;
	}

	.homeCentet_four_centet_view_three span {
		color: #333;
		font-size: 14px;
		font-weight: 600;
	}

	.homeCentet_four_centet_view_enter {
		width: 100%;
		height: 38px;
		position: absolute;
		bottom: 0;
		left: 0;
		border-top: 1px solid #EEEEEE;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 10px;
		box-sizing: border-box;
	}

	.homeCentet_four_centet_view_enter>div:nth-child(1) {
		display: flex;
		-js-display: flex;
		align-items: center;
		font-size: 12px;
		font-weight: 400;
		color: #7A7A7A;
	}

	.homeCentet_four_centet_view_enter>div:nth-child(1) img {
		width: 11px;
		height: 16px;
		margin-right: 10px;
	}

	.homeCentet_four_centet_item_type{
		position: absolute;
		left: 0;
		top: 13px;
	}

	.homeCentet_four_centet_view_enters {
		width: 80px;
		height: 30px;
		background: #03A081;
		border-radius: 0px 15px 15px 0px;
		font-size: 14px;
		font-family: PingFangSC-Medium, PingFang SC;
		font-weight: 500;
		color: #FFFFFF;
		line-height: 30px;
		text-align: center;
	}

	.homeCentet_four_centet_view_enterss {
		background: #A80012;
	}

	.homeCentet_four_centet_view_entersss {
		background: #BFBFBF;
	}

	.homeCentet_four_centets {
		display: flex;
		-js-display: flex;
		align-items: center;
		flex-wrap: wrap;
		width: 1040px;
		margin: 0 auto;
	}

	.homeCentet_four_centetsQi {
		width: 190px;
		// height: 100px;
		margin-right: 20px;
		margin-bottom: 20px;
	}
	.homeCentet_four_centetsQi:nth-child(5n){
		margin-right: 0;
	}

	.homeCentet_four_centets img {
		width: 100%;
		// height: 100px;
	}
</style>

