<template>
	<div class="min_wrapper_1500 " style="background-color: #FFFFFF;">
		<Headers />
		<div class="About_title">
			<div class="About_title_one">请选择您的企业类型:</div>
			<div class="info">
				<!-- <div class="info_div" @click="goDetial(1)">
					<img src="../assets/one.png">
					<div class="info_div_vie">
						<div>委托企业入驻 </div>
						<div>一站式服务 帮助企业资产保值增值 高效变现</div>
					</div>
				</div> -->
				<div class="info_div" @click="goDetial(2)">
					<img src="../assets/two.png">
					<div class="info_div_vie">
						<div>拍卖公司入驻 </div>
						<div>与百余家国企、央企、事业单位达成合作 资源更充足</div>
					</div>
				</div>
				<!-- <div class="info_div" @click="goDetial(3)">
					<img src="../assets/three.png">
					<div class="info_div_vie">
						<div>租购企业入驻 </div>
						<div>千万吨全品类多规格材料模板资源 供应无忧</div>
					</div>
				</div> -->
			</div>
		</div>
		<FootersBottom />
		<homeRight />
	</div>
</template>

<script>
	import Headers from '@/components/Headers.vue'
	import FootersBottom from '@/components/FootersBottom.vue'
	import homeRight from '@/components/homeRight.vue'
	export default {
		name: 'about',
		components: {
			Headers,
			FootersBottom,
			homeRight
		},
		data() {
			return {
				info: {
					qiyemingcheng: '',
					name: '',
					mobie: '',
					yzcode: '',
					qy_status: 0
				},
				weituo: '委托企业'
			}
		},
		methods: {
			goDetial(type) {
				this.$router.push({ //核心语句
					path: '/Qiyesub', //跳转的路径
					query: { //路由传参时push和query搭配使用 ，作用时传递参数
						typeIndex: type
					}
				})
			}
		}
	}
</script>

<style scoped="scoped">
	.About_title {
		min-height: 1000px;
		width: 1200px;
		margin: 0 auto;
		padding-top: 50px;
		box-sizing: border-box;
	}

	.About_title_one {
		font-size: 24px;
		font-weight: bold;
		color: #333333;
	}

	.info {
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
		margin-top: 140px;
	}

	.info_div {
		width: 440px;
		height: 168px;
		background: #FFFFFF;
		border: 1px solid #DDDDDD;
		border-radius: 10px;
		display: flex;
		-js-display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 10px;
	}

	.info_div_vie {
		width: 244px;
		height: 80px;
		margin-left: 30px;
		font-size: 22px;
		font-weight: 400;
		color: #333333;
		display: flex;
		flex-direction: column;
		-js-display: flex;
		justify-content: space-between;
	}

	.info_div_vie>div:nth-child(2) {
		font-size: 16px;
		font-weight: 400;
		color: #999999;
	}
</style>
