import './assets/main.css'
import 'animate.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
// ElementPlus 由 unplugin-element-plus 自动按需引入，无需手动配置

// 引入head
import {createHead} from '@vueuse/head'

// 导入SVG图标
import 'virtual:svg-icons-register'

import App from './App.vue'
import router from './router'
// 延迟加载组件
import '@/assets/font/font.css'

// 导入用户状态管理
import { useUserStore } from './stores/user'
import { useFreedomUserStore } from './stores/freedomUser'

// 延迟加载非关键工具（减少主包大小）

const app = createApp(App)

// 创建head实例
const head = createHead()

app.use(createPinia())
app.use(router)
// ElementPlus 组件由 unplugin-element-plus 自动注册，无需手动注册
// 异步加载组件，避免循环依赖
setTimeout(async () => {
  const { default: components } = await import('./components')
  app.use(components)
}, 0)
app.use(head)

// 延迟初始化非关键功能，减少主包大小
setTimeout(async () => {
  const { initCriticalResourcePreload } = await import('./utils/resourcePreloader')
  const { initImagePreload } = await import('./utils/imagePreloader')
  const { initPerformanceMonitor } = await import('./utils/performanceMonitor')

  initCriticalResourcePreload()
  initImagePreload()
  initPerformanceMonitor()
}, 100)

// 初始化用户状态
const userStore = useUserStore()
const freedomUserStore = useFreedomUserStore()

// 从localStorage恢复用户登录状态
userStore.initUserState()
freedomUserStore.initFreedomUserState()

app.mount('#app')
