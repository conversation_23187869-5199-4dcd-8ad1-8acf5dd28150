import{e,b as a}from"./element-plus-BiAL0NdQ.js";import{d as s,r as l,c as n,s as t,a as c,b as o,K as i,I as u,ac as d,G as r,F as p,a1 as v,D as m,O as h,P as f,$ as k,o as x}from"./vue-vendor-D6tHD5lA.js";import{S as _,_ as C}from"./index-Bsdr07Jh.js";const b={class:"service-support"},y={class:"search-section"},N={class:"search-content"},S={class:"search-title"},q={class:"search-box"},g={class:"content-section"},I={class:"content-container"},w={class:"navigation"},F=["onClick"],G={class:"nav-text"},j={class:"question-list"},H={class:"question-scroll-area"},Q=["onClick"],V={class:"question-title"},z={class:"question-answer"},K={class:"answer-content"},D={class:"helpful-section"},O={class:"helpful-buttons"},P=C(s({__name:"ServiceSupport",props:{title:{default:"嗨！有什么需要帮忙的吗？"},placeholder:{default:"搜索关键词"},navigationItems:{},onSearch:{},onGetQuestions:{},onHelpfulFeedback:{}},setup(s){const C=s,P=l(""),U=l(0),$=l([]),A=l(!1),B=n(()=>$.value[U.value]||[]),E=()=>{C.onSearch&&P.value.trim()&&C.onSearch(P.value.trim())},J=async e=>{if(U.value=e,!$.value[e]&&C.onGetQuestions){A.value=!0;try{const a=(await C.onGetQuestions(e)).map(e=>({...e,expanded:!1}));$.value[e]=a}catch(a){$.value[e]=[]}finally{A.value=!1}}},L=(e,a)=>{C.onHelpfulFeedback&&C.onHelpfulFeedback(e,a)};return t(()=>{C.navigationItems.length>0&&J(0)}),(s,l)=>{const n=a,t=e;return x(),c("div",b,[o("div",y,[o("div",N,[o("p",S,i(s.title||"嗨！有什么需要帮忙的吗？"),1),o("div",q,[u(t,{modelValue:P.value,"onUpdate:modelValue":l[0]||(l[0]=e=>P.value=e),placeholder:s.placeholder||"搜索关键词",class:"search-input",onKeyup:d(E,["enter"])},{suffix:r(()=>[u(n,{class:"search-btn",onClick:E},{default:r(()=>[u(_,{iconName:"support-search",className:"search-icon"})]),_:1})]),_:1},8,["modelValue","placeholder"])])])]),o("div",g,[o("div",I,[o("div",w,[(x(!0),c(p,null,v(s.navigationItems,(e,a)=>(x(),c("div",{class:m(["nav-item",{active:U.value===a}]),key:a,onClick:e=>J(a)},[u(_,{iconName:e.icon,className:"nav-icon"},null,8,["iconName"]),o("span",G,i(e.text),1)],10,F))),128))]),o("div",j,[o("div",H,[(x(!0),c(p,null,v(B.value,(e,a)=>(x(),c("div",{class:"question-item",key:a},[o("div",{class:"question-header",onClick:e=>(e=>{const a=$.value[U.value];a&&a[e]&&(a[e].expanded=!a[e].expanded)})(a)},[u(_,{iconName:e.expanded?"support-close":"support-open",className:"expand-icon"},null,8,["iconName"]),o("span",V,i(e.title),1)],8,Q),h(o("div",z,[o("div",K,i(e.answer),1),o("div",D,[l[3]||(l[3]=o("span",{class:"helpful-text"},"对您是否有帮助？",-1)),o("div",O,[u(n,{size:"small",type:"primary",plain:"",onClick:e=>L(a,!0)},{default:r(()=>l[1]||(l[1]=[k(" 是 ")])),_:2,__:[1]},1032,["onClick"]),u(n,{size:"small",plain:"",onClick:e=>L(a,!1)},{default:r(()=>l[2]||(l[2]=[k(" 否 ")])),_:2,__:[2]},1032,["onClick"])])])],512),[[f,e.expanded]])]))),128))])])])])])}}}),[["__scopeId","data-v-41ca98ef"]]);export{P as S};
