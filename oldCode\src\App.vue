<template>
	<div id="app">
		<router-view :key="randomId" />
	</div>
</template>
<script>
	export default {
		name: '',
		computed: {
			randomId() {
				return this.$route.fullPath + new Date()
			}
		},
		created() {
			var userAgent = navigator.userAgent // 获取浏览器信息
			var IEReg = new RegExp('MSIE \\d+\\.\\d+;'); // 正则校验IE标识符
			var IEMsg = IEReg.exec(userAgent) // 获取字符串
			var IEVersionNum = new RegExp('\\d+\\.\\d'); // 正则获取版本

			// 是IE9，引入js文件
			if (IEMsg && parseFloat(IEVersionNum.exec(IEMsg)) == 9) {
				import('@/assets/js/flex.native.min.js')
				console.log('IE9引入')
			} else {
				console.log('非IE9不引入')
			}

			var _hmt = _hmt || [];
			var hm = document.createElement("script");
			hm.src = "https://hm.baidu.com/hm.js?cee52f5837b2c725477f804bec9624b4";
			var s = document.getElementsByTagName("script")[0];
			s.parentNode.insertBefore(hm, s);
			console.log(window.parent.location,'referer')
			console.log(window.opener,'referer',window)
		},
	}
</script>

<style lang="stylus">
	body{
		margin: 0
	}
	.min_wrapper_1500 {
		min-width: 1500px;
	}
</style>
