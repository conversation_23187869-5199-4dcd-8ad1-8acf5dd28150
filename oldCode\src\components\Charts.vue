<template>
  <div id="gangjia" :class="className" :style="{height:height,width:width}" />
</template>

<script>
  import * as echarts from 'echarts'
  // import resize from './mixins/resize'

  export default {
    // mixins: [resize],
    props: {
      className: {
        type: String,
        default: 'chart'
      },
      width: {
        type: String,
        default: '200px'
      },
      height: {
        type: String,
        default: '200px'
      },
    },
    data() {
      return {
        chart: null
      }
    },
    mounted() {
      // this.initChart()
    },
    beforeDestroy() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
    methods: {
      initChart(one, two) {
        // console.log(one,two,'one,two')
        this.chart = echarts.init(document.getElementById('gangjia'))
        this.chart.setOption({
          backgroundColor: 'rgba(128, 128, 128, 0)',
          tooltip: {
            trigger: 'axis',
          },
          grid: {
            left: '5%',
            right: '2%',
            borderWidth: 0,
            top: 30,
            bottom: 20,
          },
          calculable: true,
          xAxis: [{
            type: 'category',
            boundaryGap:false,
            data: one
          }],
          yAxis: [{
            type: 'value',
            axisLine:{
              show:false,
            },
            splitLine: {
              show: true,
            },
            max:function(value){
              return value.max + 10
            },
            min:function(value){
              return value.min -10
            }
          }],
          series: [{
            name: '均价',
            type: 'line',
            stack: 'total',
            symbolSize: 10,
            symbol: 'circle',
            smooth: true,
            areaStyle: {
              color:{
                type:'linear',
                x:0,
                y:0,
                x2:0,
                y2:1,
                colorStops:[
                  {
                    offset:0,
                    color:'rgba(255, 90, 124,.3)'
                  },
                  {
                    offset:1,
                    color:'rgba(255, 90, 124,0)'
                  }
                ]
              }
            },
            itemStyle: {
              normal: {
                color: '#FF5A7C',
                barBorderRadius: 2,
                // label: {
                //   show: true,
                //   position: 'top',
                //   formatter(p) {
                //     return p.value > 0 ? p.value : ''
                //   }
                // }
              }
            },
            data: two
          }]
        })
      }
    }
  }
</script>
