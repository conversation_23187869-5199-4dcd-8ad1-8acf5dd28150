import{d as s,a as o,b as t,I as e,a7 as r,o as a}from"./vue-vendor-D6tHD5lA.js";import{_ as n}from"./index-Bsdr07Jh.js";import"./app-assets-OmEvQhWx.js";/* empty css                         */import"./pinia-DkXT_Xot.js";import"./vueuse-vendor-CTlOff0I.js";import"./vendor-others-DLVEv83C.js";import"./vue-router-D0b9rEnV.js";import"./utils-common-PdkFOSu3.js";import"./utils-http-DfEnKTfr.js";import"./http-vendor-ztdpVPaQ.js";import"./element-plus-BiAL0NdQ.js";import"./element-icons-Cfq32zG_.js";import"./vendor-lodash-D0OfQ6x6.js";import"./crypto-vendor-CkZKNqwc.js";import"./app-stores-CLUCXxRF.js";const i={class:"announcement"},m={class:"announcement-main"},p=n(s({__name:"index",setup:s=>(s,n)=>{const p=r("router-view");return a(),o("div",i,[t("main",m,[e(p)])])}}),[["__scopeId","data-v-de758aa8"]]);export{p as default};
