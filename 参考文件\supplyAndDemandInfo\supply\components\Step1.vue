<template>
  <div class="step-panel">
    <!-- 使用Ant Design表单布局，通过样式调整为左右布局 -->
    <a-form ref="formRef" :model="formData" :rules="formRules" :validate-trigger="['change', 'blur']" :scroll-to-first-error="true">
      <!-- 供应物资类型 -->
      <div class="form-section">
        <h3 class="section-title">供应物资类型</h3>
        <div class="form-row">
          <a-form-item name="materialType" class="material-type-item">
            <a-cascader
              v-model:value="formData.materialType"
              :options="materialTypeOptions"
              :load-data="loadMaterialTypeData"
              placeholder="请选择物资类型"
              size="large"
              show-search
              :filter-option="filterMaterialType"
              :field-names="{ label: 'name', value: 'id', children: 'children' }"
              change-on-select
              class="material-type-cascader"
            />
          </a-form-item>
        </div>
      </div>

      <!-- 基本供应信息 -->
      <div class="form-section">
        <h3 class="section-title">基本供应信息</h3>

        <!-- 信息标题独占一行 -->
        <div class="form-row">
          <a-form-item label="信息标题" :name="['basicInfo', 'infoTitle']" required class="info-title-item">
            <a-input v-model:value="formData.basicInfo.infoTitle" placeholder="格式建议:地区+供应+设备名称" size="large" />
          </a-form-item>
        </div>

        <!-- 物资品牌、物资型号、新旧程度三项在一行 -->
        <div class="form-row basic-three-row">
          <a-form-item label="物资品牌" :name="['basicInfo', 'brand']" class="basic-three-item">
            <a-input v-model:value="formData.basicInfo.brand" placeholder="请输入物资品牌" size="large" />
          </a-form-item>
          <a-form-item label="物资型号" :name="['basicInfo', 'model']" class="basic-three-item">
            <a-input v-model:value="formData.basicInfo.model" placeholder="请输入物资型号" size="large" />
          </a-form-item>
          <a-form-item label="新旧程度" :name="['basicInfo', 'depreciationDegree']" required class="basic-three-item">
            <a-select v-model:value="formData.basicInfo.depreciationDegree" placeholder="请选择新旧程度" size="large">
              <a-select-option :value="1">一成新</a-select-option>
              <a-select-option :value="2">二成新</a-select-option>
              <a-select-option :value="3">三成新</a-select-option>
              <a-select-option :value="4">四成新</a-select-option>
              <a-select-option :value="5">五成新</a-select-option>
              <a-select-option :value="6">六成新</a-select-option>
              <a-select-option :value="7">七成新</a-select-option>
              <a-select-option :value="8">八成新</a-select-option>
              <a-select-option :value="9">九成新</a-select-option>
            </a-select>
          </a-form-item>
        </div>
      </div>

      <!-- 规格与存放 -->
      <div class="form-section">
        <h3 class="section-title">规格与存放</h3>

        <!-- 省市区选择框第一行 -->
        <div class="form-row location-row">
          <div class="location-selects">
            <a-form-item :name="['specification', 'province']" class="location-area-item">
              <JAreaSelect
                v-model:province="formData.specification.province"
                v-model:city="formData.specification.city"
                v-model:area="formData.specification.area"
                placeholder="请选择省市区"
                :level="3"
              />
            </a-form-item>
          </div>
        </div>

        <!-- 其他项第二行 -->
        <div class="form-row specification-second-row">
          <!-- 存放方式 -->
          <a-form-item label="存放方式" :name="['specification', 'storageMethod']" required class="specification-item">
            <a-select v-model:value="formData.specification.storageMethod" placeholder="请选择存放方式" size="large">
              <a-select-option :value="1">仓库</a-select-option>
              <a-select-option :value="2">露天堆放</a-select-option>
            </a-select>
          </a-form-item>

          <!-- 物资数量 -->
          <a-form-item label="物资数量" :name="['specification', 'quantity']" required class="specification-item">
            <a-input-number v-model:value="formData.specification.quantity" placeholder="请输入数量" style="width: 100%" size="large" :min="1" />
          </a-form-item>

          <!-- 物资单位 -->
          <a-form-item label="物资单位" :name="['specification', 'unit']" required class="specification-item">
            <a-select v-model:value="formData.specification.unit" placeholder="请选择单位" size="large">
              <a-select-option value="台">台</a-select-option>
              <a-select-option value="辆">辆</a-select-option>
              <a-select-option value="套">套</a-select-option>
              <a-select-option value="个">个</a-select-option>
              <a-select-option value="件">件</a-select-option>
              <a-select-option value="批">批</a-select-option>
              <a-select-option value="米">米</a-select-option>
              <a-select-option value="吨">吨</a-select-option>
              <a-select-option value="公斤">公斤</a-select-option>
            </a-select>
          </a-form-item>

          <!-- 物资价格 -->
          <a-form-item label="物资价格" :name="['specification', 'price']" required class="specification-item">
            <a-input-number
              v-model:value="formData.specification.price"
              placeholder="请输入价格"
              style="width: 100%"
              size="large"
              :min="0"
              :precision="2"
              :formatter="(value: any) => `￥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')"
              :parser="(value: any) => value.replace(/￥\s?|(,*)/g, '')"
            />
          </a-form-item>
        </div>
      </div>

      <!-- 亮点与展示 -->
      <div class="form-section">
        <h3 class="section-title">亮点与展示</h3>

        <!-- 物资照片上传 -->
        <div class="form-row">
          <a-form-item label="物资照片" :name="['display', 'images']" class="upload-item">
            <div class="upload-container">
              <JUpload
                v-model:value="formData.display.images"
                :multiple="true"
                :max-count="15"
                accept="image/*"
                list-type="picture-card"
                file-type="image"
                :return-url="false"
                class="upload-component upload-normal"
              />
              <div class="upload-tip">建议尺寸800*800像素，最多上传15张</div>
            </div>
          </a-form-item>
        </div>

        <!-- 物资视频上传 -->
        <div class="form-row">
          <a-form-item label="物资视频" :name="['display', 'videos']" class="upload-item">
            <div class="upload-container">
              <JUpload
                v-model:value="formData.display.videos"
                :multiple="false"
                :max-count="1"
                accept="video/*"
                :return-url="false"
                class="upload-component upload-video"
              />
              <div class="upload-tip">建议视频宽高比16:9，突出商品核心卖点，时长9~30秒</div>
            </div>
          </a-form-item>
        </div>

        <!-- 供应亮点 -->
        <div class="form-row">
          <a-form-item label="供应亮点" :name="['display', 'highlights']" class="highlights-item">
            <a-input v-model:value="formData.display.highlights" placeholder="请输入供应亮点" size="large" />
          </a-form-item>
        </div>

        <!-- 物资详细描述 -->
        <div class="form-row">
          <a-form-item label="物资详细描述" :name="['display', 'materialDesc']" class="material-desc-item">
            <div class="rich-text-container">
              <JEditorTiptap v-model:value="formData.display.materialDesc" placeholder="请详细描述物资信息..." :auto-focus="false" />
            </div>
          </a-form-item>
        </div>
      </div>
    </a-form>
  </div>
</template>

<script setup lang="ts">
  import { computed, ref, onMounted } from 'vue';
  import type { FormInstance } from 'ant-design-vue';
  import { JAreaSelect, JUpload, JEditorTiptap } from '/@/components/Form';
  import { getMaterialTree } from '@/api/supplyAndDemand/SupplyDemand';
  import type { MaterialTypeNode } from '@/api/supplyAndDemand/SupplyDemand';

  // 定义 Props
  interface Props {
    modelValue: {
      materialType: string;
      basicInfo: {
        infoTitle: string;
        brand: string;
        model: string;
        depreciationDegree: number;
      };
      specification: {
        province: string;
        city: string;
        area: string;
        storageMethod: number;
        quantity: string;
        unit: string;
        price: string;
      };
      display: {
        images: string;
        videos: string;
        highlights: string;
        materialDesc: string;
      };
      attachmentList: any[];
    };
    locationLoading?: boolean;
    isEditMode?: boolean;
  }

  const props = withDefaults(defineProps<Props>(), {
    locationLoading: false,
    isEditMode: false,
  });

  // 定义 Emits
  interface Emits {
    (e: 'update:modelValue', value: Props['modelValue']): void;
    (e: 'area-change', value: any): void;
    (e: 'get-current-location'): void;
  }

  const emit = defineEmits<Emits>();

  // 表单引用
  const formRef = ref<FormInstance>();

  // 物资类型选项
  const materialTypeOptions = ref<MaterialTypeNode[]>([]);

  // 计算属性：表单数据
  const formData = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });

  // 表单校验规则
  const formRules = {
    materialType: [{ required: true, message: '请选择物资类型', trigger: 'change' }],
    basicInfo: {
      infoTitle: [
        { required: true, message: '请输入信息标题', trigger: 'blur' },
        { min: 2, max: 100, message: '信息标题长度应在2-100个字符之间', trigger: 'blur' },
      ],
      depreciationDegree: [{ required: true, message: '请选择新旧程度', trigger: 'change' }],
    },
    specification: {
      province: [{ required: true, message: '请选择省份', trigger: 'change' }],
      storageMethod: [{ required: true, message: '请选择存放方式', trigger: 'change' }],
      quantity: [{ required: true, message: '请输入物资数量', trigger: 'blur' }],
      unit: [{ required: true, message: '请选择物资单位', trigger: 'change' }],
      price: [{ required: true, message: '请输入物资价格', trigger: 'blur' }],
    },
  };

  // 获取物资类型树形数据
  const fetchMaterialTypeTree = async () => {
    try {
      const result = await getMaterialTree();
      console.log('获取物资类型树形数据:', result);
      if (result && Array.isArray(result)) {
        materialTypeOptions.value = result;
      }
    } catch (error) {
      console.error('获取物资类型失败:', error);
    }
  };

  // 懒加载物资类型数据
  const loadMaterialTypeData = async (selectedOptions: any[]) => {
    const targetOption = selectedOptions[selectedOptions.length - 1];
    targetOption.loading = true;

    try {
      // 这里可以根据需要实现懒加载逻辑
      // 目前先使用已加载的数据
      targetOption.loading = false;
    } catch (error) {
      console.error('加载子级物资类型失败:', error);
      targetOption.loading = false;
    }
  };

  // 物资类型搜索过滤
  const filterMaterialType = (inputValue: string, path: any[]) => {
    return path.some((option) => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
  };

  // 表单验证方法
  const validateForm = async (): Promise<boolean> => {
    try {
      await formRef.value?.validate();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      // 滚动到第一个错误字段
      const firstErrorField = document.querySelector('.ant-form-item-has-error');
      if (firstErrorField) {
        firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
      return false;
    }
  };

  // 清除表单验证
  const clearValidate = () => {
    formRef.value?.clearValidate();
  };

  // 组件挂载时获取物资类型数据
  onMounted(() => {
    fetchMaterialTypeTree();
  });

  // 暴露方法给父组件
  defineExpose({
    validateForm,
    clearValidate,
  });
</script>

<style lang="less" scoped>
  .step-panel {
    background: #fff;
    border-radius: 8px;
  }

  .form-section {
    margin-bottom: 32px;
  }

  .section-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #262626;
    margin-bottom: 16px;
    padding-bottom: 8px;
    &::before {
      content: '';
      display: block;
      width: 4px;
      height: 18px;
      margin-right: 8px;
      background-color: #004c66;
    }
  }

  /* 表单行布局 */
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 16px;
    gap: 20px;
  }

  /* 物资类型选择 - 只占三分之一 */
  .material-type-item {
    flex: 0 0 calc(33.333% - 14px);
    min-width: 300px;
  }

  /* 信息标题 - 占满一行 */
  .info-title-item {
    width: 100%;
  }

  /* 基本信息三项在一行 */
  .basic-three-row {
    gap: 20px;
  }

  .basic-three-item {
    flex: 1;
    min-width: calc(33.333% - 14px);
  }

  /* 省市区选择行 */
  .location-row {
    gap: 20px;
    align-items: flex-start;
  }

  /* 省市区选择区域 */
  .location-selects {
    flex: 1;
    margin-right: 20px;
  }

  .location-area-item {
    margin-bottom: 0;

    :deep(.area-select) {
      display: flex;
      gap: 10px;

      .ant-select {
        flex: 1;
        height: 40px;
        border-radius: 6px;
        font-size: 14px;

        &:hover {
          border-color: #40a9ff;
        }

        &:focus,
        &.ant-select-focused {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        // 设置选择框文字居中
        .ant-select-selector {
          display: flex;
          align-items: center;

          .ant-select-selection-item {
            text-align: center;
            width: 100%;
            display: flex;
            align-items: center;
          }

          .ant-select-selection-placeholder {
            text-align: center;
            width: 100%;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }

  /* 规格第二行 */
  .specification-second-row {
    gap: 20px;
  }

  .specification-item {
    flex: 1;
    min-width: calc(25% - 15px);
  }

  /* 表单项占满一行 */
  .form-item-full {
    width: 100%;
  }

  /* 资料上传 - 每一项都独占一行 */
  .form-section:last-child .form-row {
    width: 100%;

    .ant-form-item {
      width: 100%;
    }
  }

  .upload-component {
    cursor: pointer;
  }

  /* 上传容器布局 - 上传组件和提示文字横向排列 */
  .upload-container {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    width: 100%;
  }

  .upload-tip {
    margin-top: 8px;
    color: #999;
    font-size: 12px;
    flex: 1;
  }

  .upload-item {
    width: 100%;
  }

  /* 自定义上传组件样式 - 普通上传按钮（100px*100px） */
  .upload-normal {
    flex-shrink: 0; /* 不缩小 */

    :deep(.ant-upload-select) {
      /* 设置上传区域尺寸和样式 */
      width: 100px !important;
      height: 100px !important;
      background-color: #f2f2f2 !important; /* 背景色 */
      border: 1px solid #ddd !important; /* 边框色，实线 */
      border-radius: 4px !important;
      position: relative !important;
      overflow: hidden !important;

      /* 清除所有可能的默认样式 */
      &::before,
      &::after {
        display: none !important;
      }

      .ant-upload {
        width: 100% !important;
        height: 100% !important;
        background-color: #f2f2f2 !important; /* 背景色 */
        border: none !important;
        border-radius: 4px !important;
        position: relative !important;
        overflow: hidden !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;

        /* 清除所有可能的默认样式和伪元素 */
        &::before,
        &::after {
          display: none !important;
        }

        /* 隐藏默认的图标和文字 */
        .anticon,
        span,
        .ant-upload-text,
        .ant-upload-hint,
        * {
          display: none !important;
        }

        /* 自定义加号样式 */
        &::after {
          content: '+' !important;
          width: 22px !important;
          height: 21px !important;
          font-size: 18px !important; /* 加号字体大小 */
          color: #ddd !important; /* 加号颜色 */
          font-weight: 300 !important;
          position: absolute !important;
          top: 50% !important;
          left: 50% !important;
          transform: translate(-50%, -50%) !important;
          z-index: 10 !important;
          pointer-events: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          background: transparent !important;
          border: none !important;
          line-height: 1 !important;
        }
      }

      /* 悬停效果 */
      &:hover {
        background-color: #e8e8e8 !important; /* 悬停时背景色稍微变深 */
        border-color: #bbb !important; /* 悬停时边框色稍微变深 */

        .ant-upload {
          background-color: #e8e8e8 !important;

          &::after {
            color: #004c66 !important; /* 悬停时加号颜色变为主题色 */
          }
        }
      }
    }
  }

  /* 视频上传样式 - 视频上传按钮（178px*100px） */
  .upload-video {
    flex-shrink: 0; /* 不缩小 */

    :deep(.ant-upload-select) {
      /* 设置视频上传区域尺寸和样式 */
      width: 178px !important;
      height: 100px !important;
      background-color: #f2f2f2 !important; /* 背景色 */
      border: 1px solid #ddd !important; /* 边框色，实线 */
      border-radius: 4px !important;
      position: relative !important;
      overflow: hidden !important;

      /* 清除所有可能的默认样式 */
      &::before,
      &::after {
        display: none !important;
      }

      .ant-upload {
        width: 100% !important;
        height: 100% !important;
        background-color: #f2f2f2 !important; /* 背景色 */
        border: none !important;
        border-radius: 4px !important;
        position: relative !important;
        overflow: hidden !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;

        /* 清除所有可能的默认样式和伪元素 */
        &::before,
        &::after {
          display: none !important;
        }

        /* 隐藏默认的图标和文字 */
        .anticon,
        span,
        .ant-upload-text,
        .ant-upload-hint,
        * {
          display: none !important;
        }

        /* 自定义加号样式 */
        &::after {
          content: '+' !important;
          width: 22px !important;
          height: 21px !important;
          font-size: 18px !important; /* 加号字体大小 */
          color: #ddd !important; /* 加号颜色 */
          font-weight: 300 !important;
          position: absolute !important;
          top: 50% !important;
          left: 50% !important;
          transform: translate(-50%, -50%) !important;
          z-index: 10 !important;
          pointer-events: none !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          background: transparent !important;
          border: none !important;
          line-height: 1 !important;
        }
      }

      /* 悬停效果 */
      &:hover {
        background-color: #e8e8e8 !important; /* 悬停时背景色稍微变深 */
        border-color: #bbb !important; /* 悬停时边框色稍微变深 */

        .ant-upload {
          background-color: #e8e8e8 !important;

          &::after {
            color: #004c66 !important; /* 悬停时加号颜色变为主题色 */
          }
        }
      }
    }
  }

  .rich-text-container {
    width: 100%;
    min-height: 200px;
  }

  /* 确保label和输入框之间有10px间距 */
  :deep(.ant-form-item-label) {
    padding-right: 0 !important;
  }

  :deep(.ant-form-item-label > label) {
    margin-right: 0;
  }

  /* 统一设置所有表单项的label宽度和间距 */
  :deep(.ant-form-item) {
    margin-bottom: 16px;
    align-items: flex-start; /* 改为顶部对齐，避免校验错误影响 */

    .ant-form-item-label {
      text-align: left;
      width: auto;
      min-width: 90px;
      padding-right: 0;
      display: flex;
      align-items: center; /* label内容上下居中 */
      height: 40px; /* 固定高度，与输入框高度一致 */

      label {
        color: #666; /* label颜色改为#666 */
        font-size: 16px; /* label字体大小16px */
        font-weight: 400; /* 调整字重 */
        line-height: 1;

        &::after {
          content: '';
          margin: 0;
        }
      }
    }

    .ant-form-item-control {
      flex: 1;
      margin-left: 10px; /* label和输入框间距10px */
    }

    /* 确保输入框容器高度固定 */
    .ant-form-item-control-input {
      min-height: 40px;
    }

    /* 确保所有输入框、选择框、数字输入框保持正确大小 */
    .ant-select .ant-select-selector,
    .ant-picker {
      height: 40px !important;
      line-height: 40px !important;
    }

    .ant-input-number-input {
      height: 38px !important;
    }
  }

  /* 资料上传部分的label不需要上下居中 */
  .form-section:last-child {
    :deep(.ant-form-item) {
      align-items: flex-start !important; /* 资料上传部分label不上下居中 */

      .ant-form-item-label {
        align-items: flex-start !important; /* 资料上传部分label内容不上下居中 */
        height: auto !important; /* 取消固定高度 */
        padding-top: 0; /* 移除顶部内边距 */
      }
    }
  }

  /* 响应式布局 */
  @media (max-width: 768px) {
    .basic-three-row,
    .specification-second-row {
      flex-direction: column;
      gap: 16px;
    }

    .location-row {
      flex-direction: column;
    }

    .location-selects {
      flex-direction: column;
    }

    .material-type-item,
    .basic-three-item,
    .specification-item {
      min-width: 100%;
    }
  }
</style>
